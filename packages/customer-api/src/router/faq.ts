import { FaqPagesEnum, ProjectEnum } from "@repo/database";
import { z } from "zod";

import { createTRPCRouter, publicProcedure } from "../trpc";

const faqRouter = createTRPCRouter({
  getAllFaq: publicProcedure
    .input(
      z.object({
        project: z.nativeEnum(ProjectEnum),
        page: z.nativeEnum(FaqPagesEnum),
      }),
    )
    .query(async ({ ctx, input }) => {
      const faqs = await ctx.db.faq.findMany({
        where: {
          project: input.project,
          page: input.page,
        },
        select: {
          id: true,
          answer: true,
          question: true,
          order: true,
        },
        orderBy: {
          order: "asc",
        },
      });

      return faqs;
    }),
});

export default faqRouter;
