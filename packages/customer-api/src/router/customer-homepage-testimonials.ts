import { TRPCError } from "@trpc/server";

import { createTRPCRouter, publicProcedure } from "../trpc";

export const customerHomePageTestimonialsRouter = createTRPCRouter({
  getHomePageTestimonials: publicProcedure.query(async ({ ctx }) => {
    try {
      return await ctx.db.customerTestimonials.findMany({
        select: {
          id: true,
          name: true,
          description: true,
          fileKey: true,
          filePublicUrl: true,
          rating: true,
          city: {
            select: {
              name: true,
            },
          },
        },
      });
    } catch (err) {
      console.log("error is", err);
      throw new TRPCError({
        message: "Error in getting testimonials.",
        code: "BAD_REQUEST",
      });
    }
  }),
});
