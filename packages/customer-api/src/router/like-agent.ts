import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../trpc";

const likeAgentRouter = createTRPCRouter({
  addLikedAgents: protectedProcedure
    .input(z.object({ agentId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { agentId } = input;

      try {
        const user = await ctx.db.customer.findFirst({
          where: {
            id: ctx.session.user.id,
          },
        });

        if (!user) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Unauthorized",
          });
        }
        const isAgentLiked = await ctx.db.likedAgentsByCustomer.findFirst({
          where: {
            AND: [
              { customerId: ctx.session.user.id },
              { likedAgentId: agentId },
            ],
          },
        });
        if (isAgentLiked) {
          await ctx.db.likedAgentsByCustomer.delete({
            where: {
              id: isAgentLiked.id,
            },
          });

          return { message: "favourite agent removed successfully" };
        }
        await ctx.db.likedAgentsByCustomer.create({
          data: {
            customerId: ctx.session.user.id,
            likedAgentId: agentId,
          },
        });
        return { message: `Agent added to liked agents successfully` };
      } catch (err) {
        console.log(err);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create new liked agent",
        });
      }
    }),

  isAgentLiked: protectedProcedure
    .input(z.object({ agentId: z.string() }))
    .query(async ({ input, ctx }) => {
      const { agentId } = input;
      const like = await ctx.db.likedAgentsByCustomer.findFirst({
        where: {
          likedAgentId: agentId,
          customerId: ctx.session.user.id,
        },
      });
      return !!like;
    }),

  getFavouritesAgents: protectedProcedure.query(async ({ ctx }) => {
    try {
      const favouriteAgents = await ctx.db.likedAgentsByCustomer.findMany({
        where: {
          customerId: ctx.session.user.id,
        },
        select: {
          likedAgentId: true,
          likedAgent: {
            select: {
              name: true,
              id: true,
              createdAt: true,
              verifiedAgent: true,
              experience: true,
              propertiesSold: true,
              rating: true,
              filePublicUrl: true,
              cloudinaryProfileImageUrl: true,
            },
          },
        },
      });

      return favouriteAgents;
    } catch (err) {
      console.error("[server]: ", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch favourite agents",
      });
    }
  }),
});

export default likeAgentRouter;
