import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const agentReviewsRouter = createTRPCRouter({
  getAgentReviews: protectedProcedure
    .input(z.object({ agentId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { agentId } = input;
      return await ctx.db.customerRatingsToAgents.findMany({
        where: {
          ratedToUserId: agentId,
        },
        include: {
          ratedBy: true,
          connection: {
            select: {
              customer: {
                select: {
                  city: true,
                  cityId: true,
                },
              },
            },
          },
        },
      });
    }),
});
