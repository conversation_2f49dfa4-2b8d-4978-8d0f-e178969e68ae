import { deleteAccountFormSchema } from "@repo/validators";
import { TRPCError } from "@trpc/server";

import { createTRPCRouter, protectedProcedure } from "../trpc";

const deleteCustomerAccountRouter = createTRPCRouter({
  createDeleteCustomerAccount: protectedProcedure
    .input(deleteAccountFormSchema)
    .mutation(async ({ ctx, input }) => {
      const { email, phoneNumber, reason } = input;
      try {
        const alreadyReceivedRequest =
          await ctx.db.deleteCustomerAccount.findFirst({
            where: {
              email: email,
              phoneNumber: phoneNumber,
              deletedAt: null,
            },
          });
        if (alreadyReceivedRequest) {
          return {
            error:
              "We have already received the request for account deletion for these credentials.",
          };
        }

        // First find the user
        const user = await ctx.db.customer.findFirst({
          where: {
            email: email,
            phoneNumber: phoneNumber,
          },
        });

        if (!user) {
          return {
            error: "The account you are trying to delete doesn't exist.",
          };
        }

        await ctx.db.deleteCustomerAccount.create({
          data: {
            email,
            phoneNumber,
            reason,
          },
        });

        return {
          message: "Your request is sent for account deletion.",
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to receive the request for account deletion.",
        });
      }
    }),
});

export default deleteCustomerAccountRouter;
