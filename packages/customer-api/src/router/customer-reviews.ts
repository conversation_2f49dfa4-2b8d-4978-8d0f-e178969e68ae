import { CustomerRateAgentSchema } from "@repo/validators";
import { TRPCError } from "@trpc/server";
import { v2 as cloudinary } from "cloudinary";
import { z } from "zod";

import { env } from "../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const customerReviewsRouter = createTRPCRouter({
  getCustomerReviews: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.customerRatingsToAgents.findMany({
      select: {
        id: true,
        userStarsCount: true,
        userRatingMessage: true,
        fileKey: true,
        filePublicUrl: true,
        cloudinaryPublicId: true,
        cloudinaryUrl: true,
        ratedTo: {
          select: {
            id: true,
            name: true,
            userLocation: true,
            filePublicUrl: true,
            cloudinaryProfileImageUrl: true,
            city: {
              select: {
                name: true,
              },
            },
            company: {
              select: {
                id: true,
                companyName: true,
              },
            },
          },
        },
      },
      where: {
        ratedByUserId: ctx.session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  createCustomerReviews: protectedProcedure
    .input(
      z.object({ id: z.string(), fileKey: z.string(), fileUrl: z.string() }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const response = await ctx.db.customerRatingsToAgents.update({
          where: {
            id: input.id,
          },
          data: {
            fileKey: input.fileKey,
            filePublicUrl: input.fileUrl,
          },
        });
        return response;
      } catch (err) {
        return new TRPCError({
          message: "Error in uploading video",
          code: "BAD_REQUEST",
        });
      }
    }),

  updateCustomerReviews: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        cloudinaryPublicId: z.string(),
        cloudinaryUrl: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const response = await ctx.db.customerRatingsToAgents.update({
          where: {
            id: input.id,
          },
          data: {
            cloudinaryPublicId: input.cloudinaryPublicId,
            cloudinaryUrl: input.cloudinaryUrl,
          },
        });

        return response;
      } catch (err) {
        console.log("error is", err);
        return new TRPCError({
          message: "Error in uploading video",
          code: "BAD_REQUEST",
        });
      }
    }),

  customerRateToAgent: protectedProcedure
    .input(CustomerRateAgentSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // check weither user has already rated this agent or not
        const isAlreadyRated = await ctx.db.customerRatingsToAgents.findFirst({
          where: {
            ratedToUserId: input.ratedToUserId,
            ratedByUserId: ctx.session.user.id,
          },
        });

        if (isAlreadyRated) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You have already rated this agent.",
          });
        }

        const connection = await ctx.db.customerAgentConnections.findUnique({
          where: {
            id: input.connectionId,
          },
        });

        if (!connection)
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Conversation not found.",
          });

        await ctx.db.customerRatingsToAgents.create({
          data: {
            ratedToUserId: input.ratedToUserId,
            ratedByUserId: ctx.session.user.id,
            connectionId: input.connectionId,
            fileKey: input.fileKey,
            filePublicUrl: input.filePublicUrl,
            propertyId: input.propertyId,
            userStarsCount: input.userStarsCount,
            propertyStarsCount: input.propertyStarsCount,
            propertyRatingMessage: input.propertyRatingMessage,
            userRatingMessage: input.userRatingMessage,
          },
        });

        const previousCustomerToAgentRatingAvg =
          await ctx.db.customerRatingsToAgents.aggregate({
            where: {
              ratedToUserId: connection.agentId,
            },
            _avg: {
              userStarsCount: true,
            },
          });

        const customerToAgentAvg =
          previousCustomerToAgentRatingAvg._avg.userStarsCount ?? 0;

        console.log("customerToAgentAvg", customerToAgentAvg);

        const previousAgentToAgentRatingsAvg = await ctx.db.rating.aggregate({
          where: {
            ratedToUserId: connection.agentId,
          },
          _avg: {
            userStarsCount: true,
          },
        });

        const agentToAgentAvg =
          previousAgentToAgentRatingsAvg._avg.userStarsCount ?? 0;

        // Determine the overall average rating
        let overallAvgRating;
        if (agentToAgentAvg && customerToAgentAvg) {
          overallAvgRating = (
            (agentToAgentAvg + customerToAgentAvg) /
            2
          ).toFixed(2);
        } else if (agentToAgentAvg) {
          overallAvgRating = agentToAgentAvg.toFixed(2);
        } else if (customerToAgentAvg) {
          overallAvgRating = customerToAgentAvg.toFixed(2);
        } else {
          overallAvgRating = "0.00";
        }

        // update the avg rating of the user
        await ctx.db.user.update({
          where: {
            id: connection.agentId,
          },
          data: {
            rating: overallAvgRating,
          },
        });

        if (connection.propertyId) {
          const previousPropertyRatingsAvg = await ctx.db.rating.aggregate({
            where: {
              ratedToUserId: connection.agentId,
            },
            _avg: {
              propertyStarsCount: true,
            },
          });
          const propertyAvg =
            previousPropertyRatingsAvg._avg.propertyStarsCount?.toFixed(2);

          await ctx.db.property.update({
            where: {
              id: connection.propertyId,
            },
            data: {
              rating: String(propertyAvg),
            },
          });
        }

        return {
          message: "Thank you for rating the agent.",
        };
      } catch (err) {
        if (err instanceof TRPCError) throw err;

        console.error(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create review",
        });
      }
    }),

  customerRateToAgentNew: protectedProcedure
    .input(
      CustomerRateAgentSchema.omit({
        fileKey: true,
        filePublicUrl: true,
      }).extend({
        cloudinaryPublicId: z.string().optional(),
        cloudinaryUrl: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // check weither user has already rated this agent or not
        const isAlreadyRated = await ctx.db.customerRatingsToAgents.findFirst({
          where: {
            ratedToUserId: input.ratedToUserId,
            ratedByUserId: ctx.session.user.id,
          },
        });

        if (isAlreadyRated) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You have already rated this agent.",
          });
        }

        const connection = await ctx.db.customerAgentConnections.findUnique({
          where: {
            id: input.connectionId,
          },
        });

        if (!connection)
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Conversation not found.",
          });

        await ctx.db.customerRatingsToAgents.create({
          data: {
            ratedToUserId: input.ratedToUserId,
            ratedByUserId: ctx.session.user.id,
            connectionId: input.connectionId,
            cloudinaryPublicId: input.cloudinaryPublicId,
            cloudinaryUrl: input.cloudinaryUrl,
            propertyId: input.propertyId,
            userStarsCount: input.userStarsCount,
            propertyStarsCount: input.propertyStarsCount,
            propertyRatingMessage: input.propertyRatingMessage,
            userRatingMessage: input.userRatingMessage,
          },
        });

        const previousCustomerToAgentRatingAvg =
          await ctx.db.customerRatingsToAgents.aggregate({
            where: {
              ratedToUserId: connection.agentId,
            },
            _avg: {
              userStarsCount: true,
            },
          });

        const customerToAgentAvg =
          previousCustomerToAgentRatingAvg._avg.userStarsCount ?? 0;

        console.log("customerToAgentAvg", customerToAgentAvg);

        const previousAgentToAgentRatingsAvg = await ctx.db.rating.aggregate({
          where: {
            ratedToUserId: connection.agentId,
          },
          _avg: {
            userStarsCount: true,
          },
        });

        const agentToAgentAvg =
          previousAgentToAgentRatingsAvg._avg.userStarsCount ?? 0;

        // Determine the overall average rating
        let overallAvgRating;
        if (agentToAgentAvg && customerToAgentAvg) {
          overallAvgRating = (
            (agentToAgentAvg + customerToAgentAvg) /
            2
          ).toFixed(2);
        } else if (agentToAgentAvg) {
          overallAvgRating = agentToAgentAvg.toFixed(2);
        } else if (customerToAgentAvg) {
          overallAvgRating = customerToAgentAvg.toFixed(2);
        } else {
          overallAvgRating = "0.00";
        }

        // update the avg rating of the user
        await ctx.db.user.update({
          where: {
            id: connection.agentId,
          },
          data: {
            rating: overallAvgRating,
          },
        });

        if (connection.propertyId) {
          const previousPropertyRatingsAvg = await ctx.db.rating.aggregate({
            where: {
              ratedToUserId: connection.agentId,
            },
            _avg: {
              propertyStarsCount: true,
            },
          });
          const propertyAvg =
            previousPropertyRatingsAvg._avg.propertyStarsCount?.toFixed(2);

          await ctx.db.property.update({
            where: {
              id: connection.propertyId,
            },
            data: {
              rating: String(propertyAvg),
            },
          });
        }

        return {
          message: "Thank you for rating the agent.",
        };
      } catch (err) {
        if (err instanceof TRPCError) throw err;

        console.error(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create review",
        });
      }
    }),

  deleteCustomerReviewToAgent: protectedProcedure
    .input(z.object({ reviewId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const review = await ctx.db.customerRatingsToAgents.findUnique({
          where: {
            id: input.reviewId,
          },
        });

        if (!review) {
          throw new TRPCError({
            message: "Review not found.",
            code: "NOT_FOUND",
          });
        }

        // deleting the video from cloudinary
        if (review.cloudinaryPublicId) {
          cloudinary.config({
            api_key: env.CLOUDINARY_API_KEY,
            api_secret: env.CLOUDINARY_API_SECRET,
            cloud_name: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
          });

          await cloudinary.uploader.destroy(review.cloudinaryPublicId, {
            invalidate: true,
          });
        }

        await ctx.db.customerRatingsToAgents.delete({
          where: {
            id: input.reviewId,
          },
        });

        return {
          message: "Review deleted successfully.",
        };
      } catch (err) {
        console.log("error is", err);
        throw new TRPCError({
          message: "Error in deleting the review.",
          code: "BAD_REQUEST",
        });
      }
    }),
});
