import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../trpc";

const LikePostSchema = z.object({
  postId: z.string(),
});

const DislikePostSchema = z.object({
  postId: z.string(),
  likeId: z.string(),
});

const NewCommentSchema = z.object({
  comment: z.string(),
  postId: z.string(),
});

export const socialRouter = createTRPCRouter({
  likePost: protectedProcedure
    .input(LikePostSchema)
    .mutation(async ({ ctx, input }) => {
      const { postId } = input;
      const customerId = ctx.session.user.id;

      if (!customerId) throw new TRPCError({ code: "UNAUTHORIZED" });

      try {
        const like = await ctx.db.like.findFirst({
          where: {
            customerId:customerId,
            postId: postId,
          },
        });
        await ctx.db.$transaction(async (prisma) => {
          if (!like) {
            await prisma.like.create({
              data: {
                customerId: customerId,
                postId: postId,
              },
            });

            await prisma.post.update({
              where: {
                id: postId,
              },
              data: {
                totalLikes: {
                  increment: 1,
                },
              },
            });
          } else {
            await prisma.like.delete({
              where: {
                id: like.id,
              },
            });

            await prisma.post.update({
              where: {
                id: postId,
              },
              data: {
                totalLikes: {
                  decrement: 1,
                },
              },
            });
          }
        });

        return {
          message: like ? "Post unliked." : "Post liked.",
          isLiked: like ? false : true,
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to like post.",
        });
      }
    }),

  unlikePost: protectedProcedure
    .input(DislikePostSchema)
    .mutation(async ({ ctx, input }) => {
      const { postId, likeId } = input;
      const userId = ctx.session.user.id;

      if (!userId) throw new TRPCError({ code: "UNAUTHORIZED" });

      try {
        await ctx.db.$transaction(async (prisma) => {
          await prisma.like.delete({
            where: {
              id: likeId,
            },
          });

          await prisma.post.update({
            where: {
              id: postId,
            },
            data: {
              totalLikes: {
                decrement: 1,
              },
            },
          });
        });

        return {
          message: "Post disliked.",
        };
      } catch (err) {
        console.log(err);
      }
    }),

  newComment: protectedProcedure
    .input(NewCommentSchema)
    .mutation(async ({ ctx, input }) => {
      const { comment, postId } = input;
      console.log("comment and postId is", comment, postId);
      const userId = ctx.session.user.id;
      console.log("user is", ctx.session.user);

      if (!userId) throw new TRPCError({ code: "UNAUTHORIZED" });

      try {
        await ctx.db.$transaction(async (prisma) => {
          await prisma.comment.create({
            data: {
              comment: comment,
              postId: postId,
              isPinned: false,
              customerId: userId,
            },
          });

          await prisma.post.update({
            where: {
              id: postId,
            },
            data: {
              totalComments: {
                increment: 1,
              },
            },
          });
        });

        return {
          message: "Comment added.",
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to add comment",
        });
      }
    }),

  reportPost: protectedProcedure
    .input(z.object({ postId: z.string(), reason: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { postId, reason } = input;
      const userId = ctx.session.user.id;
      console.log("userId , postId , raeson is", userId, postId, reason);
      try {
        const reportedPost = await ctx.db.reportedPost.findFirst({
          where: {
            AND: [{ customerId: userId }, { postId: postId }],
          },
        });

        if (reportedPost)
          return { message: "You have already reported this post" };
        await ctx.db.post.update({
          where: {
            id: postId,
          },
          data: {
            reportCount: {
              increment: 1,
            },
          },
        });
        await ctx.db.reportedPost.create({
          data: {
            reason: reason,
            postId: postId,
            customerId: userId,
          },
        });
        return { message: "Post reported" };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to report post",
        });
      }
    }),
});
