import {
  ConnectionRequestsEnum,
  CustomerAgentConnectionChatStateEnum,
} from "@repo/database";
import {
  CustomerGetMessagesSchema,
  CustomerSendConnectionRequestToAgentSchema,
  CustomerSendMessageSchema,
} from "@repo/validators";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../trpc";
import NotificationMessages from "../utils/notification";

const chatRouter = createTRPCRouter({
  // read queries
  getActiveConversation: protectedProcedure.query(async ({ ctx }) => {
    // customer can get only single conversation which must be active. and also only a single conversation can be active at a point of time. to start another chat needed to close active chat then only be customer will be eligible to start a new conversation with agent.
    try {
      const conversations = await ctx.db.customerAgentConnections.findFirst({
        where: {
          state: CustomerAgentConnectionChatStateEnum.ACCEPTED,
          customerId: ctx.session.user.id,
          deletedAt: null,
        },
        include: {
          agent: {
            select: {
              id: true,
              name: true,
              filePublicUrl: true,
              cloudinaryProfileImageUrl: true,
              isOnline: true,
              rating: true,
            },
          },
          messages: {
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
          },
        },
        take: 1,
      });

      const previousRatingToAgent =
        await ctx.db.customerRatingsToAgents.findMany({
          where: {
            ratedByUserId: ctx.session.user.id,
            ratedToUserId: conversations?.agent.id,
          },
        });

      if (!conversations || !conversations.agent) {
        return null; // Return null instead of a malformed object
      }

      return { ...conversations, rating: previousRatingToAgent };
    } catch (err) {
      console.log("[server]: ", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch conversations",
      });
    }
  }),
  getMessages: protectedProcedure
    .input(CustomerGetMessagesSchema)
    .query(async ({ ctx, input }) => {
      const { connectionId, cursor, limit } = input;

      try {
        const messages = await ctx.db.customerAgentConnectionMessages.findMany({
          where: {
            connectionId: connectionId,
          },
          take: limit + 1,
          cursor: cursor ? { id: cursor } : undefined,
        });

        console.log(messages);

        let nextCursor: typeof cursor | undefined = undefined;
        if (messages.length > limit) {
          const nextItem = messages.pop();
          nextCursor = nextItem?.id;
        }

        return {
          messages: messages,
          nextCursor,
        };
      } catch (err) {
        console.log("[server]: ", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get messages",
        });
      }
    }),

  // create queries
  sendNewConnectionRequest: protectedProcedure
    .input(CustomerSendConnectionRequestToAgentSchema)
    .mutation(async ({ ctx, input }) => {
      if (!ctx.session.user.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      const { agentId, propertyId } = input;

      /* Checks to consider when sending a connection request:
            1. A customer can only have one active conversation with any given agent at a time.
            2. Before sending a connection request to a new agent, ensure that the customer's previous conversation with any agent is either inactive or closed.
            3. Check whether there is already a pending connection request with the same agent.
      */
      try {
        const request = await ctx.db.customerAgentConnections.findMany({
          where: {
            customerId: ctx.session.user.id,
            deletedAt: null,
            state: {
              not: CustomerAgentConnectionChatStateEnum.CLOSED,
            },
          },
        });

        if (request.length > 0) {
          for (const item of request) {
            if (item.state === CustomerAgentConnectionChatStateEnum.ACCEPTED) {
              return {
                warning: true,
                message: "You already have an active conversation.",
              };
            } else if (
              item.state === CustomerAgentConnectionChatStateEnum.PENDING &&
              item.agentId === agentId
            ) {
              return {
                warning: true,
                message:
                  "You already have a pending connection request with this agent.",
              };
            } else if (
              item.state === CustomerAgentConnectionChatStateEnum.REJECTED &&
              item.agentId === agentId
            ) {
              return {
                warning: true,
                message: "Your connection request is refused by the agent.",
              };
            }
          }
        }

        // Check if there was a previous (now deleted or closed) connection with this agent
        const _previousConnection =
          await ctx.db.customerAgentConnections.findFirst({
            where: {
              customerId: ctx.session.user.id,
              agentId: agentId,
              OR: [
                { deletedAt: { not: null } },
                { state: CustomerAgentConnectionChatStateEnum.CLOSED },
              ],
            },
          });

        // if controls reaches here this means there is no active conversation this means we can start a new conversation.
        const newConnection = await ctx.db.customerAgentConnections.create({
          data: {
            customerId: ctx.session.user.id,
            agentId: agentId,
            state: "ACCEPTED",
            ...(propertyId ? { propertyId: propertyId } : {}),
          },
          select: {
            id: true,
            customer: {
              select: {
                profileImagePublicUrl: true,
              },
            },
          },
        });

        let propertyTitle: string | undefined | null = undefined;
        if (propertyId) {
          const property = await ctx.db.property.findUnique({
            where: {
              id: propertyId,
            },
            select: {
              propertyTitle: true,
            },
          });
          propertyTitle = property?.propertyTitle;
        }

        const message = NotificationMessages.customerConnection({
          senderName: ctx.session.user.name,
          propertyName: propertyTitle,
        });

        // create the notification user ie. agent.
        await ctx.db.notification.create({
          data: {
            title: message.title,
            description: message.description,
            type: "CUSTOMER_CONNECTION",
            metaData: {
              chatId: newConnection.id,
              customerProfileUrl: newConnection.customer.profileImagePublicUrl,
            },
            receiverId: agentId,
          },
        });

        return {
          message: "Connected successfully.",
        };
      } catch (err) {
        console.log("[server]: ", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send connection request",
        });
      }
    }),

  updateConnectionRequest: protectedProcedure
    .input(
      z.object({
        agentId: z.string(),
        // connectedAgentId: z.string(),
        connectionId: z.string(),
        propertyId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.session.user.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      const { agentId, connectionId, propertyId } = input;

      try {
        // check for weither the user is already connected with this agent or not
        const r = await ctx.db.customerAgentConnections.findUnique({
          where: {
            id: connectionId,
          },
        });

        if (r?.agentId === agentId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You are already connected with this agent.",
          });
        }

        await ctx.db.$transaction(async (prisma) => {
          await prisma.customerAgentConnections.update({
            where: {
              id: connectionId,
            },
            data: {
              state: "CLOSED",
            },
          });

          const newConnection = await prisma.customerAgentConnections.create({
            data: {
              customerId: ctx.session.user.id,
              agentId: agentId,
              state: "ACCEPTED",
              ...(propertyId ? { propertyId: propertyId } : {}),
            },
            select: {
              id: true,
              customer: {
                select: {
                  profileImagePublicUrl: true,
                },
              },
            },
          });

          let propertyTitle: string | undefined | null = undefined;
          if (propertyId) {
            const property = await ctx.db.property.findUnique({
              where: {
                id: propertyId,
              },
              select: {
                propertyTitle: true,
              },
            });
            propertyTitle = property?.propertyTitle;
          }

          const message = NotificationMessages.customerConnection({
            senderName: ctx.session.user.name,
            propertyName: propertyTitle,
          });

          // create the notification user ie. agent.
          await ctx.db.notification.create({
            data: {
              title: message.title,
              description: message.description,
              type: "CUSTOMER_CONNECTION",
              metaData: {
                chatId: newConnection.id,
                customerProfileUrl:
                  newConnection.customer.profileImagePublicUrl,
              },
              receiverId: agentId,
            },
          });

          return {
            message:
              "Connection request sent to new agent and closed with old agent",
          };
        });
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send connection request",
        });
      }
    }),

  sendMessage: protectedProcedure
    .input(CustomerSendMessageSchema)
    .mutation(async ({ ctx, input }) => {
      if (!ctx.session.user.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      const { message, connectionId } = input;

      try {
        // check weither the sender of message is a participant of conversation.
        const convo = await ctx.db.customerAgentConnections.findUnique({
          where: {
            id: connectionId,
            state: ConnectionRequestsEnum.ACCEPTED,
          },
          include: {
            agent: {
              select: {
                id: true,
                isOnline: true,
              },
            },
            customer: {
              select: {
                id: true,
                active: true,
              },
            },
          },
        });

        if (convo?.customerId !== ctx.session.user.id) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Failed to send message",
          });
        }

        await ctx.db.$transaction(async (prisma) => {
          const newMessage =
            await prisma.customerAgentConnectionMessages.create({
              data: {
                content: message,
                senderId: ctx.session.user.id,
                connectionId: connectionId,
                read: false,
              },
            });

          if (!convo.customer.active) {
            await prisma.customerAgentConnections.update({
              where: {
                id: convo.id,
              },
              data: {
                customerUnseenMessagesCount: {
                  increment: 1,
                },
              },
            });
          } else {
            if (!convo.agent.isOnline) {
              await prisma.customerAgentConnections.update({
                where: {
                  id: convo.id,
                },
                data: {
                  agentUnseenMessagesCount: {
                    increment: 1,
                  },
                },
              });
            }
          }

          return newMessage;
        });

        return {
          message: "Message sent.",
        };
      } catch (err) {
        console.log("[server]: ", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send message",
        });
      }
    }),

  toggleStatus: protectedProcedure
    .input(z.object({ status: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      const { status } = input;
      const userId = ctx.session.user.id;

      try {
        await ctx.db.customer.update({
          where: {
            id: userId,
          },
          data: {
            active: status,
          },
        });

        return {
          message: "Status toggled successfully",
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
      }
    }),

  setUnseenMessagesToZero: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        await ctx.db.customerAgentConnections.update({
          where: {
            id: input.conversationId,
          },
          data: {
            customerUnseenMessagesCount: 0,
          },
        });
      } catch (err) {
        console.error(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to set messages 0.",
        });
      }
    }),

  deleteChat: protectedProcedure
    .input(
      z.object({
        connectionId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { connectionId } = input;

      try {
        await ctx.db.customerAgentConnections.update({
          where: {
            id: connectionId,
          },
          data: {
            deletedAt: new Date(),
            state: CustomerAgentConnectionChatStateEnum.CLOSED,
          },
        });

        return {
          message: "Chat deleted successfully.",
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete chat.",
        });
      }
    }),

  reportAgentFromChat: protectedProcedure
    .input(
      z.object({
        reportedUserId: z.string(),
        reasonForReporting: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { reasonForReporting, reportedUserId } = input;
      const userId = ctx.session.user.id;

      try {
        await ctx.db.reportedUserFromChat.create({
          data: {
            reasonForReporting: reasonForReporting,
            reportedUserId: reportedUserId,
            reportingCustomerId: userId,
          },
        });
        return {
          message: "Agent reported successfully",
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to report agent.",
        });
      }
    }),
});

export default chatRouter;
