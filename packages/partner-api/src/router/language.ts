import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const languageRouter = createTRPCRouter({
  getAllLanguages: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.languages.findMany({
      select: {
        id: true,
        name: true,
      },
      //   where: {
      //     user: {
      //       some: {
      //         id: ctx.user.id,
      //       },
      //     },
      //   },
      where: {
        deletedAt: null,
      },
    });
  }),
  getMyLanguage: protectedProcedure.query(async ({ ctx, input }) => {
    return await ctx.db.languages.findMany({
      select: {
        id: true,
        name: true,
      },
      where: {
        user: {
          some: {
            id: ctx.user.id,
          },
        },
      },
      orderBy:{
        name:"asc"
      }
    });
  }),
  addOrRemoveLanguage: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { id } = input;
      try {
        const alreadyConnected = await ctx.db.user.findUnique({
          where: {
            id: ctx.user.id,
            languages: {
              some: {
                id: id,
              },
            },
          },
          include: {
            languages: true,
          },
        });

        if (alreadyConnected?.languages.some((s) => s.id === id)) {
          await ctx.db.user.update({
            where: {
              id: ctx.user.id,
            },
            data: {
              languages: {
                disconnect: {
                  id: id,
                },
              },
            },
          });
          return {
            message: "Removed successfully",
          };
        }

        await ctx.db.user.update({
          where: {
            id: ctx.user.id,
          },
          data: {
            languages: {
              connect: {
                id: id,
              },
            },
          },
        });
        return {
          message: "language added successfully.",
        };
      } catch (err) {
        throw new TRPCError({
          message: "Error in adding language.",
          code: "BAD_REQUEST",
        });
      }
    }),
});
