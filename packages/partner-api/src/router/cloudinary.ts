import { v2 as cloudinaryClient } from "cloudinary";
import { z } from "zod";

import { env } from "../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";

cloudinaryClient.config({
  cloud_name: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: env.CLOUDINARY_API_KEY,
  api_secret: env.CLOUDINARY_API_SECRET,
});

export const cloudinaryRouter = createTRPCRouter({
  generateSignature: protectedProcedure
    .input(
      z.object({
        paramsToSign: z.object({
          timestamp: z.number(),
          folderFor: z.string().default("users"),
          forlderPurpose: z.enum(["profile", "company", "posts", "properties"]),
        }),
      }),
    )
    .query(({ ctx, input }) => {
      console.log("input", input);
      console.log("api called");
      const uploadFolderUrl = `${env.NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME}/${input.paramsToSign.folderFor}/${ctx.user.id}/${input.paramsToSign.forlderPurpose}`;

      const { paramsToSign } = input;
      console.log("paramsToSign ", paramsToSign);
      const signature = cloudinaryClient.utils.api_sign_request(
        {
          timestamp: paramsToSign.timestamp,
          folder: uploadFolderUrl,
          upload_preset: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET,
        },
        env.CLOUDINARY_API_SECRET,
      );
      console.log("signature ", signature);
      return {
        signature,
        uploadFolderUrl,
        cloudPreset: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET,
        apiKey: env.CLOUDINARY_API_KEY,
        cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      };
    }),
});
