import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const profileViewRouter = createTRPCRouter({
  createProfileView: protectedProcedure
    .input(z.object({ viewerId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { viewerId } = input;

      try {
        //if user itself views his/her profile
        if(ctx.user.id === viewerId)
        {
            return;
        }
        //if already presents inside the history
        const alreadyExistsProfileView = await ctx.db.profileView.findFirst({
          where: {
            viewedByUserId: ctx.user.id,
            viewedToUserId: viewerId,
          },
        });

        if (alreadyExistsProfileView) {
          return;
        }

        const profileView = await ctx.db.profileView.create({
          data: {
            viewedByUserId: ctx.user.id,
            viewedToUserId: viewerId,
          },
        });

        console.log("profile view created successfully", profileView);
        return;
      } catch (err) {
        console.log("error is", err);
        throw new TRPCError({
          message: "Error in creating profile view.",
          code: "BAD_REQUEST",
        });
      }
    }),

  getProfileViews: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.profileView.findMany({
      select: {
        viewedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
            filePublicUrl: true,
            company: {
              select: {
                companyName: true,
              },
            },
          },
        },
      },
      where: {
        viewedToUserId: ctx.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 3,
    });
  }),
});
