"use client";

import React, { useRef, useState } from "react";
import { Prisma } from "@repo/database";
import { Pause, Play, Star } from "lucide-react";
import { CldVideoPlayer } from "next-cloudinary";

export type IAgentReviewCard = Prisma.CustomerRatingsToAgentsGetPayload<{
  include: {
    ratedBy: true;
    connection: {
      select: {
        customer: {
          select: {
            city: true;
            cityId: true;
          };
        };
      };
    };
  };
}>;
const AgentReviewCard = ({ item }: { item: IAgentReviewCard }) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const handleVideoToggle = () => {
    if (isPlaying) {
      videoRef.current?.pause();
      setIsPlaying(false);
    } else {
      videoRef.current?.play();
      setIsPlaying(true);
    }
  };

  return (
    <>
      <div className="relative flex aspect-[9/16] items-center justify-center rounded-lg bg-black">
        {item.cloudinaryPublicId ? (
          <CldVideoPlayer
            logo={{
              imageUrl: "/logos/new-logo-footer.svg",
              onClickUrl: "/",
            }}
            controls={false}
            src={item.cloudinaryPublicId}
            videoRef={videoRef}
          />
        ) : item.filePublicUrl ? (
          <video
            src={item.filePublicUrl}
            className="rounded-lg"
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            ref={videoRef}
          />
        ) : null}

        <div className="absolute bottom-6 left-6">
          <div className="flex flex-col gap-0.5">
            <div className="font-airbnb_w_bd text-base font-bold text-text-20">
              {item.ratedBy.name}
            </div>
            <div className="flex flex-row gap-2">
              <div className="flex flex-row items-center gap-0.5">
                <Star fill="#FFC727" color="#FFC727" className="size-4" />
                <p className="font-airbnb_w_bd text-sm font-medium text-text-100">
                  {item.userStarsCount}
                </p>
              </div>
              <div className="font-airbnb_w_md text-sm font-medium text-text-100">
                {item.connection?.customer.city?.name}
              </div>
            </div>
          </div>
        </div>
        <div className="absolute bottom-6 right-6">
          <button
            className="rounded-2xl border-2 border-primary-2-200 bg-white bg-opacity-[0.16] p-4 shadow-[0_15px_75px_0_rgba(27,25,68,0.30)] backdrop-blur-[13px]"
            onClick={handleVideoToggle}
          >
            {isPlaying ? (
              <Pause size={14} color="#FEF9EA" />
            ) : (
              <Play size={14} color="#FEF9EA" />
            )}
          </button>
        </div>
      </div>
    </>
  );
};

export default AgentReviewCard;
