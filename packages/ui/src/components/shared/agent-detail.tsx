"use client";

import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Prisma } from "@repo/database";
import { Separator } from "@repo/ui/components/ui/separator";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@repo/ui/components/ui/tabs";
import { useMediaQuery } from "@uidotdev/usehooks";
import { formatDistanceToNow } from "date-fns";
import { ChevronLeft, Star } from "lucide-react";

type IAgent = Prisma.UserGetPayload<{
  select: {
    id: true;
    name: true;
    userLocation: true;
    bio: true;
    propertiesSold: true;
    experience: true;
    createdAt: true;
    bgFileKey: true;
    bgFilePublicUrl: true;
    cloudinaryProfileImagePublicId: true;
    cloudinaryProfileImageUrl: true;
    cloudinaryBgImagePublicId: true;
    cloudinaryBgImageUrl: true;
    fileKey: true;
    filePublicUrl: true;
    rating: true;
    reviews: true;
    company: true;
    properties: {
      include: {
        areaUnit: true;
        utilities: true;
        amenities: true;
        user: {
          select: {
            id: true;
            company: true;
            companyDetails: true;
          };
        };
        customerFavourites: true;
        mediaSections: {
          include: {
            media: {
              select: {
                id: true;
                fileKey: true;
                filePublicUrl: true;
                cloudinaryId: true;
                cloudinaryUrl: true;
              };
            };
          };
        };
      };
    };
    operationArea: true;
    posts: {
      include: {
        user: {
          select: {
            id: true;
            filePublicUrl: true;
            name: true;
            company: {
              select: {
                companyName: true;
              };
            };
          };
        };
        media: {
          select: {
            filePublicUrl: true;
            mediaType: true;
          };
        };
        comments: {
          select: {
            comment: true;
            isPinned: true;
            createdAt: true;
            user: {
              select: {
                id: true;
                name: true;
                filePublicUrl: true;
                companyDetails: {
                  select: {
                    companyName: true;
                  };
                };
              };
            };
          };
        };
        likes: {
          select: {
            id: true;
            postId: true;
          };
          take: 10;
        };
      };
      orderBy: {
        createdAt: "desc";
      };
    };
    languages: true;
    coustomerConnections: true;
    receivedConnectionRequests: true;
    sentConnectionRequests: true;
    customerRatingsToAgents: true;
  };
}>;

type AgentDetailProps = {
  agent: IAgent;
  contactAgent?: React.ReactNode;
  likeAgentBtn?: React.ReactNode;
  shareAgentProfileButton?: React.ReactNode;
  agentPostsCardSwiper: React.ReactNode;
  agentPropertyCardsSwiper: React.ReactNode;
  videoReviews: React.ReactNode;
  recordProfileView?: () => void;
};
const AgentDetail = ({
  agent,
  contactAgent,
  likeAgentBtn,
  shareAgentProfileButton,
  agentPostsCardSwiper,
  agentPropertyCardsSwiper,
  videoReviews,
  recordProfileView,
}: AgentDetailProps) => {
  const isSmallDevice = useMediaQuery("only screen and (max-width : 767px)");
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const agentProfileDetails = [
    {
      icon: "/icons/shared/property-sold.svg",
      title: "Total sales closed",
      value: agent.propertiesSold || 0,
    },
    {
      icon: "/icons/shared/experience.svg",
      title: "Experience",
      value: `${agent.experience || 0} yrs`,
    },
    {
      icon: "/icons/shared/active.svg",
      title: "Active",
      value: `${formatDistanceToNow(agent.createdAt)}`,
    },
    {
      icon: "/icons/shared/connections.svg",
      title: "Connections",
      value:
        agent.coustomerConnections.length +
        agent.sentConnectionRequests.length +
        agent.receivedConnectionRequests.length,
    },
  ];

  const propertyDetails = [
    {
      info: "Website:",
      value: agent.company?.companyWebsiteLink,
      href: agent.company?.companyWebsiteLink,
    },
    {
      info: "Location:",
      value: agent.company?.companyLocation,
      href: `${agent.company?.companyLocation ? `https://www.google.com/maps/search/${encodeURIComponent(agent.company?.companyLocation)}` : null}`,
    },
    {
      info: "Mobile:",
      value: agent.company?.phoneNumber,
      href: `tel:${agent.company?.phoneNumber}`,
    },
    {
      info: "Email:",
      value: agent.company?.email,
      href: `mailto:${agent.company?.email}`,
    },
    {
      info: "Fax:",
      value: agent.company?.fax,
      href: null,
    },
  ];

  useEffect(() => {
    console.log("need to create the profile view");
    if (recordProfileView) {
      console.log("calling the record profile view");
      recordProfileView();
    }
  }, [agent.id]);

  return (
    <>
      <div className="flex flex-col gap-3 overflow-x-hidden p-5 lg:gap-4">
        {/* profile info */}
        <div className="rounded-xl border border-secondary-2-100 bg-text-20">
          <div className="relative">
            {agent.bgFilePublicUrl || agent.cloudinaryBgImageUrl ? (
              <div className="relative mb-[37px] h-[180px] w-full 2xl:h-[236px]">
                <Image
                  src={agent.cloudinaryBgImageUrl ?? agent.bgFilePublicUrl!}
                  alt="profile-background-image"
                  className="rounded-tl-xl rounded-tr-xl object-cover"
                  fill
                />
              </div>
            ) : (
              <div className="mb-[37px] h-[180px] w-full rounded-tl-xl rounded-tr-xl bg-primary-2-100"></div>
            )}
            <div
              onClick={(e) => {
                e.stopPropagation();
                params.delete("viewAgentId");
                history.pushState(null, "", `?${params.toString()}`);
              }}
              className="absolute left-5 top-5 cursor-pointer rounded-full bg-secondary-2-100 p-3 lg:left-8 lg:top-8"
            >
              <ChevronLeft className="size-[16px] cursor-pointer text-text-500 lg:size-[18px]" />
            </div>
            <Image
              src={
                agent.cloudinaryProfileImageUrl ??
                agent.filePublicUrl ??
                "/images/placeholder-user-image.jpg"
              }
              alt="profile"
              height={1000}
              width={1000}
              className="absolute bottom-[-30px] left-5 size-[100px] rounded-full object-cover lg:bottom-[-60px] lg:left-[32px] lg:size-[140px] 2xl:size-[160px]"
            />
          </div>
          {/* detailed profile info */}
          <div className="flex w-full flex-col gap-5 p-4 md:gap-6 md:p-5 lg:gap-8 lg:p-[30px] xl:p-8 2xl:p-10">
            {/* user data */}
            <div className="flex flex-col gap-2 xl:gap-3">
              <div className="flex flex-row justify-between">
                <div className="flex flex-col gap-1 2xl:gap-1.5">
                  <div className="font-airbnb_w_xbd text-xl font-extrabold leading-[32px] text-primary-2-800 md:text-2xl md:leading-[34px] lg:text-[30px] lg:leading-[40px] 2xl:text-4xl 2xl:leading-[42px]">
                    {agent.name}
                  </div>
                  {agent.company && (
                    <div className="font-airbnb_w_md text-sm font-medium text-text-600 md:text-base lg:text-lg 2xl:text-[28px] 2xl:leading-[30px]">
                      {agent.company.companyName}
                    </div>
                  )}
                </div>
                <div className="flex flex-col items-end justify-between">
                  <div className="flex flex-row items-center gap-0.5">
                    <Star
                      fill="#FFC727"
                      color="#FFC727"
                      className="size-5 2xl:size-6"
                    />
                    <p className="font-airbnb_w_bd text-base font-bold text-text-600 lg:text-xl lg:leading-[30px] 2xl:text-2xl">
                      {agent.rating ?? 0}
                    </p>
                  </div>
                  <div className="font-airbnb_w_md text-sm font-medium text-text-600 md:text-base lg:text-lg 2xl:text-xl 2xl:leading-[30px]">
                    {agent.customerRatingsToAgents.length ?? 0} Reviews
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-1 text-sm md:flex-row md:flex-wrap md:gap-4 md:text-base lg:text-lg 2xl:text-[22px] 2xl:leading-[34px]">
                {agent.userLocation && (
                  <div className="font-airbnb_w_bk font-normal text-text-500">
                    {agent.userLocation}
                  </div>
                )}
              </div>
            </div>
            {/* stats */}
            <div
              className={
                isSmallDevice
                  ? `flex flex-wrap justify-between gap-3 sm:gap-5`
                  : `flex flex-row items-center`
              }
            >
              {agentProfileDetails.map((agent, index) => (
                <>
                  <div
                    key={index}
                    className={`flex flex-row items-center ${
                      index % 2 === 1
                        ? "justify-end"
                        : "w-1/2 justify-center sm:w-fit"
                    }`}
                  >
                    <div className="flex flex-col items-center gap-1.5">
                      <div className="font-airbnb_w_bd text-lg font-bold text-primary-2-700 lg:text-xl">
                        {agent.value}
                      </div>
                      <div className="flex flex-row gap-1">
                        <div className="relative aspect-square size-[22px]">
                          <Image
                            src={agent.icon}
                            alt="icon-image"
                            className="size-[22px]"
                            fill
                          />
                        </div>
                        <div className="font-airbnb_w_bk text-sm font-normal text-text-500">
                          {agent.title}
                        </div>
                      </div>
                    </div>
                    {index != agentProfileDetails.length - 1 &&
                      !isSmallDevice && (
                        <Separator
                          orientation="vertical"
                          className="mx-4 h-[55px] bg-primary-2-300 lg:mx-5"
                        />
                      )}
                  </div>
                </>
              ))}
            </div>
            {/* buttons */}
            <div className="flex flex-wrap gap-5 md:gap-6 xl:gap-8">
              {contactAgent}
              {likeAgentBtn}
              {shareAgentProfileButton}
            </div>
          </div>
        </div>

        {/* Activity */}
        <div className="flex flex-col gap-3 rounded-xl border border-secondary-2-100 bg-text-20 p-4 md:gap-4 md:p-5 lg:p-[30px] xl:p-8 2xl:gap-6 2xl:p-10">
          <div>
            <h2 className="font-airbnb_w_bd text-lg font-bold text-primary-2-750 md:text-xl md:leading-[32px] lg:text-2xl lg:leading-[36px] 2xl:text-[30px] 2xl:leading-[40px]">
              Activity
            </h2>
            <Separator
              orientation="horizontal"
              className="my-3 xl:my-4 2xl:my-6"
            />
            <Tabs defaultValue="Post">
              <TabsList className="mb-5 rounded-lg bg-text-40 text-text-500 lg:mb-8">
                <TabsTrigger
                  value="Post"
                  className="rounded-bl-lg rounded-tl-lg px-[50px] py-2 font-airbnb_w_md text-sm font-medium data-[state=active]:bg-primary-2-550 data-[state=active]:text-primary-2-800 md:px-[48px] md:text-base xl:px-[46px] xl:text-lg 2xl:px-[44px] 2xl:text-xl"
                >
                  Post
                </TabsTrigger>
                <TabsTrigger
                  value="Property"
                  className="rounded-br-lg rounded-tr-lg px-[35.5px] py-2 font-airbnb_w_md text-sm font-medium data-[state=active]:bg-primary-2-550 data-[state=active]:text-primary-2-800 md:px-[31.5px] xl:px-[27.5px] xl:text-lg 2xl:px-[23px] 2xl:text-xl"
                >
                  Property
                </TabsTrigger>
              </TabsList>
              <TabsContent className="pb-1" value="Post">
                {agentPostsCardSwiper}
              </TabsContent>
              <TabsContent className="pb-1" value="Property">
                {agentPropertyCardsSwiper}
              </TabsContent>
            </Tabs>
          </div>
        </div>
        {/* Bio */}
        {agent.bio && (
          <div className="flex flex-col gap-3 rounded-xl border border-secondary-2-100 bg-text-20 p-4 md:gap-4 md:p-5 lg:p-[30px] xl:p-8 2xl:gap-6 2xl:p-10">
            <h2 className="font-airbnb_w_bd text-lg font-bold text-primary-2-750 md:text-xl md:leading-[32px] lg:text-2xl lg:leading-[36px] 2xl:text-[30px] 2xl:leading-[40px]">
              Bio
            </h2>
            <div className="font-airbnb_w_bk text-base font-normal leading-[28px] text-text-500 2xl:text-xl 2xl:leading-[32px]">
              {agent.bio}
            </div>
          </div>
        )}
        {/* About Company */}
        {agent.company && (
          <div className="flex flex-col gap-3 rounded-xl border border-secondary-2-100 bg-text-20 p-4 md:gap-4 md:p-5 lg:gap-8 lg:p-[30px] xl:p-8 2xl:gap-10 2xl:p-10">
            <div className="flex flex-col gap-3 md:gap-4 2xl:gap-6">
              <h2 className="font-airbnb_w_bd text-lg font-bold text-primary-2-750 md:text-xl md:leading-[32px] lg:text-2xl lg:leading-[36px] 2xl:text-[30px] 2xl:leading-[40px]">
                About Company
              </h2>
              <div className="font-airbnb_w_bk text-base font-normal leading-[28px] text-text-500 2xl:text-xl 2xl:leading-[32px]">
                {agent.company.about}
              </div>
            </div>
            {/* company additional information */}
            <div className="grid grid-cols-1 items-start gap-4 md:gap-5 lg:grid-cols-2 lg:gap-x-8 lg:gap-y-5 2xl:gap-y-8">
              {propertyDetails.map((detail, index) => (
                <React.Fragment key={index}>
                  {detail.value && (
                    <div className="flex flex-row justify-between gap-5 rounded-sm bg-[#FFFCF5] px-3 py-2 text-sm md:text-base">
                      <div className="font-airbnb_w_bk font-normal text-primary-2-750">
                        {detail.info}
                      </div>
                      {detail.href != null ? (
                        <Link href={detail.href}>
                          <div className="text-right font-airbnb_w_md font-medium text-text-700">
                            {detail.value}
                          </div>
                        </Link>
                      ) : (
                        <div className="text-right font-airbnb_w_md font-medium text-text-700">
                          {detail.value}
                        </div>
                      )}
                    </div>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
        )}
        {/* Operational area */}
        {agent.operationArea.length > 0 && (
          <div className="flex flex-col gap-6 rounded-xl border border-secondary-2-100 bg-text-20 p-4 md:p-5 lg:p-[30px] xl:gap-8 xl:p-8 2xl:gap-10 2xl:p-10">
            <div className="flex flex-col gap-3 md:gap-[14px] lg:gap-4 xl:gap-[18px] 2xl:gap-6">
              {/* heading*/}
              <h2 className="font-airbnb_w_bd text-lg font-bold text-primary-2-750 md:text-xl md:leading-[32px] lg:text-2xl lg:leading-[36px] 2xl:text-[30px] 2xl:leading-[40px]">
                Operational Areas
              </h2>
              {/* location and supporting areas */}
              <div className="flex flex-col gap-3 lg:gap-6 xl:gap-8">
                {agent.userLocation && (
                  <div className="font-airbnb_w_md text-base leading-[28px] text-text-500 2xl:text-xl 2xl:leading-[32px]">
                    {agent.userLocation}
                  </div>
                )}
                <div className="flex flex-wrap items-center gap-4 md:gap-6">
                  {agent.operationArea.map((area) => {
                    return (
                      <div className="flex items-center gap-2 rounded-[6px] bg-primary-2-100 px-3 py-2 font-airbnb_w_md text-base font-medium text-primary-2-800 2xl:text-lg">
                        {area.name}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* map */}
          </div>
        )}
        {/* Languages */}
        {agent.languages.length > 0 && (
          <div className="flex flex-col gap-3 rounded-xl border border-secondary-2-100 bg-text-20 p-4 md:gap-[14px] md:p-5 lg:gap-4 lg:p-[30px] xl:gap-[18px] xl:p-8 2xl:gap-6 2xl:p-10">
            {/* heading */}
            <h2 className="font-airbnb_w_bd text-lg font-bold text-primary-2-750 md:text-xl md:leading-[32px] lg:text-2xl lg:leading-[36px] 2xl:text-[30px] 2xl:leading-[40px]">
              Language
            </h2>
            {/* supporting languages */}
            <div className="flex flex-wrap items-center gap-4 md:gap-6">
              {agent.languages.map((lang) => {
                return (
                  <div className="flex items-center gap-2 rounded-[6px] bg-primary-2-100 px-3 py-2 font-airbnb_w_md text-base font-medium text-primary-2-800 2xl:text-lg">
                    {lang.name}
                  </div>
                );
              })}
            </div>
          </div>
        )}
        {/* Testimonials */}
        <div className="rounded-xl border border-secondary-2-100 bg-text-20 py-4 md:py-5 xl:py-8 2xl:py-10">
          <div className="flex flex-col gap-3 px-4 md:px-5 xl:gap-5 xl:px-8 2xl:px-10">
            {/* heading */}
            <h2 className="font-airbnb_w_bd text-lg font-bold text-primary-2-750 md:text-xl md:leading-[32px] lg:text-2xl lg:leading-[36px] 2xl:text-[30px] 2xl:leading-[40px]">
              Users Testimonials
            </h2>
            {/* Agent testimonials Swiper */}
            {videoReviews}
          </div>
        </div>
      </div>
    </>
  );
};

export default AgentDetail;
