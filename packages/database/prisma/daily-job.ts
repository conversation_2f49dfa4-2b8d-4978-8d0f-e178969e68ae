import { NotificationEnum, PrismaClient } from "@prisma/client";

// import cron from "node-cron";

// "0 9 * * *"
//  │ │ │ │ │
//  │ │ │ │ └── Day of week (0-6) (Sunday=0)
//  │ │ │ └──── Month (1-12)
//  │ │ └────── Day of month (1-31)
//  │ └──────── Hour (0-23)
//  └────────── Minute (0-59)

// "*/10 * * * * *" => every 10 seconds
// "0 21 * * *" => every day 9 pm
// "0 9 * * *" => every day 9 am

// This cron job works daily 9 AM but the issue is logic insde is not correct. need to be implmented correctly.

const db = new PrismaClient();

const verifyDatabaseConnection = async () => {
  try {
    // Attempt a simple query to verify connection
    await db.$queryRaw`SELECT 1`;
    console.log("✅ Database connection successful");
    console.log("✅ Cron job started successfully.");
    return true;
  } catch (error) {
    console.error("❌ Database connection failed:", error);
    return false;
  }
};

const handleUnseenMessagesNotificationReminderForPartner = async () => {
  try {
    // Get users with unseen messages from receiver's perspective
    const receiversWithUnseenMessages = await db.connectionRequests.findMany({
      where: {
        receiverUnseenMessagesCount: { gt: 0 },
        deletedAt: null,
        blocked: false,
        status: "ACCEPTED",
      },
      distinct: ["receiverId"],
      select: {
        receiverId: true,
      },
    });

    // Get users with unseen messages from sender's perspective
    const sendersWithUnseenMessages = await db.connectionRequests.findMany({
      where: {
        senderUnseenMessagesCount: { gt: 0 },
        deletedAt: null,
        blocked: false,
        status: "ACCEPTED",
      },
      distinct: ["senderId"],
      select: {
        senderId: true,
      },
    });

    // Combine unique user IDs who need notifications
    const notifications = [
      ...receiversWithUnseenMessages.map(({ receiverId }) => ({
        title: "You have unread messages",
        description: "You have pending messages to check",
        type: "CHAT" as NotificationEnum,
        metaData: {},
        receiverId: receiverId,
      })),
      ...sendersWithUnseenMessages.map(({ senderId }) => ({
        title: "You have unread messages",
        description: "You have pending messages to check",
        type: "CHAT" as NotificationEnum,
        metaData: {},
        receiverId: senderId,
      })),
    ];

    // Create all notifications at once
    if (notifications.length > 0) {
      await db.notification.createMany({
        data: notifications,
      });
    }

    console.log(
      `Cron job executed - Sent notifications to ${notifications.length} users with pending messages`,
    );
  } catch (error) {
    console.error(
      "Error in cron job, while sending unseen messages reminder to partners:",
      error,
    );
  }
};

const handlePendingConnectionRequestsReminderForPartner = async () => {
  try {
    // Find all pending connection requests
    const pendingConnections = await db.connectionRequests.findMany({
      where: {
        status: "PENDING",
        deletedAt: null,
        blocked: false,
      },
      select: {
        id: true,
        receiver: {
          select: {
            id: true,
          },
        },
        sender: {
          select: {
            name: true,
          },
        },
      },
      distinct: ["receiverId"],
    });

    await db.notification.createMany({
      data: pendingConnections.map((connection) => ({
        title: "Pending Connection Request",
        description: `You have a pending connection request from ${connection.sender.name}`,
        type: "CONNECTION",
        metaData: { connectionId: connection.id, type: "pending" },
        receiverId: connection.receiver.id,
      })),
    });

    console.log(
      `Cron job executed - Sent notifications for ${pendingConnections.length} pending connection requests`,
    );
  } catch (err) {
    console.error(
      "Error in cron job, while sending pending connection requests reminder to partners.",
    );
  }
};

(async () => {
  const isConnected = await verifyDatabaseConnection();

  if (isConnected) {
    // cron.schedule("0 9 * * *", async () => {
    //   await handleUnseenMessagesNotificationReminderForPartner();
    //   await handlePendingConnectionRequestsReminderForPartner();
    // });

    await handleUnseenMessagesNotificationReminderForPartner();
    await handlePendingConnectionRequestsReminderForPartner();
  } else {
    console.error("Cron jobs not started due to database connection failure");
    process.exit(1);
  }
})();
