import { relations, sql } from 'drizzle-orm'
import { bigint, boolean, decimal, doublePrecision, foreignKey, integer, jsonb, pgEnum, pgTable, text, timestamp, uniqueIndex } from 'drizzle-orm/pg-core'

export const AdminUserTypeEnum = pgEnum('AdminUserTypeEnum', ['ADMIN', 'MANAGER'])

export const ONBOARDING_STEP_ENUM = pgEnum('ONBOARDING_STEP_ENUM', ['STEP_1', 'STEP_2', 'STEP_3'])

export const EnquiryType = pgEnum('EnquiryType', ['AGENT', 'CUSTOMER'])

export const PropertyCategoryEnum = pgEnum('PropertyCategoryEnum', ['RESIDENTIAL', 'COMMERCIAL'])

export const PropertyForEnum = pgEnum('PropertyForEnum', ['SALE', 'RENT'])

export const FacingEnum = pgEnum('FacingEnum', ['NORTH', 'SOUTH', 'EAST', 'WEST', 'NORTH_EAST', 'NORTH_WEST', 'SOUTH_EAST', 'SOUTH_WEST'])

export const FurnishingEnum = pgEnum('FurnishingEnum', ['RAW', 'SEMIFURNISHED', 'FULLYFURNISHED'])

export const PropertyStateEnum = pgEnum('PropertyStateEnum', ['NEW', 'RESALE', 'UPCOMING'])

export const PossessionStateEnum = pgEnum('PossessionStateEnum', ['READY_TO_MOVE', 'UNDER_6_MONTHS', 'UNDER_1_YEAR', 'UNDER_3_YEARS'])

export const PropertyStatusEnum = pgEnum('PropertyStatusEnum', ['PENDING', 'REJECTED', 'InREVIEW', 'ACTIVE', 'INACTIVE', 'DELETED', 'SOLD'])

export const PostPropertyFormFieldStatusEnum = pgEnum('PostPropertyFormFieldStatusEnum', ['SHOW', 'HIDE', 'OPTIONAL'])

export const PropertyMediaTypeEnum = pgEnum('PropertyMediaTypeEnum', ['IMAGE', 'VIDEO'])

export const ProjectEnum = pgEnum('ProjectEnum', ['B2B_DEER_CONNECT', 'B2C_MY_DEER'])

export const FaqPagesEnum = pgEnum('FaqPagesEnum', ['HELP_CENTER_PAGE', 'DELETE_ACCOUNT_PAGE', 'CAREER_PAGE', 'CONTACT_US_PAGE'])

export const ConnectionRequestsEnum = pgEnum('ConnectionRequestsEnum', ['ACCEPTED', 'REJECTED', 'PENDING'])

export const NotificationEnum = pgEnum('NotificationEnum', ['CHAT', 'CONNECTION', 'POST', 'WALLET', 'CUSTOMER_CHAT', 'CUSTOMER_CONNECTION', 'PROPERTY'])

export const PostsMediaTypeEnum = pgEnum('PostsMediaTypeEnum', ['VIDEO', 'IMAGE'])

export const CustomerAgentConnectionChatStateEnum = pgEnum('CustomerAgentConnectionChatStateEnum', ['CLOSED', 'ACCEPTED', 'PENDING', 'REJECTED'])

export const AdminUser = pgTable('AdminUser', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	passwordHash: text('passwordHash').notNull(),
	userType: AdminUserTypeEnum('userType').notNull().default("ADMIN"),
	active: boolean('active').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 })
});

export const User = pgTable('User', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	emailVerified: timestamp('emailVerified', { precision: 3 }),
	passwordHash: text('passwordHash'),
	accountType: text('accountType').notNull().default("normal"),
	uid: text('uid'),
	token: text('token'),
	refreshToken: text('refreshToken'),
	resetPasswordToken: text('resetPasswordToken'),
	resetPasswordSentAt: timestamp('resetPasswordSentAt', { precision: 3 }),
	active: boolean('active').notNull(),
	statusUpdatedAt: timestamp('statusUpdatedAt', { precision: 3 }),
	statusUpdateRemarks: text('statusUpdateRemarks'),
	statusUpdatedBy: text('statusUpdatedBy'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 }),
	phoneNumber: text('phoneNumber').notNull().unique(),
	pancardNumber: text('pancardNumber').notNull().unique(),
	adharcardNumber: text('adharcardNumber').unique(),
	reraNumber: text('reraNumber'),
	gstNumber: text('gstNumber'),
	OtpSentAt: timestamp('OtpSentAt', { precision: 3 }),
	verifiedAgent: boolean('verifiedAgent'),
	experience: text('experience'),
	propertiesSold: integer('propertiesSold'),
	rating: text('rating'),
	reviews: integer('reviews'),
	bio: text('bio'),
	preference: PropertyForEnum('preference'),
	userLocation: text('userLocation'),
	latitude: text('latitude'),
	longitude: text('longitude'),
	fileKey: text('fileKey'),
	filePublicUrl: text('filePublicUrl'),
	bgFileKey: text('bgFileKey'),
	bgFilePublicUrl: text('bgFilePublicUrl'),
	cloudinaryProfileImagePublicId: text('cloudinaryProfileImagePublicId'),
	cloudinaryProfileImageUrl: text('cloudinaryProfileImageUrl'),
	cloudinaryBgImagePublicId: text('cloudinaryBgImagePublicId'),
	cloudinaryBgImageUrl: text('cloudinaryBgImageUrl'),
	inviteCode: text('inviteCode').unique().default(sql`cuid(1)`),
	referredBy: text('referredBy'),
	referredByUserId: text('referredByUserId'),
	isOnline: boolean('isOnline').notNull(),
	lastActive: timestamp('lastActive', { precision: 3 }),
	totalViews: integer('totalViews').notNull(),
	lastNotificationCheckedAt: timestamp('lastNotificationCheckedAt', { precision: 3 }),
	companyId: text('companyId'),
	cityId: text('cityId'),
	onboardingStatus: boolean('onboardingStatus').notNull(),
	onboardingStep: ONBOARDING_STEP_ENUM('onboardingStep').notNull().default("STEP_1"),
	onboardingPreference: text('onboardingPreference'),
	walletBalanceInCents: integer('walletBalanceInCents').notNull()
}, (User) => ({
	'User_company_fkey': foreignKey({
		name: 'User_company_fkey',
		columns: [User.companyId],
		foreignColumns: [CompanyDetails.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'User_city_fkey': foreignKey({
		name: 'User_city_fkey',
		columns: [User.cityId],
		foreignColumns: [City.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const ProfileView = pgTable('ProfileView', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	viewedByUserId: text('viewedByUserId').notNull(),
	viewedToUserId: text('viewedToUserId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (ProfileView) => ({
	'ProfileView_viewedByUser_fkey': foreignKey({
		name: 'ProfileView_viewedByUser_fkey',
		columns: [ProfileView.viewedByUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ProfileView_viewedToUser_fkey': foreignKey({
		name: 'ProfileView_viewedToUser_fkey',
		columns: [ProfileView.viewedToUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Languages = pgTable('Languages', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 }),
	walletBalanceInCents: integer('walletBalanceInCents').notNull()
});

export const WalletTransaction = pgTable('WalletTransaction', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	message: text('message').notNull(),
	title: text('title').notNull().default("Referral Points"),
	amountInCents: integer('amountInCents').notNull(),
	userId: text('userId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (WalletTransaction) => ({
	'WalletTransaction_user_fkey': foreignKey({
		name: 'WalletTransaction_user_fkey',
		columns: [WalletTransaction.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Customer = pgTable('Customer', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	phoneNumber: text('phoneNumber').notNull().unique(),
	adharcardNumber: text('adharcardNumber').unique(),
	bio: text('bio'),
	profileImageKey: text('profileImageKey'),
	profileImagePublicUrl: text('profileImagePublicUrl'),
	cloudinaryImagePublicId: text('cloudinaryImagePublicId'),
	cloudinaryImagePublicUrl: text('cloudinaryImagePublicUrl'),
	active: boolean('active').notNull().default(true),
	emailVerified: timestamp('emailVerified', { precision: 3 }),
	onboardingStatus: boolean('onboardingStatus').notNull(),
	onboardingPreference: text('onboardingPreference'),
	locationAddress: text('locationAddress'),
	latitude: text('latitude'),
	longitude: text('longitude'),
	cityId: text('cityId'),
	inviteCode: text('inviteCode').unique().default(sql`cuid(1)`),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 })
}, (Customer) => ({
	'Customer_city_fkey': foreignKey({
		name: 'Customer_city_fkey',
		columns: [Customer.cityId],
		foreignColumns: [City.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const OperationArea = pgTable('OperationArea', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	areaLat: text('areaLat').notNull(),
	areaLng: text('areaLng').notNull(),
	areaGooglePlaceId: text('areaGooglePlaceId').notNull(),
	areaAddressComponents: jsonb('areaAddressComponents').notNull(),
	userId: text('userId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (OperationArea) => ({
	'OperationArea_user_fkey': foreignKey({
		name: 'OperationArea_user_fkey',
		columns: [OperationArea.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const CompanyDetails = pgTable('CompanyDetails', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	companyName: text('companyName').notNull(),
	companyLocation: text('companyLocation').notNull(),
	companyLatitude: text('companyLatitude').notNull(),
	companyLongitude: text('companyLongitude').notNull(),
	email: text('email').notNull(),
	phoneNumber: text('phoneNumber').notNull(),
	fax: text('fax'),
	about: text('about').notNull(),
	companyWebsiteLink: text('companyWebsiteLink'),
	cloudinaryCompanyLogoPublicId: text('cloudinaryCompanyLogoPublicId'),
	cloudinaryCompanyLogoUrl: text('cloudinaryCompanyLogoUrl'),
	fileKey: text('fileKey'),
	filePublicUrl: text('filePublicUrl'),
	adminUserId: text('adminUserId').notNull().unique()
}, (CompanyDetails) => ({
	'CompanyDetails_adminUser_fkey': foreignKey({
		name: 'CompanyDetails_adminUser_fkey',
		columns: [CompanyDetails.adminUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Account = pgTable('Account', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	type: text('type').notNull(),
	provider: text('provider').notNull(),
	providerAccountId: text('providerAccountId').notNull(),
	token_type: text('token_type'),
	refresh_token: text('refresh_token'),
	access_token: text('access_token'),
	id_token: text('id_token'),
	scope: text('scope'),
	session_state: text('session_state'),
	expires_at: integer('expires_at'),
	userId: text('userId').notNull()
}, (Account) => ({
	'Account_user_fkey': foreignKey({
		name: 'Account_user_fkey',
		columns: [Account.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Account_provider_providerAccountId_unique_idx': uniqueIndex('Account_provider_providerAccountId_key')
		.on(Account.provider, Account.providerAccountId)
}));

export const Session = pgTable('Session', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	sessionToken: text('sessionToken').notNull().unique(),
	userId: text('userId').notNull(),
	expires: timestamp('expires', { precision: 3 }).notNull()
}, (Session) => ({
	'Session_user_fkey': foreignKey({
		name: 'Session_user_fkey',
		columns: [Session.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Testimonials = pgTable('Testimonials', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	title: text('title').notNull(),
	description: text('description').notNull(),
	stars: integer('stars').notNull(),
	writerId: text('writerId').notNull()
}, (Testimonials) => ({
	'Testimonials_writer_fkey': foreignKey({
		name: 'Testimonials_writer_fkey',
		columns: [Testimonials.writerId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const CustomerTestimonials = pgTable('CustomerTestimonials', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	description: text('description').notNull(),
	fileKey: text('fileKey'),
	filePublicUrl: text('filePublicUrl'),
	rating: integer('rating').notNull(),
	cityId: text('cityId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (CustomerTestimonials) => ({
	'CustomerTestimonials_city_fkey': foreignKey({
		name: 'CustomerTestimonials_city_fkey',
		columns: [CustomerTestimonials.cityId],
		foreignColumns: [City.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Enquiries = pgTable('Enquiries', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	email: text('email').notNull(),
	phoneNumber: text('phoneNumber').notNull().unique(),
	enquiryType: EnquiryType('enquiryType').notNull(),
	verified: boolean('verified').notNull(),
	agentCode: text('agentCode'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull().defaultNow()
});

export const Amenities = pgTable('Amenities', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	fileKey: text('fileKey'),
	filePublicUrl: text('filePublicUrl'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
});

export const PropertyType = pgTable('PropertyType', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	categoryId: text('categoryId'),
	name: text('name').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (PropertyType) => ({
	'PropertyType_category_fkey': foreignKey({
		name: 'PropertyType_category_fkey',
		columns: [PropertyType.categoryId],
		foreignColumns: [PropertyCategories.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const PropertyCategories = pgTable('PropertyCategories', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull().unique(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	showBedrooms: PostPropertyFormFieldStatusEnum('showBedrooms').notNull().default("HIDE"),
	showBathrooms: PostPropertyFormFieldStatusEnum('showBathrooms').notNull().default("HIDE"),
	showSecurityDeposit: PostPropertyFormFieldStatusEnum('showSecurityDeposit').notNull().default("HIDE"),
	showAreaIn: PostPropertyFormFieldStatusEnum('showAreaIn').notNull().default("HIDE"),
	showArea: PostPropertyFormFieldStatusEnum('showArea').notNull().default("HIDE"),
	showAboutProperty: PostPropertyFormFieldStatusEnum('showAboutProperty').notNull().default("HIDE"),
	showPropertyAddress: PostPropertyFormFieldStatusEnum('showPropertyAddress').notNull().default("HIDE"),
	showPropertyLocation: PostPropertyFormFieldStatusEnum('showPropertyLocation').notNull().default("HIDE"),
	showUtilities: PostPropertyFormFieldStatusEnum('showUtilities').notNull().default("HIDE"),
	showSocietyName: PostPropertyFormFieldStatusEnum('showSocietyName').notNull().default("HIDE"),
	showBuildYear: PostPropertyFormFieldStatusEnum('showBuildYear').notNull().default("HIDE"),
	showPossessionState: PostPropertyFormFieldStatusEnum('showPossessionState').notNull().default("HIDE"),
	showAmenities: PostPropertyFormFieldStatusEnum('showAmenities').notNull().default("HIDE"),
	showFurnishing: PostPropertyFormFieldStatusEnum('showFurnishing').notNull().default("HIDE"),
	showFacing: PostPropertyFormFieldStatusEnum('showFacing').notNull().default("HIDE"),
	showTotalFloors: PostPropertyFormFieldStatusEnum('showTotalFloors').notNull().default("HIDE"),
	showFloorNumber: PostPropertyFormFieldStatusEnum('showFloorNumber').notNull().default("HIDE"),
	showCarParking: PostPropertyFormFieldStatusEnum('showCarParking').notNull().default("HIDE"),
	showPropertyState: PostPropertyFormFieldStatusEnum('showPropertyState').notNull().default("HIDE")
});

export const Utilities = pgTable('Utilities', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	utility: text('utility').notNull(),
	distanceInKm: decimal('distanceInKm', { precision: 65, scale: 30 }).notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	propertyId: text('propertyId').notNull()
}, (Utilities) => ({
	'Utilities_property_fkey': foreignKey({
		name: 'Utilities_property_fkey',
		columns: [Utilities.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const AreaUnit = pgTable('AreaUnit', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	shortForm: text('shortForm').notNull(),
	conversionMultiplyer: doublePrecision('conversionMultiplyer').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
});

export const PropertyMediaSection = pgTable('PropertyMediaSection', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	title: text('title').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	propertyId: text('propertyId').notNull()
}, (PropertyMediaSection) => ({
	'PropertyMediaSection_property_fkey': foreignKey({
		name: 'PropertyMediaSection_property_fkey',
		columns: [PropertyMediaSection.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const PropertyMedia = pgTable('PropertyMedia', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	fileKey: text('fileKey'),
	filePublicUrl: text('filePublicUrl'),
	mediaType: PropertyMediaTypeEnum('mediaType'),
	cloudinaryId: text('cloudinaryId'),
	cloudinaryUrl: text('cloudinaryUrl'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	propertyMediaSectionId: text('propertyMediaSectionId').notNull()
}, (PropertyMedia) => ({
	'PropertyMedia_propertyMediaSection_fkey': foreignKey({
		name: 'PropertyMedia_propertyMediaSection_fkey',
		columns: [PropertyMedia.propertyMediaSectionId],
		foreignColumns: [PropertyMediaSection.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Property = pgTable('Property', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	registeryFileKey: text('registeryFileKey'),
	propertyTitle: text('propertyTitle'),
	propertyFor: PropertyForEnum('propertyFor').notNull(),
	bedrooms: integer('bedrooms'),
	bathrooms: integer('bathrooms'),
	propertyPrice: decimal('propertyPrice', { precision: 65, scale: 30 }),
	securityDeposit: decimal('securityDeposit', { precision: 65, scale: 30 }),
	areaUnitId: text('areaUnitId'),
	area: doublePrecision('area'),
	areaInSqMeters: doublePrecision('areaInSqMeters'),
	aboutProperty: text('aboutProperty'),
	propertyAddress: text('propertyAddress'),
	propertyLatitude: doublePrecision('propertyLatitude'),
	propertyLongitude: doublePrecision('propertyLongitude'),
	propertyGooglePlaceId: text('propertyGooglePlaceId'),
	propertyAddressComponents: jsonb('propertyAddressComponents'),
	propertyMarkersLatLng: jsonb('propertyMarkersLatLng'),
	propertyLocation: text('propertyLocation'),
	societyOrLocalityName: text('societyOrLocalityName'),
	buildYear: integer('buildYear'),
	possessionState: PossessionStateEnum('possessionState'),
	furnishing: FurnishingEnum('furnishing'),
	totalFloors: integer('totalFloors'),
	floorNumber: integer('floorNumber'),
	carParking: integer('carParking'),
	facing: FacingEnum('facing'),
	propertyState: PropertyStateEnum('propertyState'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	propertyTypeId: text('propertyTypeId').notNull(),
	propertyStatus: PropertyStatusEnum('propertyStatus').default("PENDING"),
	statusUpdatedAt: timestamp('statusUpdatedAt', { precision: 3 }),
	statusUpdateRemarks: text('statusUpdateRemarks'),
	statusUpdatedBy: text('statusUpdatedBy'),
	userId: text('userId').notNull(),
	rating: text('rating'),
	review: text('review'),
	popularProperty: boolean('popularProperty'),
	soldAt: timestamp('soldAt', { precision: 3 }),
	totalViews: integer('totalViews').notNull(),
	propertyCategoryId: text('propertyCategoryId')
}, (Property) => ({
	'Property_areaUnit_fkey': foreignKey({
		name: 'Property_areaUnit_fkey',
		columns: [Property.areaUnitId],
		foreignColumns: [AreaUnit.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Property_propertyType_fkey': foreignKey({
		name: 'Property_propertyType_fkey',
		columns: [Property.propertyTypeId],
		foreignColumns: [PropertyType.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Property_user_fkey': foreignKey({
		name: 'Property_user_fkey',
		columns: [Property.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Property_PropertyCategory_fkey': foreignKey({
		name: 'Property_PropertyCategory_fkey',
		columns: [Property.propertyCategoryId],
		foreignColumns: [PropertyCategories.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Feedback = pgTable('Feedback', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	agentName: text('agentName').notNull(),
	companyName: text('companyName').notNull(),
	rating: integer('rating').notNull(),
	whatDoYouLikeAboutTheAgent: jsonb('whatDoYouLikeAboutTheAgent'),
	doYouHaveAnythingElseToAddAboutTheAgent: text('doYouHaveAnythingElseToAddAboutTheAgent').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
});

export const PartnerResume = pgTable('PartnerResume', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	phoneNumber: text('phoneNumber').notNull().unique(),
	jobRoleId: text('jobRoleId').notNull(),
	resumeKey: text('resumeKey').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (PartnerResume) => ({
	'PartnerResume_jobRole_fkey': foreignKey({
		name: 'PartnerResume_jobRole_fkey',
		columns: [PartnerResume.jobRoleId],
		foreignColumns: [JobRole.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const JobRole = pgTable('JobRole', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 })
});

export const DeleteAccount = pgTable('DeleteAccount', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	email: text('email').notNull(),
	phoneNumber: text('phoneNumber').notNull(),
	reason: text('reason').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	deletedAt: timestamp('deletedAt', { precision: 3 })
});

export const DeleteCustomerAccount = pgTable('DeleteCustomerAccount', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	email: text('email').notNull(),
	phoneNumber: text('phoneNumber').notNull(),
	reason: text('reason').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	deletedAt: timestamp('deletedAt', { precision: 3 })
});

export const Comments = pgTable('Comments', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	userId: text('userId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	deletedAt: timestamp('deletedAt', { precision: 3 }),
	description: text('description').notNull(),
	propertyId: text('propertyId').notNull()
}, (Comments) => ({
	'Comments_user_fkey': foreignKey({
		name: 'Comments_user_fkey',
		columns: [Comments.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Comments_property_fkey': foreignKey({
		name: 'Comments_property_fkey',
		columns: [Comments.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Faq = pgTable('Faq', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	question: text('question').notNull(),
	answer: text('answer').notNull(),
	order: integer('order').notNull(),
	project: ProjectEnum('project'),
	page: FaqPagesEnum('page'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
});

export const ConnectionRequests = pgTable('ConnectionRequests', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	senderId: text('senderId').notNull(),
	receiverId: text('receiverId').notNull(),
	status: ConnectionRequestsEnum('status').notNull().default("PENDING"),
	senderUnseenMessagesCount: integer('senderUnseenMessagesCount').notNull(),
	receiverUnseenMessagesCount: integer('receiverUnseenMessagesCount').notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 }),
	blocked: boolean('blocked').notNull(),
	blockReason: text('blockReason'),
	blockByUserId: text('blockByUserId'),
	propertyId: text('propertyId'),
	ratingId: text('ratingId'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (ConnectionRequests) => ({
	'ConnectionRequests_blockByUser_fkey': foreignKey({
		name: 'ConnectionRequests_blockByUser_fkey',
		columns: [ConnectionRequests.blockByUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ConnectionRequests_property_fkey': foreignKey({
		name: 'ConnectionRequests_property_fkey',
		columns: [ConnectionRequests.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ConnectionRequests_sender_fkey': foreignKey({
		name: 'ConnectionRequests_sender_fkey',
		columns: [ConnectionRequests.senderId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ConnectionRequests_receiver_fkey': foreignKey({
		name: 'ConnectionRequests_receiver_fkey',
		columns: [ConnectionRequests.receiverId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Message = pgTable('Message', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	content: text('content').notNull(),
	read: boolean('read').notNull(),
	senderId: text('senderId').notNull(),
	connectionId: text('connectionId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (Message) => ({
	'Message_connection_fkey': foreignKey({
		name: 'Message_connection_fkey',
		columns: [Message.connectionId],
		foreignColumns: [ConnectionRequests.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Favourites = pgTable('Favourites', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	userId: text('userId').notNull(),
	propertyId: text('propertyId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (Favourites) => ({
	'Favourites_property_fkey': foreignKey({
		name: 'Favourites_property_fkey',
		columns: [Favourites.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Favourites_user_fkey': foreignKey({
		name: 'Favourites_user_fkey',
		columns: [Favourites.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const LikedAgents = pgTable('LikedAgents', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	userId: text('userId').notNull(),
	likedAgentId: text('likedAgentId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (LikedAgents) => ({
	'LikedAgents_user_fkey': foreignKey({
		name: 'LikedAgents_user_fkey',
		columns: [LikedAgents.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const LikedAgentsByCustomer = pgTable('LikedAgentsByCustomer', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	customerId: text('customerId').notNull(),
	likedAgentId: text('likedAgentId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (LikedAgentsByCustomer) => ({
	'LikedAgentsByCustomer_customer_fkey': foreignKey({
		name: 'LikedAgentsByCustomer_customer_fkey',
		columns: [LikedAgentsByCustomer.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'LikedAgentsByCustomer_likedAgent_fkey': foreignKey({
		name: 'LikedAgentsByCustomer_likedAgent_fkey',
		columns: [LikedAgentsByCustomer.likedAgentId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const RecentlyViewed = pgTable('RecentlyViewed', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	userId: text('userId').notNull(),
	propertyId: text('propertyId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (RecentlyViewed) => ({
	'RecentlyViewed_user_fkey': foreignKey({
		name: 'RecentlyViewed_user_fkey',
		columns: [RecentlyViewed.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'RecentlyViewed_property_fkey': foreignKey({
		name: 'RecentlyViewed_property_fkey',
		columns: [RecentlyViewed.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const News = pgTable('News', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	title: text('title').notNull(),
	description: text('description').notNull(),
	fileKey: text('fileKey').notNull(),
	filePublicUrl: text('filePublicUrl').notNull(),
	redirectUrl: text('redirectUrl').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
});

export const Rating = pgTable('Rating', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	propertyStarsCount: integer('propertyStarsCount'),
	propertyRatingMessage: text('propertyRatingMessage'),
	userStarsCount: integer('userStarsCount').notNull(),
	userRatingMessage: text('userRatingMessage').notNull(),
	propertyId: text('propertyId'),
	ratedByUserId: text('ratedByUserId').notNull(),
	ratedToUserId: text('ratedToUserId').notNull(),
	connectionId: text('connectionId'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (Rating) => ({
	'Rating_connection_fkey': foreignKey({
		name: 'Rating_connection_fkey',
		columns: [Rating.connectionId],
		foreignColumns: [ConnectionRequests.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Rating_property_fkey': foreignKey({
		name: 'Rating_property_fkey',
		columns: [Rating.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Rating_ratedTo_fkey': foreignKey({
		name: 'Rating_ratedTo_fkey',
		columns: [Rating.ratedToUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Rating_ratedBy_fkey': foreignKey({
		name: 'Rating_ratedBy_fkey',
		columns: [Rating.ratedByUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Notification = pgTable('Notification', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	title: text('title').notNull(),
	description: text('description').notNull(),
	type: NotificationEnum('type').notNull(),
	metaData: jsonb('metaData').notNull(),
	customerId: text('customerId'),
	receiverId: text('receiverId'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (Notification) => ({
	'Notification_customer_fkey': foreignKey({
		name: 'Notification_customer_fkey',
		columns: [Notification.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Notification_receiver_fkey': foreignKey({
		name: 'Notification_receiver_fkey',
		columns: [Notification.receiverId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Post = pgTable('Post', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	content: text('content').notNull(),
	totalLikes: integer('totalLikes').notNull(),
	totalComments: integer('totalComments').notNull(),
	reportCount: integer('reportCount').notNull(),
	userId: text('userId').notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 }),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (Post) => ({
	'Post_user_fkey': foreignKey({
		name: 'Post_user_fkey',
		columns: [Post.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const ReportedPost = pgTable('ReportedPost', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	reason: text('reason').notNull(),
	userId: text('userId'),
	customerId: text('customerId'),
	postId: text('postId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 })
}, (ReportedPost) => ({
	'ReportedPost_user_fkey': foreignKey({
		name: 'ReportedPost_user_fkey',
		columns: [ReportedPost.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ReportedPost_customer_fkey': foreignKey({
		name: 'ReportedPost_customer_fkey',
		columns: [ReportedPost.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ReportedPost_post_fkey': foreignKey({
		name: 'ReportedPost_post_fkey',
		columns: [ReportedPost.postId],
		foreignColumns: [Post.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const PostsMedia = pgTable('PostsMedia', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	fileKey: text('fileKey'),
	filePublicUrl: text('filePublicUrl'),
	cloudinaryId: text('cloudinaryId'),
	cloudinaryUrl: text('cloudinaryUrl'),
	mediaType: PostsMediaTypeEnum('mediaType').notNull(),
	postId: text('postId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (PostsMedia) => ({
	'PostsMedia_post_fkey': foreignKey({
		name: 'PostsMedia_post_fkey',
		columns: [PostsMedia.postId],
		foreignColumns: [Post.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Like = pgTable('Like', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	userId: text('userId'),
	customerId: text('customerId'),
	postId: text('postId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow()
}, (Like) => ({
	'Like_post_fkey': foreignKey({
		name: 'Like_post_fkey',
		columns: [Like.postId],
		foreignColumns: [Post.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Like_user_fkey': foreignKey({
		name: 'Like_user_fkey',
		columns: [Like.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Like_customer_fkey': foreignKey({
		name: 'Like_customer_fkey',
		columns: [Like.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const Comment = pgTable('Comment', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	comment: text('comment').notNull(),
	isPinned: boolean('isPinned').notNull(),
	userId: text('userId'),
	customerId: text('customerId'),
	postId: text('postId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	deletedAt: timestamp('deletedAt', { precision: 3 })
}, (Comment) => ({
	'Comment_user_fkey': foreignKey({
		name: 'Comment_user_fkey',
		columns: [Comment.userId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Comment_customer_fkey': foreignKey({
		name: 'Comment_customer_fkey',
		columns: [Comment.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'Comment_post_fkey': foreignKey({
		name: 'Comment_post_fkey',
		columns: [Comment.postId],
		foreignColumns: [Post.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const City = pgTable('City', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	name: text('name').notNull(),
	cityMarkersLatLng: jsonb('cityMarkersLatLng').notNull(),
	northMaxLat: doublePrecision('northMaxLat'),
	southMaxLat: doublePrecision('southMaxLat'),
	eastMaxLng: doublePrecision('eastMaxLng'),
	westMaxLng: doublePrecision('westMaxLng'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
});

export const CustomerFavourites = pgTable('CustomerFavourites', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	customerId: text('customerId').notNull(),
	propertyId: text('propertyId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (CustomerFavourites) => ({
	'CustomerFavourites_property_fkey': foreignKey({
		name: 'CustomerFavourites_property_fkey',
		columns: [CustomerFavourites.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerFavourites_user_fkey': foreignKey({
		name: 'CustomerFavourites_user_fkey',
		columns: [CustomerFavourites.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const CustomerPropertyEnquiryForm = pgTable('CustomerPropertyEnquiryForm', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	propertyCategoryId: text('propertyCategoryId').notNull(),
	propertyTypeId: text('propertyTypeId').notNull(),
	cityId: text('cityId').notNull(),
	propertyPrice: bigint('propertyPrice', { mode: 'bigint' }).array().notNull(),
	propertyPossession: PossessionStateEnum('propertyPossession').notNull(),
	propertyRequirement: text('propertyRequirement'),
	customerId: text('customerId').notNull()
}, (CustomerPropertyEnquiryForm) => ({
	'CustomerPropertyEnquiryForm_propertyCategory_fkey': foreignKey({
		name: 'CustomerPropertyEnquiryForm_propertyCategory_fkey',
		columns: [CustomerPropertyEnquiryForm.propertyCategoryId],
		foreignColumns: [PropertyCategories.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerPropertyEnquiryForm_propertyType_fkey': foreignKey({
		name: 'CustomerPropertyEnquiryForm_propertyType_fkey',
		columns: [CustomerPropertyEnquiryForm.propertyTypeId],
		foreignColumns: [PropertyType.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerPropertyEnquiryForm_city_fkey': foreignKey({
		name: 'CustomerPropertyEnquiryForm_city_fkey',
		columns: [CustomerPropertyEnquiryForm.cityId],
		foreignColumns: [City.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerPropertyEnquiryForm_customer_fkey': foreignKey({
		name: 'CustomerPropertyEnquiryForm_customer_fkey',
		columns: [CustomerPropertyEnquiryForm.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const CustomerRatingsToAgents = pgTable('CustomerRatingsToAgents', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	propertyStarsCount: integer('propertyStarsCount'),
	propertyRatingMessage: text('propertyRatingMessage'),
	userStarsCount: integer('userStarsCount').notNull(),
	userRatingMessage: text('userRatingMessage').notNull(),
	fileKey: text('fileKey'),
	filePublicUrl: text('filePublicUrl'),
	cloudinaryPublicId: text('cloudinaryPublicId'),
	cloudinaryUrl: text('cloudinaryUrl'),
	propertyId: text('propertyId'),
	ratedByUserId: text('ratedByUserId').notNull(),
	ratedToUserId: text('ratedToUserId').notNull(),
	connectionId: text('connectionId'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (CustomerRatingsToAgents) => ({
	'CustomerRatingsToAgents_connection_fkey': foreignKey({
		name: 'CustomerRatingsToAgents_connection_fkey',
		columns: [CustomerRatingsToAgents.connectionId],
		foreignColumns: [CustomerAgentConnections.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerRatingsToAgents_property_fkey': foreignKey({
		name: 'CustomerRatingsToAgents_property_fkey',
		columns: [CustomerRatingsToAgents.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerRatingsToAgents_ratedTo_fkey': foreignKey({
		name: 'CustomerRatingsToAgents_ratedTo_fkey',
		columns: [CustomerRatingsToAgents.ratedToUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerRatingsToAgents_ratedBy_fkey': foreignKey({
		name: 'CustomerRatingsToAgents_ratedBy_fkey',
		columns: [CustomerRatingsToAgents.ratedByUserId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const CustomerAgentConnectionMessages = pgTable('CustomerAgentConnectionMessages', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	content: text('content').notNull(),
	read: boolean('read').notNull(),
	senderId: text('senderId').notNull(),
	connectionId: text('connectionId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (CustomerAgentConnectionMessages) => ({
	'CustomerAgentConnectionMessages_connection_fkey': foreignKey({
		name: 'CustomerAgentConnectionMessages_connection_fkey',
		columns: [CustomerAgentConnectionMessages.connectionId],
		foreignColumns: [CustomerAgentConnections.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const CustomerAgentConnections = pgTable('CustomerAgentConnections', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	customerId: text('customerId').notNull(),
	agentId: text('agentId').notNull(),
	state: CustomerAgentConnectionChatStateEnum('state').notNull().default("PENDING"),
	customerUnseenMessagesCount: integer('customerUnseenMessagesCount').notNull(),
	agentUnseenMessagesCount: integer('agentUnseenMessagesCount').notNull(),
	deletedAt: timestamp('deletedAt', { precision: 3 }),
	propertyId: text('propertyId'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (CustomerAgentConnections) => ({
	'CustomerAgentConnections_property_fkey': foreignKey({
		name: 'CustomerAgentConnections_property_fkey',
		columns: [CustomerAgentConnections.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerAgentConnections_customer_fkey': foreignKey({
		name: 'CustomerAgentConnections_customer_fkey',
		columns: [CustomerAgentConnections.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerAgentConnections_agent_fkey': foreignKey({
		name: 'CustomerAgentConnections_agent_fkey',
		columns: [CustomerAgentConnections.agentId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const CustomerPropertyViewingHistory = pgTable('CustomerPropertyViewingHistory', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	customerId: text('customerId').notNull(),
	propertyId: text('propertyId').notNull(),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (CustomerPropertyViewingHistory) => ({
	'CustomerPropertyViewingHistory_user_fkey': foreignKey({
		name: 'CustomerPropertyViewingHistory_user_fkey',
		columns: [CustomerPropertyViewingHistory.customerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'CustomerPropertyViewingHistory_property_fkey': foreignKey({
		name: 'CustomerPropertyViewingHistory_property_fkey',
		columns: [CustomerPropertyViewingHistory.propertyId],
		foreignColumns: [Property.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const ReportedUserFromChat = pgTable('ReportedUserFromChat', {
	id: text('id').notNull().primaryKey().default(sql`cuid(1)`),
	reportingUserId: text('reportingUserId'),
	reportedUserId: text('reportedUserId').notNull(),
	reasonForReporting: text('reasonForReporting').notNull(),
	reportingCustomerId: text('reportingCustomerId'),
	createdAt: timestamp('createdAt', { precision: 3 }).notNull().defaultNow(),
	updatedAt: timestamp('updatedAt', { precision: 3 }).notNull()
}, (ReportedUserFromChat) => ({
	'ReportedUserFromChat_reportingUser_fkey': foreignKey({
		name: 'ReportedUserFromChat_reportingUser_fkey',
		columns: [ReportedUserFromChat.reportingUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ReportedUserFromChat_reportedUser_fkey': foreignKey({
		name: 'ReportedUserFromChat_reportedUser_fkey',
		columns: [ReportedUserFromChat.reportedUserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'ReportedUserFromChat_reportingCustomer_fkey': foreignKey({
		name: 'ReportedUserFromChat_reportingCustomer_fkey',
		columns: [ReportedUserFromChat.reportingCustomerId],
		foreignColumns: [Customer.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const LanguagesToUser = pgTable('_LanguagesToUser', {
	UserId: text('A').notNull(),
	LanguagesId: text('B').notNull()
}, (LanguagesToUser) => ({
	'_LanguagesToUser_User_fkey': foreignKey({
		name: '_LanguagesToUser_User_fkey',
		columns: [LanguagesToUser.UserId],
		foreignColumns: [User.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade'),
	'_LanguagesToUser_Languages_fkey': foreignKey({
		name: '_LanguagesToUser_Languages_fkey',
		columns: [LanguagesToUser.LanguagesId],
		foreignColumns: [Languages.id]
	})
		.onDelete('cascade')
		.onUpdate('cascade')
}));

export const UserRelations = relations(User, ({ many, one }) => ({
	Account: many(Account, {
		relationName: 'AccountToUser'
	}),
	Session: many(Session, {
		relationName: 'SessionToUser'
	}),
	operationArea: many(OperationArea, {
		relationName: 'OperationAreaToUser'
	}),
	testimonials: many(Testimonials, {
		relationName: 'TestimonialsToUser'
	}),
	properties: many(Property, {
		relationName: 'PropertyToUser'
	}),
	comments: many(Comments, {
		relationName: 'CommentsToUser'
	}),
	sentConnectionRequests: many(ConnectionRequests, {
		relationName: 'sender'
	}),
	receivedConnectionRequests: many(ConnectionRequests, {
		relationName: 'receiver'
	}),
	block: many(ConnectionRequests, {
		relationName: 'block'
	}),
	coustomerConnections: many(CustomerAgentConnections, {
		relationName: 'CustomerAgentConnectionsToUser'
	}),
	companyDetails: many(CompanyDetails, {
		relationName: 'adminUser'
	}),
	company: one(CompanyDetails, {
		relationName: 'user',
		fields: [User.companyId],
		references: [CompanyDetails.id]
	}),
	Favourites: many(Favourites, {
		relationName: 'FavouritesToUser'
	}),
	likedAgents: many(LikedAgents, {
		relationName: 'LikedAgentsToUser'
	}),
	recentlyViewed: many(RecentlyViewed, {
		relationName: 'RecentlyViewedToUser'
	}),
	viewedByUsers: many(ProfileView, {
		relationName: 'viewedByUser'
	}),
	viewedToUsers: many(ProfileView, {
		relationName: 'viewedToUser'
	}),
	ratingFromConversations: many(Rating, {
		relationName: 'ratedBy'
	}),
	ratedToConversations: many(Rating, {
		relationName: 'ratedTo'
	}),
	notifications: many(Notification, {
		relationName: 'NotificationToUser'
	}),
	posts: many(Post, {
		relationName: 'PostToUser'
	}),
	likes: many(Like, {
		relationName: 'LikeToUser'
	}),
	postComments: many(Comment, {
		relationName: 'CommentToUser'
	}),
	city: one(City, {
		relationName: 'CityToUser',
		fields: [User.cityId],
		references: [City.id]
	}),
	customerRatingsToAgents: many(CustomerRatingsToAgents, {
		relationName: 'CustomerRatingsToAgentsToUser'
	}),
	reportedPost: many(ReportedPost, {
		relationName: 'ReportedPostToUser'
	}),
	walletTransactions: many(WalletTransaction, {
		relationName: 'UserToWalletTransaction'
	}),
	languages: many(LanguagesToUser, {
		relationName: 'UserToLanguagesToUser'
	}),
	LikedAgentsByCustomer: many(LikedAgentsByCustomer, {
		relationName: 'LikedAgentsByCustomerToUser'
	}),
	reportedToUsersFromChat: many(ReportedUserFromChat, {
		relationName: 'reportedUser'
	}),
	reportedByUsersFromChat: many(ReportedUserFromChat, {
		relationName: 'reportingUser'
	})
}));

export const ProfileViewRelations = relations(ProfileView, ({ one }) => ({
	viewedByUser: one(User, {
		relationName: 'viewedByUser',
		fields: [ProfileView.viewedByUserId],
		references: [User.id]
	}),
	viewedToUser: one(User, {
		relationName: 'viewedToUser',
		fields: [ProfileView.viewedToUserId],
		references: [User.id]
	})
}));

export const LanguagesRelations = relations(Languages, ({ many }) => ({
	user: many(LanguagesToUser, {
		relationName: 'LanguagesToLanguagesToUser'
	})
}));

export const WalletTransactionRelations = relations(WalletTransaction, ({ one }) => ({
	user: one(User, {
		relationName: 'UserToWalletTransaction',
		fields: [WalletTransaction.userId],
		references: [User.id]
	})
}));

export const CustomerRelations = relations(Customer, ({ one, many }) => ({
	city: one(City, {
		relationName: 'CityToCustomer',
		fields: [Customer.cityId],
		references: [City.id]
	}),
	customerPropertyEnquiryForm: many(CustomerPropertyEnquiryForm, {
		relationName: 'CustomerToCustomerPropertyEnquiryForm'
	}),
	favouriteProperties: many(CustomerFavourites, {
		relationName: 'CustomerToCustomerFavourites'
	}),
	connections: many(CustomerAgentConnections, {
		relationName: 'CustomerToCustomerAgentConnections'
	}),
	customerRatingsToAgents: many(CustomerRatingsToAgents, {
		relationName: 'CustomerToCustomerRatingsToAgents'
	}),
	customerPropertyViewingHistory: many(CustomerPropertyViewingHistory, {
		relationName: 'CustomerToCustomerPropertyViewingHistory'
	}),
	LikedAgentsByCustomer: many(LikedAgentsByCustomer, {
		relationName: 'CustomerToLikedAgentsByCustomer'
	}),
	notifications: many(Notification, {
		relationName: 'CustomerToNotification'
	}),
	likes: many(Like, {
		relationName: 'CustomerToLike'
	}),
	postComments: many(Comment, {
		relationName: 'CommentToCustomer'
	}),
	reportedPost: many(ReportedPost, {
		relationName: 'CustomerToReportedPost'
	}),
	ReportedUserFromChat: many(ReportedUserFromChat, {
		relationName: 'CustomerToReportedUserFromChat'
	})
}));

export const OperationAreaRelations = relations(OperationArea, ({ one }) => ({
	user: one(User, {
		relationName: 'OperationAreaToUser',
		fields: [OperationArea.userId],
		references: [User.id]
	})
}));

export const CompanyDetailsRelations = relations(CompanyDetails, ({ one, many }) => ({
	adminUser: one(User, {
		relationName: 'adminUser',
		fields: [CompanyDetails.adminUserId],
		references: [User.id]
	}),
	companyAgents: many(User, {
		relationName: 'user'
	})
}));

export const AccountRelations = relations(Account, ({ one }) => ({
	user: one(User, {
		relationName: 'AccountToUser',
		fields: [Account.userId],
		references: [User.id]
	})
}));

export const SessionRelations = relations(Session, ({ one }) => ({
	user: one(User, {
		relationName: 'SessionToUser',
		fields: [Session.userId],
		references: [User.id]
	})
}));

export const TestimonialsRelations = relations(Testimonials, ({ one }) => ({
	writer: one(User, {
		relationName: 'TestimonialsToUser',
		fields: [Testimonials.writerId],
		references: [User.id]
	})
}));

export const CustomerTestimonialsRelations = relations(CustomerTestimonials, ({ one }) => ({
	city: one(City, {
		relationName: 'CityToCustomerTestimonials',
		fields: [CustomerTestimonials.cityId],
		references: [City.id]
	})
}));

export const AmenitiesRelations = relations(Amenities, ({ many }) => ({
	properties: many(Property, {
		relationName: 'AmenitiesToProperty'
	})
}));

export const PropertyTypeRelations = relations(PropertyType, ({ one, many }) => ({
	category: one(PropertyCategories, {
		relationName: 'PropertyCategoriesToPropertyType',
		fields: [PropertyType.categoryId],
		references: [PropertyCategories.id]
	}),
	properties: many(Property, {
		relationName: 'PropertyToPropertyType'
	}),
	customerPropertyEnquiryForm: many(CustomerPropertyEnquiryForm, {
		relationName: 'CustomerPropertyEnquiryFormToPropertyType'
	})
}));

export const PropertyCategoriesRelations = relations(PropertyCategories, ({ many }) => ({
	properties: many(Property, {
		relationName: 'PropertyToPropertyCategories'
	}),
	PropertyTypes: many(PropertyType, {
		relationName: 'PropertyCategoriesToPropertyType'
	}),
	customerPropertyEnquiryForm: many(CustomerPropertyEnquiryForm, {
		relationName: 'CustomerPropertyEnquiryFormToPropertyCategories'
	}),
	AreaUnit: many(AreaUnit, {
		relationName: 'AreaUnitToPropertyCategories'
	})
}));

export const UtilitiesRelations = relations(Utilities, ({ one }) => ({
	property: one(Property, {
		relationName: 'PropertyToUtilities',
		fields: [Utilities.propertyId],
		references: [Property.id]
	})
}));

export const AreaUnitRelations = relations(AreaUnit, ({ many }) => ({
	category: many(PropertyCategories, {
		relationName: 'AreaUnitToPropertyCategories'
	}),
	Property: many(Property, {
		relationName: 'AreaUnitToProperty'
	})
}));

export const PropertyMediaSectionRelations = relations(PropertyMediaSection, ({ one, many }) => ({
	property: one(Property, {
		relationName: 'PropertyToPropertyMediaSection',
		fields: [PropertyMediaSection.propertyId],
		references: [Property.id]
	}),
	media: many(PropertyMedia, {
		relationName: 'PropertyMediaToPropertyMediaSection'
	})
}));

export const PropertyMediaRelations = relations(PropertyMedia, ({ one }) => ({
	propertyMediaSection: one(PropertyMediaSection, {
		relationName: 'PropertyMediaToPropertyMediaSection',
		fields: [PropertyMedia.propertyMediaSectionId],
		references: [PropertyMediaSection.id]
	})
}));

export const PropertyRelations = relations(Property, ({ one, many }) => ({
	areaUnit: one(AreaUnit, {
		relationName: 'AreaUnitToProperty',
		fields: [Property.areaUnitId],
		references: [AreaUnit.id]
	}),
	utilities: many(Utilities, {
		relationName: 'PropertyToUtilities'
	}),
	amenities: many(Amenities, {
		relationName: 'AmenitiesToProperty'
	}),
	mediaSections: many(PropertyMediaSection, {
		relationName: 'PropertyToPropertyMediaSection'
	}),
	comments: many(Comments, {
		relationName: 'CommentsToProperty'
	}),
	propertyType: one(PropertyType, {
		relationName: 'PropertyToPropertyType',
		fields: [Property.propertyTypeId],
		references: [PropertyType.id]
	}),
	user: one(User, {
		relationName: 'PropertyToUser',
		fields: [Property.userId],
		references: [User.id]
	}),
	connections: many(ConnectionRequests, {
		relationName: 'ConnectionRequestsToProperty'
	}),
	Favourites: many(Favourites, {
		relationName: 'FavouritesToProperty'
	}),
	customerFavourites: many(CustomerFavourites, {
		relationName: 'CustomerFavouritesToProperty'
	}),
	viewedByUsers: many(RecentlyViewed, {
		relationName: 'PropertyToRecentlyViewed'
	}),
	ratingFromConversation: many(Rating, {
		relationName: 'PropertyToRating'
	}),
	PropertyCategory: one(PropertyCategories, {
		relationName: 'PropertyToPropertyCategories',
		fields: [Property.propertyCategoryId],
		references: [PropertyCategories.id]
	}),
	customerAgentConnections: many(CustomerAgentConnections, {
		relationName: 'CustomerAgentConnectionsToProperty'
	}),
	CustomerRatingsToAgents: many(CustomerRatingsToAgents, {
		relationName: 'CustomerRatingsToAgentsToProperty'
	}),
	customerPropertyViewingHistory: many(CustomerPropertyViewingHistory, {
		relationName: 'CustomerPropertyViewingHistoryToProperty'
	})
}));

export const PartnerResumeRelations = relations(PartnerResume, ({ one }) => ({
	jobRole: one(JobRole, {
		relationName: 'JobRoleToPartnerResume',
		fields: [PartnerResume.jobRoleId],
		references: [JobRole.id]
	})
}));

export const JobRoleRelations = relations(JobRole, ({ many }) => ({
	partnerResume: many(PartnerResume, {
		relationName: 'JobRoleToPartnerResume'
	})
}));

export const CommentsRelations = relations(Comments, ({ one }) => ({
	user: one(User, {
		relationName: 'CommentsToUser',
		fields: [Comments.userId],
		references: [User.id]
	}),
	property: one(Property, {
		relationName: 'CommentsToProperty',
		fields: [Comments.propertyId],
		references: [Property.id]
	})
}));

export const ConnectionRequestsRelations = relations(ConnectionRequests, ({ one, many }) => ({
	blockByUser: one(User, {
		relationName: 'block',
		fields: [ConnectionRequests.blockByUserId],
		references: [User.id]
	}),
	property: one(Property, {
		relationName: 'ConnectionRequestsToProperty',
		fields: [ConnectionRequests.propertyId],
		references: [Property.id]
	}),
	rating: many(Rating, {
		relationName: 'ConnectionRequestsToRating'
	}),
	sender: one(User, {
		relationName: 'sender',
		fields: [ConnectionRequests.senderId],
		references: [User.id]
	}),
	receiver: one(User, {
		relationName: 'receiver',
		fields: [ConnectionRequests.receiverId],
		references: [User.id]
	}),
	messages: many(Message, {
		relationName: 'ConnectionRequestsToMessage'
	})
}));

export const MessageRelations = relations(Message, ({ one }) => ({
	connection: one(ConnectionRequests, {
		relationName: 'ConnectionRequestsToMessage',
		fields: [Message.connectionId],
		references: [ConnectionRequests.id]
	})
}));

export const FavouritesRelations = relations(Favourites, ({ one }) => ({
	property: one(Property, {
		relationName: 'FavouritesToProperty',
		fields: [Favourites.propertyId],
		references: [Property.id]
	}),
	user: one(User, {
		relationName: 'FavouritesToUser',
		fields: [Favourites.userId],
		references: [User.id]
	})
}));

export const LikedAgentsRelations = relations(LikedAgents, ({ one }) => ({
	user: one(User, {
		relationName: 'LikedAgentsToUser',
		fields: [LikedAgents.userId],
		references: [User.id]
	})
}));

export const LikedAgentsByCustomerRelations = relations(LikedAgentsByCustomer, ({ one }) => ({
	customer: one(Customer, {
		relationName: 'CustomerToLikedAgentsByCustomer',
		fields: [LikedAgentsByCustomer.customerId],
		references: [Customer.id]
	}),
	likedAgent: one(User, {
		relationName: 'LikedAgentsByCustomerToUser',
		fields: [LikedAgentsByCustomer.likedAgentId],
		references: [User.id]
	})
}));

export const RecentlyViewedRelations = relations(RecentlyViewed, ({ one }) => ({
	user: one(User, {
		relationName: 'RecentlyViewedToUser',
		fields: [RecentlyViewed.userId],
		references: [User.id]
	}),
	property: one(Property, {
		relationName: 'PropertyToRecentlyViewed',
		fields: [RecentlyViewed.propertyId],
		references: [Property.id]
	})
}));

export const RatingRelations = relations(Rating, ({ one }) => ({
	connection: one(ConnectionRequests, {
		relationName: 'ConnectionRequestsToRating',
		fields: [Rating.connectionId],
		references: [ConnectionRequests.id]
	}),
	property: one(Property, {
		relationName: 'PropertyToRating',
		fields: [Rating.propertyId],
		references: [Property.id]
	}),
	ratedTo: one(User, {
		relationName: 'ratedTo',
		fields: [Rating.ratedToUserId],
		references: [User.id]
	}),
	ratedBy: one(User, {
		relationName: 'ratedBy',
		fields: [Rating.ratedByUserId],
		references: [User.id]
	})
}));

export const NotificationRelations = relations(Notification, ({ one }) => ({
	customer: one(Customer, {
		relationName: 'CustomerToNotification',
		fields: [Notification.customerId],
		references: [Customer.id]
	}),
	receiver: one(User, {
		relationName: 'NotificationToUser',
		fields: [Notification.receiverId],
		references: [User.id]
	})
}));

export const PostRelations = relations(Post, ({ many, one }) => ({
	media: many(PostsMedia, {
		relationName: 'PostToPostsMedia'
	}),
	likes: many(Like, {
		relationName: 'LikeToPost'
	}),
	comments: many(Comment, {
		relationName: 'CommentToPost'
	}),
	user: one(User, {
		relationName: 'PostToUser',
		fields: [Post.userId],
		references: [User.id]
	}),
	reportedPost: many(ReportedPost, {
		relationName: 'PostToReportedPost'
	})
}));

export const ReportedPostRelations = relations(ReportedPost, ({ one }) => ({
	user: one(User, {
		relationName: 'ReportedPostToUser',
		fields: [ReportedPost.userId],
		references: [User.id]
	}),
	customer: one(Customer, {
		relationName: 'CustomerToReportedPost',
		fields: [ReportedPost.customerId],
		references: [Customer.id]
	}),
	post: one(Post, {
		relationName: 'PostToReportedPost',
		fields: [ReportedPost.postId],
		references: [Post.id]
	})
}));

export const PostsMediaRelations = relations(PostsMedia, ({ one }) => ({
	post: one(Post, {
		relationName: 'PostToPostsMedia',
		fields: [PostsMedia.postId],
		references: [Post.id]
	})
}));

export const LikeRelations = relations(Like, ({ one }) => ({
	post: one(Post, {
		relationName: 'LikeToPost',
		fields: [Like.postId],
		references: [Post.id]
	}),
	user: one(User, {
		relationName: 'LikeToUser',
		fields: [Like.userId],
		references: [User.id]
	}),
	customer: one(Customer, {
		relationName: 'CustomerToLike',
		fields: [Like.customerId],
		references: [Customer.id]
	})
}));

export const CommentRelations = relations(Comment, ({ one }) => ({
	user: one(User, {
		relationName: 'CommentToUser',
		fields: [Comment.userId],
		references: [User.id]
	}),
	customer: one(Customer, {
		relationName: 'CommentToCustomer',
		fields: [Comment.customerId],
		references: [Customer.id]
	}),
	post: one(Post, {
		relationName: 'CommentToPost',
		fields: [Comment.postId],
		references: [Post.id]
	})
}));

export const CityRelations = relations(City, ({ many }) => ({
	customers: many(Customer, {
		relationName: 'CityToCustomer'
	}),
	users: many(User, {
		relationName: 'CityToUser'
	}),
	customerPropertyUniqueForm: many(CustomerPropertyEnquiryForm, {
		relationName: 'CityToCustomerPropertyEnquiryForm'
	}),
	CustomerTestimonials: many(CustomerTestimonials, {
		relationName: 'CityToCustomerTestimonials'
	})
}));

export const CustomerFavouritesRelations = relations(CustomerFavourites, ({ one }) => ({
	property: one(Property, {
		relationName: 'CustomerFavouritesToProperty',
		fields: [CustomerFavourites.propertyId],
		references: [Property.id]
	}),
	user: one(Customer, {
		relationName: 'CustomerToCustomerFavourites',
		fields: [CustomerFavourites.customerId],
		references: [Customer.id]
	})
}));

export const CustomerPropertyEnquiryFormRelations = relations(CustomerPropertyEnquiryForm, ({ one }) => ({
	propertyCategory: one(PropertyCategories, {
		relationName: 'CustomerPropertyEnquiryFormToPropertyCategories',
		fields: [CustomerPropertyEnquiryForm.propertyCategoryId],
		references: [PropertyCategories.id]
	}),
	propertyType: one(PropertyType, {
		relationName: 'CustomerPropertyEnquiryFormToPropertyType',
		fields: [CustomerPropertyEnquiryForm.propertyTypeId],
		references: [PropertyType.id]
	}),
	city: one(City, {
		relationName: 'CityToCustomerPropertyEnquiryForm',
		fields: [CustomerPropertyEnquiryForm.cityId],
		references: [City.id]
	}),
	customer: one(Customer, {
		relationName: 'CustomerToCustomerPropertyEnquiryForm',
		fields: [CustomerPropertyEnquiryForm.customerId],
		references: [Customer.id]
	})
}));

export const CustomerRatingsToAgentsRelations = relations(CustomerRatingsToAgents, ({ one }) => ({
	connection: one(CustomerAgentConnections, {
		relationName: 'CustomerAgentConnectionsToCustomerRatingsToAgents',
		fields: [CustomerRatingsToAgents.connectionId],
		references: [CustomerAgentConnections.id]
	}),
	property: one(Property, {
		relationName: 'CustomerRatingsToAgentsToProperty',
		fields: [CustomerRatingsToAgents.propertyId],
		references: [Property.id]
	}),
	ratedTo: one(User, {
		relationName: 'CustomerRatingsToAgentsToUser',
		fields: [CustomerRatingsToAgents.ratedToUserId],
		references: [User.id]
	}),
	ratedBy: one(Customer, {
		relationName: 'CustomerToCustomerRatingsToAgents',
		fields: [CustomerRatingsToAgents.ratedByUserId],
		references: [Customer.id]
	})
}));

export const CustomerAgentConnectionMessagesRelations = relations(CustomerAgentConnectionMessages, ({ one }) => ({
	connection: one(CustomerAgentConnections, {
		relationName: 'CustomerAgentConnectionMessagesToCustomerAgentConnections',
		fields: [CustomerAgentConnectionMessages.connectionId],
		references: [CustomerAgentConnections.id]
	})
}));

export const CustomerAgentConnectionsRelations = relations(CustomerAgentConnections, ({ one, many }) => ({
	property: one(Property, {
		relationName: 'CustomerAgentConnectionsToProperty',
		fields: [CustomerAgentConnections.propertyId],
		references: [Property.id]
	}),
	rating: many(CustomerRatingsToAgents, {
		relationName: 'CustomerAgentConnectionsToCustomerRatingsToAgents'
	}),
	customer: one(Customer, {
		relationName: 'CustomerToCustomerAgentConnections',
		fields: [CustomerAgentConnections.customerId],
		references: [Customer.id]
	}),
	agent: one(User, {
		relationName: 'CustomerAgentConnectionsToUser',
		fields: [CustomerAgentConnections.agentId],
		references: [User.id]
	}),
	messages: many(CustomerAgentConnectionMessages, {
		relationName: 'CustomerAgentConnectionMessagesToCustomerAgentConnections'
	})
}));

export const CustomerPropertyViewingHistoryRelations = relations(CustomerPropertyViewingHistory, ({ one }) => ({
	user: one(Customer, {
		relationName: 'CustomerToCustomerPropertyViewingHistory',
		fields: [CustomerPropertyViewingHistory.customerId],
		references: [Customer.id]
	}),
	property: one(Property, {
		relationName: 'CustomerPropertyViewingHistoryToProperty',
		fields: [CustomerPropertyViewingHistory.propertyId],
		references: [Property.id]
	})
}));

export const ReportedUserFromChatRelations = relations(ReportedUserFromChat, ({ one }) => ({
	reportingUser: one(User, {
		relationName: 'reportingUser',
		fields: [ReportedUserFromChat.reportingUserId],
		references: [User.id]
	}),
	reportedUser: one(User, {
		relationName: 'reportedUser',
		fields: [ReportedUserFromChat.reportedUserId],
		references: [User.id]
	}),
	reportingCustomer: one(Customer, {
		relationName: 'CustomerToReportedUserFromChat',
		fields: [ReportedUserFromChat.reportingCustomerId],
		references: [Customer.id]
	})
}));

export const LanguagesToUserRelations = relations(LanguagesToUser, ({ one }) => ({
	User: one(User, {
		relationName: 'UserToLanguagesToUser',
		fields: [LanguagesToUser.UserId],
		references: [User.id]
	}),
	Languages: one(Languages, {
		relationName: 'LanguagesToLanguagesToUser',
		fields: [LanguagesToUser.LanguagesId],
		references: [Languages.id]
	})
}));