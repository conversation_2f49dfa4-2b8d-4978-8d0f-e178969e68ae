{"name": "@repo/tailwind-config", "version": "0.1.0", "private": true, "type": "module", "exports": {"./native": "./native.ts", "./web": "./web.ts", "./postcss.config": "./postcss.config.mjs"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"postcss": "^8.4.47", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/prettier-config": "*", "@repo/tsconfig": "*", "eslint": "^9.12.0", "prettier": "^3.3.3", "typescript": "^5.8.2"}, "prettier": "@acme/prettier-config"}