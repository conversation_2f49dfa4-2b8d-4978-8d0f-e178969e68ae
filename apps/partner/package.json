{"name": "partner", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev --turbopack -p 3002", "lint": "next lint", "start": "next start -p 7005"}, "dependencies": {"@auth/prisma-adapter": "^1.6.0", "@hookform/resolvers": "^3.9.0", "@meilisearch/instant-meilisearch": "^0.24.0", "@prisma/nextjs-monorepo-workaround-plugin": "6.8.2", "@react-google-maps/api": "^2.20.3", "@repo/database": "*", "@repo/partner-api": "*", "@repo/partner-auth": "*", "@repo/tailwind-config": "*", "@repo/ui": "*", "@repo/validators": "*", "@sentry/nextjs": "^9", "@smastrom/react-rating": "^1.5.0", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5", "@tanstack/react-query-devtools": "^5", "@trpc/client": "^11.2.0", "@trpc/next": "^11.2.0", "@trpc/react-query": "^11.2.0", "@trpc/server": "^11.2.0", "@trpc/tanstack-react-query": "^11.2.0", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.7.7", "cloudinary": "^2.6.0", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.3.1", "emoji-mart": "^5.6.0", "instantsearch.css": "^8.5.1", "lucide-react": "^0.395.0", "meilisearch": "^0.49.0", "motion": "^11.13.5", "next": "15.2.1-canary.6", "next-auth": "5.0.0-beta.25", "next-cloudinary": "^6.16.0", "onesignal-node": "^3.4.0", "react": "19.0.0", "react-dom": "19.0.0", "react-dropzone": "^14.2.9", "react-fullscreen-image": "^0.0.3", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-instantsearch": "^7.15.3", "react-intersection-observer": "^9.13.1", "react-onesignal": "^3.0.1", "react-share": "^5.1.0", "recharts": "^2.13.0", "server-only": "^0.0.1", "sharp": "^0.33.5", "superjson": "^2.2.1", "usehooks-ts": "^3.1.0", "zod": "^3.24.1", "zustand": "^5.0.0"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/tsconfig": "*", "@types/node": "^20.14.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^9.12.0", "eslint-config-next": "15.0.1", "postcss": "^8.4.39", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.15", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.37.0"}}