"use client";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/default-dialog";
import { ChevronLeft } from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import AgentLanguageForm from "./forms/agent-language-form";
const AgentLanguage = ({ languagesLength }: { languagesLength: number }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);

  const handleEditLangugaes = () => {
    params.set("editLanguage", "true");
    router.replace(`?${params}`);
  };
  return (
    <>
      {languagesLength != 0 ? (
        <div
          className="relative aspect-square w-[18px] cursor-pointer lg:w-5 2xl:w-[30px]"
          onClick={handleEditLangugaes}
        >
          <Image
            src={"/icons/edit-pencil.svg"}
            alt="Edit icon"
            className="text-black"
            fill
          />
        </div>
      ) : (
        <Button
          onClick={handleEditLangugaes}
          className="py-2 font-airbnb_w_md xl:py-2.5 xl:text-base 2xl:text-xl"
        >
          Add
        </Button>
      )}

      <Dialog open={!!searchParams.get("editLanguage")}>
        <DialogContent className="max-w-sm rounded-2xl p-0 md:max-w-md lg:max-w-2xl [&>button]:hidden">
          <DialogHeader className="flex flex-row items-center space-y-0 rounded-2xl bg-text-30 px-[20px] py-4 lg:px-[40px] 2xl:px-[50px]">
            <ChevronLeft
              onClick={() => {
                params.delete("editLanguage");
                router.replace(`?${params.toString()}`);
              }}
              className="size-5 cursor-pointer text-text-500 md:size-6 lg:size-[30px]"
            />
            <DialogTitle className="mx-auto font-airbnb_w_bd text-lg font-bold text-secondary-2-700 lg:text-xl xl:text-2xl 2xl:text-[28px]">
              Edit Languages
            </DialogTitle>
          </DialogHeader>{" "}
          <AgentLanguageForm />
        </DialogContent>
      </Dialog>
    </>
  );
};
export default AgentLanguage;
