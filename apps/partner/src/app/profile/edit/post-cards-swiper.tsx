"use client";
import { api } from "~/trpc/react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import PostCard from "@repo/ui/components/shared/post-card";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter, useSearchParams } from "next/navigation";
import ReportPostDialog from "../feed/report-post-dialog";
import PostCardSkeleton from "../feed/post-card-skeleton";
import { useSession } from "next-auth/react";
const PostCardsSwiper = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const session=useSession()
  const { data, isPending } = api.user.getPosts.useQuery()

  const feed = data ?? [];
  const { mutate: likePost } = api.social.likePost.useMutation();
//   const { mutate: unlikePost } = api.social.unlikePost.useMutation();
  const { mutate: newComment } = api.social.newComment.useMutation();
  const { mutate: deletePost } = api.social.deletePost.useMutation();
  const trcpUtils = api.useUtils();

  const handlePostClick = (postId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("postId", postId);
    router.replace(`?${params}`, { scroll: false });
  };

  const handlePostDelete = (postId: string) => {
    deletePost(
      { postId: postId },
      {
        onSuccess: (opts) => {
          void trcpUtils.social.getFeed.invalidate();
          void trcpUtils.user.getPosts.invalidate()
          toast.success(opts.message);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };


  return (
    <>
      {feed.length == 0 && (
        <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-sm md:text-base text-primary-2-800 xl:text-lg 2xl:text-xl">
          No posts.
        </div>
      )}
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {isPending
            ? Array.from({ length: 4 }).map((_, index) => (
                <CarouselItem key={index} className="basis-[390px]">
                  <PostCardSkeleton key={index} />
                </CarouselItem>
              ))
            : feed.length > 0 &&
              feed.map((item, index) => {
                const handleLike = () => {
                    likePost(
                      { postId: item.id },
                      {
                        onSuccess: () => {
                          void trcpUtils.user.getPosts.invalidate();
                          void trcpUtils.homePage.getAgentDetails.invalidate()
                          void trcpUtils.social.invalidate()
                        },
                        onError: (opts) => {
                          toast.error(opts.message);
                        },
                      },
                    );
                };
                const handleComment = (postId: string, comment: string) => {
                  newComment(
                    { comment, postId },
                    {
                      onSuccess: () => {
                        void trcpUtils.user.getPosts.invalidate();
                        void trcpUtils.homePage.getAgentDetails.invalidate()
                        void trcpUtils.social.invalidate()
                      },
                      onError: (opts) => {
                        toast.error(opts.message);
                      },
                    },
                  );
                };

                return (
                  <CarouselItem
                    key={index}
                    className="basis-[360px] md:basis-[390px] 2xl:basis-[500px]"
                  >
                    <PostCard
                      isLiked={!!item.likes.length}
                      onLike={handleLike}
                      onDelete={handlePostDelete}
                      hideReportButton={session.data?.user?.id === item.user.id}
                      hideDeleteButton={session.data?.user?.id === item.user.id}
                      onComment={handleComment}
                      onPostClick={handlePostClick}
                      formatTime={(date) => date.toLocaleDateString()}
                      formatCount={(count) => count.toString()}
                      reportPostDialogForm={
                        <ReportPostDialog postId={item.id}></ReportPostDialog>
                      }
                      {...item}
                    />
                  </CarouselItem>
                );
              })}
        </CarouselContent>
      </Carousel>
    </>
  );
};

export default PostCardsSwiper;
