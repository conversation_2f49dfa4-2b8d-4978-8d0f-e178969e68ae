"use client";
import Image from "next/image";
import { Button } from "@repo/ui/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/default-dialog";
import { ChevronLeft } from "lucide-react";
import AgentBioForm from "./forms/agent-bio-form";
const AgentBio = ({ bio }: { bio: string | null }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);

  const handleEditBio = () => {
    params.set("editBio", "true");
    router.replace(`?${params}`);
  };
  return (
    <>
      {bio ? (
        <div
          className="relative aspect-square w-[18px] cursor-pointer lg:w-5 2xl:w-[30px]"
          onClick={handleEditBio}
        >
          <Image
            src={"/icons/edit-pencil.svg"}
            alt="Edit icon"
            className="text-black"
            fill
          />
        </div>
      ) : (
        <Button
          onClick={handleEditBio}
          className="py-2 font-airbnb_w_md xl:py-2.5 xl:text-base 2xl:text-xl"
        >
          Add
        </Button>
      )}
      <Dialog open={!!searchParams.get("editBio")}>
        {/* h-[43vh] lg:h-[47vh] xl:h-[44vh] 2xl:h-[33vh] */}
        <DialogContent className="max-w-sm rounded-2xl md:max-w-md lg:max-w-xl p-0 [&>button]:hidden">
          <DialogHeader className="rounded-2xl flex flex-row items-center space-y-0 bg-text-30 px-[20px] py-4 lg:px-[40px] 2xl:px-[50px]">
            <ChevronLeft
              onClick={() => {
                params.delete("editBio");
                router.replace(`?${params.toString()}`);
              }}
              className="size-5 cursor-pointer text-text-500 md:size-6 lg:size-[30px]"
            />
            <DialogTitle className="mx-auto font-airbnb_w_bd text-lg font-bold text-secondary-2-700 lg:text-xl xl:text-2xl 2xl:text-[28px]">
              Edit Bio
            </DialogTitle>
          </DialogHeader>{" "}
          <AgentBioForm bio={bio} />
        </DialogContent>
      </Dialog>
    </>
  );
};
export default AgentBio;

{
  /* <FormField
control={form.control}
name="experience"
render={({ field }) => (
  <FormItem>
    <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
      Total Experience (optional)
    </FormLabel>
    <FormControl>
      <Input placeholder="Experience in years" {...field} />
    </FormControl>
    <FormMessage />
  </FormItem>
)}
/>

 */
}
