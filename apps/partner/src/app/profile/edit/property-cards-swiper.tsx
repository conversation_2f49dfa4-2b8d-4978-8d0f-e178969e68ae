"use client";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import { api } from "~/trpc/react";
import PropertyCard from "@repo/ui/components/shared/property-card";
import ContactCheckResponsesButton from "~/app/_components/shared/contact-checkresponses-button";
import EditPropertyButton from "../feed/edit-property-button";
import LikePropertyButton from "~/app/_components/shared/like-property-button";
import SharePropertyButton from "~/app/_components/shared/share-property-button";
import PublishPropertyButton from "~/app/_components/shared/publish-property-button";
import MarkAsSoldButton from "~/app/_components/shared/mark-as-sold-button";
import { useSession } from "next-auth/react";
import PropertyCardSkeleton from "@repo/ui/components/skeleton/property-card-skeleton";

const PropertyCardsSwiper = () => {
  const session = useSession();
  const { data, isPending } = api.postProperty.getFilteredProperties.useQuery({
    filter: "Active",
    q: null,
  });
  const activeProperties = data ?? [];
  return (
    <>
      {activeProperties.length == 0 && (
        <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-sm text-primary-2-800 md:text-base xl:text-lg 2xl:text-xl">
          Your active property listings will appear here.
        </div>
      )}
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {isPending
            ? Array.from({ length: 4 }).map((_, index) => (
                <CarouselItem key={index} className="basis-[340px]">
                  <PropertyCardSkeleton key={index} />
                </CarouselItem>
              ))
            : activeProperties.length > 0 &&
              activeProperties.map((item, index) => (
                <CarouselItem key={index} className="basis-[340px]">
                  <PropertyCard
                    locationIcon="/icons/location.svg"
                    id={item.id}
                    propertyOwnerId={item.user.id}
                    key={item.id}
                    property={item}
                    userId={session.data?.user?.id}
                    contactOrCheckResponsesButton={
                      <ContactCheckResponsesButton
                        soldAt={item.soldAt}
                        propertyOwnerId={item.user.id}
                        propertyId={item.id}
                      />
                    }
                    editPropertyButton={
                      <EditPropertyButton propertyId={item.id} />
                    }
                    likePropertyButton={
                      <LikePropertyButton propertyId={item.id} />
                    }
                    sharePropertyButton={
                      <SharePropertyButton propertyId={item.id} />
                    }
                    publishPropertyButton={
                      <PublishPropertyButton propertyId={item.id} />
                    }
                    markAsSoldButton={<MarkAsSoldButton propertyId={item.id} />}
                  />
                </CarouselItem>
              ))}
        </CarouselContent>
      </Carousel>
    </>
  );
};
export default PropertyCardsSwiper;
