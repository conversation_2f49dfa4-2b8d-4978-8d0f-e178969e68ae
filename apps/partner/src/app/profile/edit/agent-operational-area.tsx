"use client";
import Image from "next/image";
import { Button } from "@repo/ui/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/default-dialog";
import { ChevronLeft } from "lucide-react";
import AgentOperationalForm from "./forms/agent-operational-area-form";
const AgentOperationalArea = ({
  operationAreaLength,
}: {
  operationAreaLength: number;
}) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);

  const handleEditOperationArea = () => {
    params.set("editOperationArea", "true");
    router.replace(`?${params}`);
  };
  return (
    <>
      {operationAreaLength != 0 ? (
        <div
          className="relative aspect-square w-[18px] cursor-pointer lg:w-5 2xl:w-[30px]"
          onClick={handleEditOperationArea}
        >
          <Image
            src={"/icons/edit-pencil.svg"}
            alt="Edit icon"
            className="text-black"
            fill
          />
        </div>
      ) : (
        <Button
          onClick={handleEditOperationArea}
          className="py-2 font-airbnb_w_md xl:py-2.5 xl:text-base 2xl:text-xl"
        >
          Add
        </Button>
      )}

      <Dialog open={!!searchParams.get("editOperationArea")}>
        <DialogContent className="max-w-sm rounded-2xl md:max-w-md lg:max-w-2xl xl:max-w-3xl [&>button]:hidden p-0">
          <DialogHeader className="bg-text-30 flex flex-row items-center space-y-0 rounded-2xl px-[20px] py-4 lg:px-[40px] 2xl:px-[50px]">
            <ChevronLeft
              onClick={() => {
                params.delete("editOperationArea");
                router.replace(`?${params.toString()}`);
              }}
              className="size-5 cursor-pointer text-text-500 md:size-6 lg:size-[30px]"
            />
            <DialogTitle className="mx-auto font-airbnb_w_bd text-lg font-bold text-secondary-2-700 lg:text-xl xl:text-2xl 2xl:text-[28px]">
              Edit Operational Area
            </DialogTitle>
          </DialogHeader>{" "}
          <AgentOperationalForm />
          <DialogClose className="hidden"/>
        </DialogContent>
      </Dialog>
    </>
  );
};
export default AgentOperationalArea;
