"use client";

import { Camera, Trash, UserRoundPlusIcon } from "lucide-react";
import { api } from "~/trpc/react";
import { useRouter } from "next/navigation";
import { toast } from "@repo/ui/components/ui/sonner";
import { CldUploadWidget } from "next-cloudinary";
import type { CloudinaryUploadWidgetResults } from "next-cloudinary";
import { env } from "~/env";
import { cn } from "@repo/ui/lib/utils";

const EditUserBgImage = ({
  userId,
  showDeleteButton,
}: {
  userId: string;
  showDeleteButton: boolean;
}) => {
  const router = useRouter();
  const { mutate: updateUserBgImage, isPending } =
    api.user.updateUserBgImage.useMutation({
      onSuccess: (opts) => {
        toast.success(opts.message);
        toast.dismiss();
        router.refresh();
      },
      onError: (opts) => {
        toast.dismiss();
        toast.error(opts.message);
      },
    });

  const handleUploadSuccess = (result: CloudinaryUploadWidgetResults) => {
    const { info } = result as {
      info: { public_id: string; secure_url: string };
      event: string;
    };

    const cloudinaryId = info.public_id;
    const cloudinaryUrl = info.secure_url;

    if (!cloudinaryId || !cloudinaryUrl) {
      toast.error("Upload failed");
      return;
    }

    updateUserBgImage({
      purpose: "update",
      cloudinaryId: cloudinaryId,
      cloudinaryUrl: cloudinaryUrl,
    });
  };

  const handleImageDelete = () => {
    updateUserBgImage({
      purpose: "delete",
    });
  };

  if (isPending) {
    toast.loading("Processing...");
  }

  return (
    <>
      <CldUploadWidget
        signatureEndpoint="/api/cloudinary"
        uploadPreset={env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET}
        onSuccess={handleUploadSuccess}
        options={{
          cropping: true,
          croppingCoordinatesMode: "custom",
          croppingShowDimensions: true,
          croppingValidateDimensions: true,
          croppingAspectRatio: 9.33 / 1,
          showSkipCropButton: false,
          maxFiles: 1,
          showPoweredBy: false,
          resourceType: "image",
          clientAllowedFormats: ["png", "jpg", "jpeg"],
          folder: `${env.NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME}/users/${userId}/profile`,
        }}
      >
        {({ open }) => (
          <button
            onClick={() => open()}
            className={cn(
              "absolute top-8 cursor-pointer rounded-full bg-[#FDEDE9] p-3",
              showDeleteButton ? "right-24" : "right-8",
            )}
          >
            <Camera className="size-5" color="#D6330A" />
          </button>
        )}
      </CldUploadWidget>

      {showDeleteButton && (
        <button
          onClick={handleImageDelete}
          className="absolute right-8 top-8 cursor-pointer rounded-full bg-[#FDEDE9] p-3"
        >
          <Trash className="size-5" color="#D6330A" />
        </button>
      )}
    </>
  );
};

export default EditUserBgImage;
