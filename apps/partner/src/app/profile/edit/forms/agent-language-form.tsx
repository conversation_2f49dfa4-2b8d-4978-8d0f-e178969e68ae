import { Button } from "@repo/ui/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@repo/ui/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/ui/popover";
import { toast } from "@repo/ui/components/ui/sonner";
import { cn } from "@repo/ui/lib/utils";
import { Check, ChevronsUpDown } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { api } from "~/trpc/react";
const AgentLanguageForm = () => {
  const [open, setOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);
  const { data: getLanguages } = api.languages.getAllLanguages.useQuery();
  const allLanguages = getLanguages ?? [];

  const addLanguage = api.languages.addOrRemoveLanguage.useMutation();
  const trpcUtils = api.useUtils();
  const { data: getMyLanguages } = api.languages.getMyLanguage.useQuery();
  const myLanguages = getMyLanguages ?? [];

  const handleSetLanguage = async (langId: string) => {
    const isSelected = myLanguages.some((lang) => lang.id === langId);
    await addLanguage.mutateAsync(
      { id: langId },
      {
        onSuccess: () => {
          trpcUtils.languages.invalidate().catch(() => {
            console.log("refetched");
          });
          router.refresh()

          if (isSelected) {
            toast.success("Language removed successfully.");
          } else {
            toast.success("Language updated successfully.");
          }
        },
        onError: () => {
          console.log("error in adding the language");
        },
      },
    );
  };
  return (
    <>
      <div className="mt-3 px-[30px] lg:px-10 2xl:px-[50px]">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              size={"small"}
              role="combobox"
              aria-expanded={open}
              className="flex w-full flex-row justify-between rounded-lg border border-[#ECE9E8] bg-white font-airbnb_w_bk text-muted-foreground hover:bg-transparent md:text-sm"
            >
              <div className="flex justify-start gap-2">
                {myLanguages.length
                  ? myLanguages.map((val, i) => (
                      <div
                        key={i}
                        className="rounded-[6px] bg-primary-2-100 px-3 py-2 font-airbnb_w_md text-sm font-medium text-primary-2-800 md:text-base 2xl:text-lg"
                      >
                        {allLanguages.find((lang) => lang.id === val.id)?.name}
                      </div>
                    ))
                  : "Select your language"}
              </div>
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[--radix-popover-trigger-width]">
            <Command className="w-full">
              <CommandInput placeholder="Search language..." />
              <CommandEmpty>No languages found.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  {allLanguages.map((lang) => (
                    <CommandItem
                      key={lang.id}
                      value={lang.name}
                      onSelect={() => handleSetLanguage(lang.id)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          myLanguages.some(
                            (language) => language.id === lang.id,
                          )
                            ? "opacity-100"
                            : "opacity-0",
                        )}
                      />
                      {lang.name}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        <div className="sticky bottom-0 flex items-center justify-between bg-[rgba(255,_251,_249,_0.30)] py-5 backdrop-blur-lg">
          <Button
            onClick={() => {
              params.delete("editLanguage");
              router.replace(`?${params.toString()}`);
            }}
            type="button"
            className="rounded-xl bg-secondary-2-100 px-4 py-3 text-sm font-medium text-secondary-2-700 hover:bg-secondary-2-100 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              params.delete("editLanguage");
              router.replace(`?${params.toString()}`);
              toast.success("Languages addded succesfully.");
            }}
            type="submit"
            className="py-3 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
          >
            Save Changes
          </Button>
        </div>
      </div>
    </>
  );
};

export default AgentLanguageForm;
