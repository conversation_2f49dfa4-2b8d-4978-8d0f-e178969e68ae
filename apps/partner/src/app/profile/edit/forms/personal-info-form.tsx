"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@repo/ui/components/ui/button";
import { z } from "zod";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { FieldErrors } from "react-hook-form";

import { signUpInputValidation } from "@repo/validators";
import GoogleAutocompleteInput from "@repo/ui/components/shared/google-autocomplete-input";
import PersonalDetailEditFormSheet from "../personal-detail-edit-form-sheet";
import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { useRouter, useSearchParams } from "next/navigation";

export type IPersonalInfo = {
  id?: string;
  name: string;
  email: string;
  phoneNumber: string;
  adharcardNumber: string | null;
  pancardNumber: string;
  reraNumber?: string | null;
  gstNumber?: string | null;
  bio?: string | null;
  experience?: string | null;
  operationArea?: {
    name: string;
    areaLat: string;
    areaLng: string;
    areaGooglePlaceId: string;
  }[];
  userLocation?: string | null;
  city: { name: string; id: string } | null;
};

const PersonalInfoForm = ({ user }: { user: IPersonalInfo }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const profileUpdate = api.user.profileUpdate.useMutation();

  const onSubmit = (values: z.infer<typeof signUpInputValidation>) => {
    console.log("values are", values);
    profileUpdate.mutate(values, {
      onSuccess: (opts) => {
        console.log("User updated", opts.messageDescription);
        toast.success(opts.messageDescription);
        params.delete("editProfile");
        router.replace(`?${params.toString()}`);
      },
      onError: (opts) => {
        console.log(opts);

        toast.error(opts.message);
      },
    });
    //   .then((resp) => {
    //     console.log("User updated", resp.messageDescription);
    //     toast.success("User updated successfully");
    //   })
    //   .catch((err) => {
    //     console.log("Error in updating user");
    //     toast.error(err);
    //   });
  };

  const onError = (
    error: FieldErrors<z.infer<typeof signUpInputValidation>>,
  ) => {
    console.log("Error", error);
    toast.error("Please check form fields");
  };

  const form = useForm<z.infer<typeof signUpInputValidation>>({
    resolver: zodResolver(signUpInputValidation),
    defaultValues: {
      name: user.name,
      email: user.email,
      phoneNumber: user.phoneNumber,
      adharcardNumber: user.adharcardNumber ?? undefined,
      pancardNumber: user.pancardNumber,
      reraNumber: user.reraNumber ?? "",
      gstNumber: user.gstNumber ?? "",
      experience: user.experience ?? "",
      bio: user.bio ?? "",
      userLocation: user.userLocation ?? "",
      operationArea: "",
      cityId: user.city?.id ?? "",
    },
  });

  //   useEffect(() => {
  //     if (!city?.city?.cityMarkersLatLng) return;
  //     const cityMarkersLatLng = city.city.cityMarkersLatLng as {
  //       lat: number;
  //       lng: number;
  //     }[];

  //     cityMarkersLatLng.forEach((marker) => {
  //       setLocationRestriction((prev) => [...prev, marker]);
  //     });
  //   }, [city]);

  //   const bioinputRef = useRef<HTMLInputElement>(null);
  //   const operationalAreaRef = useRef<HTMLInputElement>(null);

  //   useEffect(() => {
  //     if (!bioinputRef.current) return;
  //     if (!operationalAreaRef.current) return;

  //     if (params.get("scrollTo") === "bio") {
  //       bioinputRef.current.scrollIntoView({ behavior: "smooth" });
  //     }
  //     if (params.get("scrollTo") === "operationArea") {
  //       operationalAreaRef.current.scrollIntoView({ behavior: "smooth" });
  //     }
  //   }, []);

  return (
    <>
      <div className="mt-2 px-[20px] lg:px-[40px] 2xl:px-[50px]">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit, onError)}
            className="relative space-y-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Name
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: someone" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Email Id
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: <EMAIL>" {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center justify-between">
                    <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                      Phone Number
                    </FormLabel>

                    <PersonalDetailEditFormSheet
                      //   pancardNumber={user?.pancardNumber}
                      phoneNumber={user.phoneNumber}
                      //   adharcardNumber={user?.adharcardNumber}
                      email={user.email}
                    />
                  </div>
                  <FormControl>
                    <Input placeholder="eg: 9876543212" {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cityId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger disabled>
                        <SelectValue placeholder="Select a verified email to display">
                          {user.city?.name ?? ""}
                        </SelectValue>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent></SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="adharcardNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Adhar Card Number (optional)
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: 123456789012" {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="pancardNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Pan Card
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: **********" {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="reraNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    RERA Number (optional)
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: RERA321" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gstNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    GST Number (optional)
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: 123456789012435" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="userLocation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Your Location (optional)
                  </FormLabel>
                  <FormControl>
                    <GoogleAutocompleteInput
                      initialValue={field.value}
                      className="w-full rounded-md py-3.5 pl-0 outline-none focus:outline-none"
                      placeholder="Search Location..."
                      onInputChange={(e) => {
                        field.onChange(e);
                      }}
                      onLocationSelect={(e) => {
                        field.onChange(e.fullAddress);
                        form.setValue("latitude", e.latitude);
                        form.setValue("longitude", e.longitude);
                      }}
                      onUserLocationDetect={(e) => {
                        field.onChange(e.address);
                      }}
                      showSearchIcon={true}
                      showAutoDetectLocationIcon={true}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="experience"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Total Experience (optional)
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Experience in years" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="sticky bottom-0 flex items-center justify-between bg-[rgba(255,_251,_249,_0.30)] py-3 backdrop-blur-lg">
              <Button
                onClick={() => {
                  params.delete("editProfile");
                  router.replace(`?${params.toString()}`);
                }}
                type="button"
                className="rounded-xl bg-secondary-2-100 px-4 py-3 text-sm font-medium text-secondary-2-700 hover:bg-secondary-2-100 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="py-3 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};

export default PersonalInfoForm;
