import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { toast } from "@repo/ui/components/ui/sonner";
import { Textarea } from "@repo/ui/components/ui/textarea";
import { bioFormSchema } from "@repo/validators";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { api } from "~/trpc/react";

const AgentBioForm = ({ bio }: { bio: string | null }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);
  const bioUpdate = api.user.bioUpdate.useMutation();
  const form = useForm<z.infer<typeof bioFormSchema>>({
    resolver: zodResolver(bioFormSchema),
    defaultValues: {
      bio: bio || "",
    },
  });
  const onSubmit = async(values: z.infer<typeof bioFormSchema>) => {
    await bioUpdate.mutateAsync(values, {
      onSuccess: (opts) => {
        console.log("User updated bio", opts.messageDescription);
        toast.success(opts.messageDescription);
        params.delete("editBio");
        router.replace(`?${params.toString()}`);
      },
      onError: (err) => {
        console.log("error is", err.message);
        toast.error(err.message);
      },
    });
  };
  return (
    <>
      <div className="mt-2 px-[20px] lg:px-[40px] 2xl:px-[50px]">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="relative space-y-5"
          >
            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Write Bio (optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      className="bg-white"
                      {...field}
                      placeholder="Type here..."
                    ></Textarea>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="sticky bottom-0 flex items-center justify-between bg-[rgba(255,_251,_249,_0.30)] pb-4 backdrop-blur-lg">
              <Button
                onClick={() => {
                  params.delete("editBio");
                  router.replace(`?${params.toString()}`);
                }}
                type="button"
                className="rounded-xl bg-secondary-2-100 px-4 py-3 text-sm font-medium text-secondary-2-700 hover:bg-secondary-2-100 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="py-3 text-sm 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};

export default AgentBioForm;
