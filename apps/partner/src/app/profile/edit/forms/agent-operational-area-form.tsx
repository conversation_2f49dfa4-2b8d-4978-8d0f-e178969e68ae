import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/ui/alert-dialog";
import GoogleAutocompleteInput from "@repo/ui/components/shared/google-autocomplete-input";
import { enableRestriction } from "~/app/_helpers";

import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { api } from "~/trpc/react";
import { CircleX } from "lucide-react";
import { useEffect, useState } from "react";
import { operationAreaSchema } from "@repo/validators";

type Point = {
  lat: number;
  lng: number;
};
const AgentOperationalForm = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);
  const trpcUtils = api.useUtils();
  const onboardingOperationAreaMutation =
    api.operationArea.onboardingOperationArea.useMutation();
  const getOperationArea = api.operationArea.getOperationArea.useQuery();
  const { data: city } = api.operationArea.getCity.useQuery();
  const { data: operationArea, isLoading } = getOperationArea;
  const [locationRestriction, setLocationRestriction] = useState<Point[]>([]);

  const deleteOperationArea =
    api.operationArea.deleteOperationArea.useMutation();
  const handleDeleteOperationArea = (operationAreaId: string) => {
    deleteOperationArea
      .mutateAsync({
        operationAreaId: operationAreaId,
      })
      .then(async(resp) => {
        toast.success("Operation area deleted successfully");
        await getOperationArea.refetch();
        router.refresh()
      })
      .catch((err) => {
        toast.error("Something went wrong");
      });
  };
  const onSubmit = (values: z.infer<typeof operationAreaSchema>) => {
    params.delete("editOperationArea");
    router.replace(`?${params}`);
    toast.success("Operational Area added successfully");
  };
  useEffect(() => {
    if (!city?.city?.cityMarkersLatLng) return;
    const cityMarkersLatLng = city.city.cityMarkersLatLng as {
      lat: number;
      lng: number;
    }[];

    cityMarkersLatLng.forEach((marker) => {
      setLocationRestriction((prev) => [...prev, marker]);
    });
  }, [city]);

  const form = useForm<z.infer<typeof operationAreaSchema>>({
    resolver: zodResolver(operationAreaSchema),
    defaultValues: {
      operationArea: "",
    },
  });
  return (
    <>
      <div className="mt-3 px-[30px] lg:px-10 2xl:px-[50px]">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="relative space-y-4"
          >
            <FormField
              control={form.control}
              name="operationArea"
              render={({ field }) => (
                <FormItem className="">
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Your Operational Areas
                  </FormLabel>
                  <FormControl>
                    <GoogleAutocompleteInput
                      initialValue={field.value ?? ""}
                      className="mt-2 py-3.5"
                      placeholder="Enter your operation area"
                      onLocationSelect={(e) => {
                        const {
                          addressComponents,
                          longitude,
                          placeId,
                          latitude,
                          fullAddress,
                        } = e;
                        field.onChange("");
                        onboardingOperationAreaMutation
                          .mutateAsync({
                            operationArea: fullAddress,
                            operationAreaGooglePlaceId: placeId,
                            operationAreaLatitude: latitude,
                            operationAreaLongitude: longitude,
                            operationAreaAddressComponent: addressComponents,
                          })
                          .then((resp) => {
                            toast.success(resp.messageDescription);
                            form.setValue("operationArea", "");
                            trpcUtils.operationArea.getOperationArea
                              .invalidate()
                              .catch((e) => console.log("refetched"));
                              router.refresh()
                          })
                          .catch((err) => {
                            console.log("error is", err);
                            toast.error("Something went wrong");
                          });
                      }}
                      searchBounds={
                        enableRestriction
                          ? {
                              point1: locationRestriction[0],
                              point2: locationRestriction[1],
                              point3: locationRestriction[2],
                              point4: locationRestriction[3],
                            }
                          : undefined
                      }
                      showSearchIcon={true}
                    />
                  </FormControl>
                  {/* suggestions container */}
                  <div className="flex max-h-[120px] flex-wrap items-start gap-2 overflow-y-scroll">
                    {isLoading ? (
                      <div className="flex h-6 w-16 animate-pulse items-center gap-2 rounded-[6px] bg-gray-400 px-3 py-2 font-airbnb_w_bk text-sm text-primary-2-800 xl:h-7 xl:text-base">
                        {/* <CircleX
                              onClick={handleDeleteOperationArea}
                              className="size-4 xl:size-5"
                            ></CircleX> */}
                      </div>
                    ) : (
                      operationArea?.map((area) => {
                        return (
                          <div className="flex items-center gap-2 rounded-[6px] bg-primary-2-100 px-3 py-2 font-airbnb_w_bk text-sm text-primary-2-800 2xl:text-base">
                            {area.name}
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <CircleX className="size-5"></CircleX>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="w-[300px] max-w-xl md:w-[398px] lg:w-[576px] xl:w-[800px] xl:p-8 2xl:w-[880px]">
                                <AlertDialogHeader className="mb-2">
                                  <AlertDialogTitle className="text-center font-airbnb_w_xbd text-xl font-extrabold text-text-600 2xl:text-2xl">
                                    Are you absolutely sure?
                                  </AlertDialogTitle>
                                  <AlertDialogDescription className="text-center font-airbnb_w_bk text-base font-normal md:text-lg 2xl:text-xl">
                                    Are you sure you want to delete this
                                    operation area?
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel className="w-full border border-transparent bg-secondary-2-100 text-secondary-2-700">
                                    Cancel
                                  </AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() =>
                                      handleDeleteOperationArea(area.id)
                                    }
                                    className="w-full"
                                  >
                                    Continue
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        );
                      })
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="sticky bottom-0 flex items-center justify-between bg-[rgba(255,_251,_249,_0.30)] pb-4 backdrop-blur-lg">
              <Button
                onClick={() => {
                  params.delete("editOperationArea");
                  router.replace(`?${params.toString()}`);
                }}
                type="button"
                className="rounded-xl bg-secondary-2-100 px-4 py-3 text-sm font-medium text-secondary-2-700 hover:bg-secondary-2-100 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="py-3 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};

export default AgentOperationalForm;
