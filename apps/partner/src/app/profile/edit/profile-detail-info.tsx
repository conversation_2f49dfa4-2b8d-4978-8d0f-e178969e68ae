"use client";
import { ChevronLeft, Star } from "lucide-react";
import Image from "next/image";
import { Separator } from "@repo/ui/components/ui/separator";
import { useMediaQuery } from "@uidotdev/usehooks";
import { Prisma } from "@repo/database";
import { formatDistanceToNow } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/dialog";
import PersonalInfoForm from "./forms/personal-info-form";
import { useRouter, useSearchParams } from "next/navigation";

export type IProfile = Prisma.UserGetPayload<{
  select: {
    id: true;
    name: true;
    phoneNumber: true;
    email: true;
    filePublicUrl: true;
    onboardingStatus: true;
    adharcardNumber: true;
    pancardNumber: true;
    reraNumber: true;
    gstNumber: true;
    cityId: true;
    referredBy: true;
    experience: true;
    bio: true;
    userLocation: true;
    inviteCode: true;
    longitude: true;
    latitude: true;
    createdAt: true;
    companyId: true;
    bgFilePublicUrl: true;
    propertiesSold: true;
    rating: true;
    reviews: true;
    company: {
      select: {
        id: true;
        companyName: true;
        filePublicUrl: true;
        about: true;
        companyWebsiteLink: true;
        companyLocation: true;
        email: true;
        phoneNumber: true;
        fax: true;
      };
    };
    coustomerConnections: true;
    sentConnectionRequests: true;
    receivedConnectionRequests: true;
    customerRatingsToAgents:true,
    operationArea: {
      select: {
        id: true;
        name: true;
        areaAddressComponents: true;
        areaGooglePlaceId: true;
        areaLat: true;
        areaLng: true;
      };
    };
    city: {
      select: {
        id: true;
        name: true;
      };
    };
    OtpSentAt: true;
  };
}>;

const ProfileDetailInfo = ({ profile }: { profile: IProfile }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const isSmallDevice = useMediaQuery("only screen and (max-width : 767px)");

  const agentProfileDetails = [
    {
      icon: "/icons/shared/property-sold.svg",
      title: "Total sales closed",
      value: profile.propertiesSold ?? 0,
    },
    {
      icon: "/icons/shared/experience.svg",
      title: "Experience",
      value: `${profile.experience || 0} yrs`,
    },
    {
      icon: "/icons/shared/active.svg",
      title: "Active",
      value: `${formatDistanceToNow(profile.createdAt)}`,
    },
    {
      icon: "/icons/shared/connections.svg",
      title: "Connections",
      value:
        profile.coustomerConnections.length +
        profile.sentConnectionRequests.length +
        profile.receivedConnectionRequests.length,
    },
  ];
  const handleEditProfile = () => {
    console.log("here is searchParams");
    const params = new URLSearchParams(searchParams.toString());
    params.set("editProfile", "true");
    router.replace(`?${params}`);
  };

  return (
    <>
      <div className="flex flex-col items-end gap-3 px-4 pb-4 md:px-5 md:pb-5 lg:px-[30px] lg:pb-[30px] xl:px-8 xl:pb-8 2xl:px-10 2xl:pb-10">
        <div
          className="relative aspect-square w-[18px] cursor-pointer lg:w-5 2xl:w-[30px]"
          onClick={handleEditProfile}
        >
          <Image
            src={"/icons/edit-pencil.svg"}
            alt="Edit icon"
            className="text-black"
            fill
          />
        </div>
        <div className="flex w-full flex-col gap-5 md:gap-6 lg:gap-8">
          {/* user data */}
          <div className="flex flex-col gap-2 xl:gap-3">
            <div className="flex flex-row justify-between">
              <div className="flex flex-col gap-1 2xl:gap-1.5">
                <div className="font-airbnb_w_xbd text-xl font-extrabold leading-[32px] text-primary-2-800 md:text-2xl md:leading-[34px] lg:text-[30px] lg:leading-[40px] 2xl:text-4xl 2xl:leading-[42px]">
                  {profile.name}
                </div>
                {profile.company && (
                  <div className="font-airbnb_w_md text-sm font-medium text-text-600 md:text-base lg:text-lg 2xl:text-[28px] 2xl:leading-[30px]">
                    {profile.company.companyName}
                  </div>
                )}
              </div>
              <div className="flex flex-col items-end justify-between">
                <div className="flex flex-row items-center gap-0.5">
                  <Star
                    fill="#FFC727"
                    color="#FFC727"
                    className="size-5 2xl:size-6"
                  />
                  <p className="font-airbnb_w_bd text-base font-bold text-text-600 lg:text-xl lg:leading-[30px] 2xl:text-2xl">
                    {profile.rating ?? 0}
                  </p>
                </div>
                <div className="font-airbnb_w_md text-sm font-medium text-text-600 md:text-base lg:text-lg 2xl:text-xl 2xl:leading-[30px]">
                  {profile.customerRatingsToAgents.length} Reviews
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-1 text-sm md:flex-row md:flex-wrap md:gap-4 md:text-base lg:text-lg 2xl:text-[22px] 2xl:leading-[34px]">
              {profile.userLocation && (
                <div className="font-airbnb_w_bk font-normal text-text-500">
                  {profile.userLocation}
                </div>
              )}
              <div className="font-airbnb_w_md font-medium text-primary-2-750">
                {profile.email} , {profile.phoneNumber}
              </div>
            </div>
          </div>
          {/* stats */}
          <div
            className={
              isSmallDevice
                ? `flex flex-wrap justify-between gap-5`
                : `flex flex-row items-center`
            }
          >
            {agentProfileDetails.map((agent, index) => (
              <div
                key={index}
                className={`flex flex-row items-center ${
                  index % 2 === 1 ? "justify-end" : "justify-start"
                }`}
              >
                <div className="flex flex-col items-center gap-1.5">
                  <div className="font-airbnb_w_bd text-lg font-bold text-primary-2-700 lg:text-xl">
                    {agent.value}
                  </div>
                  <div className="flex flex-row gap-1">
                    <div className="relative aspect-square size-[22px]">
                      <Image
                        src={agent.icon}
                        alt="icon-image"
                        className="size-[22px]"
                        fill
                      />
                    </div>
                    <div className="font-airbnb_w_bk text-sm font-normal text-text-500">
                      {agent.title}
                    </div>
                  </div>
                </div>
                {index != agentProfileDetails.length - 1 && !isSmallDevice && (
                  <Separator
                    orientation="vertical"
                    className="mx-4 h-[55px] bg-primary-2-300 lg:mx-5"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <Dialog open={!!searchParams.get("editProfile")}>
        <DialogContent className="h-[90vh] max-w-sm rounded-2xl md:max-w-md lg:max-w-2xl [&>button]:hidden">
          <DialogHeader className="flex flex-row items-center space-y-0 rounded-2xl bg-text-30 px-[20px] py-4 lg:px-[40px] 2xl:px-[50px]">
            <ChevronLeft
              onClick={() => {
                params.delete("editProfile");
                router.replace(`?${params.toString()}`);
              }}
              className="size-5 cursor-pointer text-text-500 md:size-6 lg:size-[30px]"
            />
            <DialogTitle className="mx-auto font-airbnb_w_bd text-lg font-bold text-secondary-2-700 lg:text-xl xl:text-2xl 2xl:text-[28px]">
              Edit profile
            </DialogTitle>
          </DialogHeader>{" "}
          <PersonalInfoForm user={profile} />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ProfileDetailInfo;
