'use client'
import { But<PERSON> } from "@repo/ui/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
const AgentCompany = ({ companyId }: { companyId: string | null }) => {
  const router=useRouter();
  useEffect(() => {
    router.refresh()
  },[router]);
  return (
    <>
      {companyId ? (
        <Link href="/profile/about-company">
          <div className="relative aspect-square w-[18px] cursor-pointer lg:w-5 2xl:w-[30px]">
            <Image
              src={"/icons/edit-pencil.svg"}
              alt="Edit icon"
              className="text-black"
              fill
            />
          </div>
        </Link>
      ) : (
        <Link href="/profile/about-company">
          <Button className="py-2 font-airbnb_w_md xl:py-2.5 xl:text-base 2xl:text-xl">
            Add
          </Button>
        </Link>
      )}
    </>
  );
};

export default AgentCompany;
