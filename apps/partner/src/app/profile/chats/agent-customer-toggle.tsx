"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@repo/ui/components/ui/button";

const AgentCustomerToggle = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sort = searchParams.get("sort");

  const handleChatToggle = (value: string) => {
    if (sort === value) return;

    const params = new URLSearchParams(searchParams);
    params.set("sort", value);
    router.replace(`?${params.toString()}`);
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        size="sm"
        variant={sort === "agent" ? "default" : "secondary"}
        onClick={() => handleChatToggle("agent")}
      >
        Agent
      </Button>
      <Button
        size="sm"
        variant={sort === "customer" ? "default" : "secondary"}
        onClick={() => handleChatToggle("customer")}
      >
        Customer
      </Button>
    </div>
  );
};

export default AgentCustomerToggle;
