import { Badge } from "@repo/ui/components/ui/badge";
import React, { useState, useEffect, useRef, useCallback } from "react";
import ReceivedMessage from "./received-message";
import SentMessage from "./sent-message";
import { Input } from "@repo/ui/components/ui/input";
import { SendIcon } from "lucide-react";
import type { CustomerAgentConnectionMessages } from "@repo/database";
import { useSession } from "next-auth/react";
import { api } from "~/trpc/react";
import { useSearchParams } from "next/navigation";
import { toast } from "@repo/ui/components/ui/sonner";
import { formatDate } from "date-fns";

interface CustomerAgentChatScreenBodyProps {
  initialMessages?: CustomerAgentConnectionMessages[] | undefined;
}

const POLLING_INTERVAL = 10000; // 10 seconds
const SCROLL_THRESHOLD = 100;

const getGroupTitle = (date: Date): string => {
  const now = new Date();
  const diffInDays = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),
  );

  if (diffInDays === 0) return "Today";
  if (diffInDays === 1) return "Yesterday";
  return formatDate(date, "dd, eeee");
};

const CustomerAgentChatScreenBody: React.FC<
  CustomerAgentChatScreenBodyProps
> = ({ initialMessages }) => {
  const [newMessage, setNewMessage] = useState<string>("");
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const messageEndDivRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const searchParams = useSearchParams();
  const connectionId = searchParams.get("id");
  const { data: sessionData, status } = useSession();
  const trpcUtils = api.useUtils();

  const { data: messages } = api.chat.getCustomerMessages.useQuery(
    { connectionId: connectionId! },
    {
      initialData: initialMessages
        ? {
            id: connectionId!,
            createdAt: new Date(),
            messages: initialMessages,
            property: null,
          }
        : undefined,
      refetchInterval: POLLING_INTERVAL,
      enabled: !!connectionId,
    },
  );

  const { mutate: sendMessage } = api.chat.sendCustomerMessage.useMutation({
    onSuccess: () => {
      setNewMessage("");
      void trpcUtils.chat.getCustomerMessages.invalidate();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const isNearBottom = useCallback(() => {
    if (!chatContainerRef.current) return false;
    const container = chatContainerRef.current;
    return (
      container.scrollHeight - container.scrollTop - container.clientHeight <
      SCROLL_THRESHOLD
    );
  }, []);

  const scrollToBottom = useCallback((smooth = true) => {
    if (!messageEndDivRef.current) return;
    messageEndDivRef.current.scrollIntoView({
      behavior: smooth ? "smooth" : "auto",
      block: "end",
    });
  }, []);

  // Initial load effect
  useEffect(() => {
    if (isInitialLoad && connectionId) {
      setIsInitialLoad(false);
    }
  }, [connectionId, isInitialLoad]);

  // Scroll effect
  useEffect(() => {
    if (!messageEndDivRef.current || !chatContainerRef.current) return;

    if (isInitialLoad) {
      scrollToBottom(false);
      setIsInitialLoad(false);
      return;
    }

    const shouldScroll =
      isNearBottom() ||
      messages?.messages[messages.messages.length - 1]?.senderId ===
        sessionData?.user?.id;

    if (shouldScroll) {
      scrollToBottom();
    }
  }, [
    messages?.messages.length,
    sessionData?.user?.id,
    isInitialLoad,
    isNearBottom,
    scrollToBottom,
  ]);

  if (status === "loading") return null;

  const groupedMessages = messages?.messages.reduce<
    Record<string, CustomerAgentConnectionMessages[]>
  >((groups, message) => {
    const groupTitle = getGroupTitle(new Date(message.createdAt));
    if (!groups[groupTitle]) {
      groups[groupTitle] = [];
    }
    groups[groupTitle].push(message);
    return groups;
  }, {});

  const handleSendMessage = () => {
    if (!newMessage.trim() || !sessionData?.user?.id || !connectionId) return;

    sendMessage(
      {
        content: newMessage.trim(),
        senderId: sessionData.user.id,
        connectionId: connectionId,
      },
      {
        onSuccess: () => {
          void trpcUtils.chat.getConversations.invalidate();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      <div
        ref={chatContainerRef}
        className="relative flex max-h-[calc(100vh-32vh)] min-h-[calc(100vh-32vh)] flex-col items-center gap-5 overflow-y-auto py-4 lg:px-5"
      >
        <div className="flex w-full flex-col gap-2">
          {groupedMessages &&
            Object.entries(groupedMessages).map(
              ([groupTitle, groupMessages]) => (
                <div key={groupTitle} className="flex w-full flex-col gap-2">
                  <div className="sticky top-0 z-10 flex w-full items-center justify-center">
                    <Badge className="z-10 w-fit bg-secondary-2-100 font-airbnb_w_md text-sm font-medium text-secondary-2-700">
                      {groupTitle}
                    </Badge>
                  </div>

                  {groupMessages.map((message) =>
                    message.senderId === sessionData?.user?.id ? (
                      <SentMessage key={message.id} message={message} />
                    ) : (
                      <ReceivedMessage key={message.id} message={message} />
                    ),
                  )}
                </div>
              ),
            )}
          <div ref={messageEndDivRef} />
        </div>
      </div>

      <div className="w-full pl-2.5">
        <div className="relative w-full">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            className="border-secondary-2-100 pr-10 text-primary-2-800 placeholder:text-primary-2-800"
            placeholder="Type your message..."
          />
          <SendIcon
            className="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer"
            strokeWidth={1}
            onClick={handleSendMessage}
          />
        </div>
      </div>
    </>
  );
};

export default CustomerAgentChatScreenBody;
