import { Badge } from "@repo/ui/components/ui/badge";
import React, { useCallback, useEffect, useRef, useState } from "react";
import ReceivedMessage from "./received-message";
import SentMessage from "./sent-message";
import { Input } from "@repo/ui/components/ui/input";
import { SendIcon } from "lucide-react";
import type { Message } from "@repo/database";
import { useSession } from "next-auth/react";
import { api } from "~/trpc/react";
import { useSearchParams } from "next/navigation";
import { toast } from "@repo/ui/components/ui/sonner";
import { formatDate } from "date-fns";
import type { TEmit } from "@repo/partner-api/src/router/chat";

const SCROLL_THRESHOLD = 100;
const RECONNECTION_ATTEMPTS = 10;
const RECONNECTION_DELAY = 2000; // 2 seconds

interface ChatScreenBodyProps {
  messages?: Message[] | undefined;
  blocked: boolean;
}

const getGroupTitle = (date: Date): string => {
  const now = new Date();
  const diffInDays = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),
  );

  if (diffInDays === 0) return "Today";
  if (diffInDays === 1) return "Yesterday";
  return formatDate(date, "dd, eeee");
};

const ChatScreenBody: React.FC<ChatScreenBodyProps> = ({
  messages,
  blocked,
}) => {
  const [newMessage, setNewMessage] = useState<string>("");
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [reconnectionAttempt, setReconnectionAttempt] = useState(0);
  const [isReconnecting, setIsReconnecting] = useState(false);

  const messageEndDivRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const reconnectionTimeoutRef = useRef<NodeJS.Timeout>(null);

  const searchParams = useSearchParams();
  const connectionId = searchParams.get("id");
  const { data: sessionData, status } = useSession();
  const trpcUtils = api.useUtils();

  const { mutate: sendMessage } = api.chat.sendMessage.useMutation({
    onSuccess: () => {
      setNewMessage("");
      void trpcUtils.invalidate();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const { mutate: toggleStatus } = api.chat.toggleStatus.useMutation();

  const handleSubscriptionData = useCallback(
    (content: TEmit) => {
      if (!messages || !connectionId) return;
      void trpcUtils.invalidate();
      setReconnectionAttempt(0); // Reset reconnection attempts on successful data
    },
    [connectionId, messages, trpcUtils],
  );

  const handleSubscriptionError = useCallback(() => {
    if (reconnectionAttempt < RECONNECTION_ATTEMPTS && !isReconnecting) {
      setIsReconnecting(true);
      reconnectionTimeoutRef.current = setTimeout(() => {
        setReconnectionAttempt((prev) => prev + 1);
        setIsReconnecting(false);
        void trpcUtils.invalidate();
      }, RECONNECTION_DELAY);
    } else if (reconnectionAttempt >= RECONNECTION_ATTEMPTS) {
      toast.error("Failed to connect to chat. Please refresh the page.");
    }
  }, [reconnectionAttempt, isReconnecting, trpcUtils]);

  const subscription = api.chat.onNewMessage.useSubscription(
    { connectionId: searchParams.get("id") ?? "" },
    {
      onData: handleSubscriptionData,
      onError: handleSubscriptionError,
    },
  );

  const isNearBottom = useCallback(() => {
    if (!chatContainerRef.current) return false;
    const container = chatContainerRef.current;
    return (
      container.scrollHeight - container.scrollTop - container.clientHeight <
      SCROLL_THRESHOLD
    );
  }, []);

  const scrollToBottom = useCallback((smooth = true) => {
    if (!messageEndDivRef.current) return;
    messageEndDivRef.current.scrollIntoView({
      behavior: smooth ? "smooth" : "auto",
      block: "end",
    });
  }, []);

  useEffect(() => {
    if (isInitialLoad && connectionId) {
      setIsInitialLoad(false);
    }
  }, [connectionId, isInitialLoad]);

  // use effect to scroll the chat screen on initial load of page
  useEffect(() => {
    if (!messageEndDivRef.current || !chatContainerRef.current) return;

    if (isInitialLoad) {
      scrollToBottom(false);
      setIsInitialLoad(false);
      return;
    }

    const shouldScroll =
      isNearBottom() ||
      messages?.[messages.length - 1]?.senderId === sessionData?.user?.id;

    if (shouldScroll) {
      scrollToBottom();
    }
  }, [
    messages?.length,
    sessionData?.user?.id,
    isInitialLoad,
    isNearBottom,
    scrollToBottom,
  ]);

  // ! Fix: This useEffect is not working on tab close needed to be fix
  // use effect to set user offline as soons as user closes the tab.
  useEffect(() => {
    const handleBeforeUnload = () => {
      toggleStatus(
        { status: false },
        {
          onSuccess: () => {
            void trpcUtils.invalidate();
          },
          onError: (error) => {
            console.error("Failed to update connection status", error);
          },
        },
      );
    };

    window.addEventListener("unload", handleBeforeUnload);
    return () => {
      window.removeEventListener("unload", handleBeforeUnload);
    };
  }, [toggleStatus, trpcUtils]);

  useEffect(() => {
    return () => {
      if (reconnectionTimeoutRef.current) {
        clearTimeout(reconnectionTimeoutRef.current);
      }
    };
  }, []);

  if (status === "loading") return null;

  const groupedMessages = messages?.reduce<Record<string, Message[]>>(
    (groups, message) => {
      const groupTitle = getGroupTitle(new Date(message.createdAt));
      if (!groups[groupTitle]) {
        groups[groupTitle] = [];
      }
      groups[groupTitle].push(message);
      return groups;
    },
    {},
  );

  const handleSendMessage = () => {
    if (!newMessage.trim() || !sessionData?.user?.id || !connectionId) return;

    sendMessage({
      content: newMessage.trim(),
      senderId: sessionData.user.id,
      connectionId: connectionId,
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      <div
        ref={chatContainerRef}
        className="relative flex max-h-[calc(100vh-32vh)] min-h-[calc(100vh-32vh)] flex-col items-center gap-5 overflow-y-auto py-4 lg:px-5"
      >
        {/* {isReconnecting && (
          <Badge className="sticky top-0 bg-yellow-100 text-yellow-800">
            Reconnecting... Attempt {reconnectionAttempt + 1}/
            {RECONNECTION_ATTEMPTS}
          </Badge>
        )} */}

        <div className="flex w-full flex-col gap-2">
          {groupedMessages &&
            Object.entries(groupedMessages).map(
              ([groupTitle, groupMessages]) => (
                <div key={groupTitle} className="flex w-full flex-col gap-2">
                  <div className="sticky top-0 z-10 flex w-full items-center justify-center">
                    <Badge className="z-10 w-fit bg-secondary-2-100 font-airbnb_w_md text-sm font-medium text-secondary-2-700">
                      {groupTitle}
                    </Badge>
                  </div>

                  {groupMessages.map((message) =>
                    message.senderId === sessionData?.user?.id ? (
                      <SentMessage key={message.id} message={message} />
                    ) : (
                      <ReceivedMessage key={message.id} message={message} />
                    ),
                  )}
                </div>
              ),
            )}
          <div ref={messageEndDivRef} />
        </div>
      </div>

      <div className="w-full pl-2.5">
        {blocked ? (
          <div className="mx-4 flex items-center justify-center rounded-md bg-primary-100 py-4">
            This conversation is blocked.
          </div>
        ) : (
          <div className="relative w-full">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              className="border-secondary-2-100 pr-10 text-primary-2-800 placeholder:text-primary-2-800"
              placeholder="Type your message..."
            />
            <SendIcon
              className="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer"
              strokeWidth={1}
              onClick={handleSendMessage}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default ChatScreenBody;
