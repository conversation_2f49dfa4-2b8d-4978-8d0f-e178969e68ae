import { Badge } from "@repo/ui/components/ui/badge";
import { format } from "date-fns";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import type { CustomerAgentConversationsWithExtraDetails } from "./page";
import { api } from "~/trpc/react";

const CustomerChatListItem = ({
  id,
  messages,
  customer,
  agentUnseenMessagesCount,
  property,
}: CustomerAgentConversationsWithExtraDetails) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data, status: loadingStatusSession } = useSession();

  const { mutate: toggleStatus } = api.chat.toggleStatus.useMutation();
  const { mutate: updateUnseenNotificationsCount } =
    api.chat.updateUnseenMessagesCount.useMutation();
  const trpcUtils = api.useUtils();
  const currentConversationId = searchParams.get("id");

  const updateSearchParams = (newId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("id", newId);
    params.set("userType", "customer");
    router.replace(`?${params.toString()}`, { scroll: false });
  };

  const handleConverseClick = () => {
    toggleStatus(
      { status: true },
      {
        onSuccess: () => {
          void trpcUtils.invalidate();
        },
        onError: (error) => {
          console.error("Failed to update connection status", error);
        },
      },
    );

    if (id !== currentConversationId) {
      updateSearchParams(id);
    }

    updateUnseenNotificationsCount({
      connectionId: "",
      chatOpendByUserId: String(data?.user?.id),
      customerAgentConnectionId: id,
    });
  };

  if (loadingStatusSession === "loading") return null;

  return (
    <div
      onClick={handleConverseClick}
      className={`flex w-full cursor-pointer justify-between gap-4 rounded-lg px-3 pb-4 pt-3 ${
        id === currentConversationId ? "bg-[#fff6f4]" : "bg-text-30"
      }`}
    >
      {/* left div */}
      <div className="flex w-full items-start gap-3">
        {/* image */}
        <div className="relative aspect-square w-9 xl:w-[46px]">
          <Image
            src={
              customer.cloudinaryImagePublicUrl ??
              customer.profileImagePublicUrl ??
              "/images/placeholder-user-image.jpg"
            }
            className="relative rounded-full object-cover"
            alt={`${customer.name}'s profile`}
            fill
          />
        </div>
        {/* content */}
        <div className="flex w-full flex-col gap-2">
          {/* top div with user name, property name and badge */}
          <div className="flex items-start justify-between gap-2">
            {/* name */}
            <div className="space-y-0.5">
              <h2 className="font-airbnb_w_bd font-bold text-primary-2-800">
                {customer.name}
              </h2>
              {property?.propertyTitle && (
                <p className="line-clamp-2 font-airbnb_w_bk text-xs text-[#1e1e1e]">
                  Property Name: {property.propertyTitle}
                </p>
              )}
            </div>
            {/* badge */}
            <Badge className="rounded-sm bg-secondary-2-100 px-2 py-1 font-airbnb_w_md text-xs font-medium">
              Customer
            </Badge>
          </div>
          {/* bottom div with message */}
          <p className="line-clamp-1 font-airbnb_w_bk text-xs text-primary-2-800 lg:text-sm">
            {messages[0]?.content}
          </p>
        </div>
      </div>
      {/* right div */}
      {agentUnseenMessagesCount && messages[0]?.createdAt ? (
        <div className="flex flex-col items-end gap-2 text-nowrap">
          <div className="font-airbnb_w_md text-[10px] font-medium leading-3 text-primary-2-800 lg:text-xs">
            {format(new Date(messages[0].createdAt), "hh:mm aa")}
          </div>

          <div className="size-5 rounded-full bg-secondary-500 p-[3px] text-center text-xs font-medium text-white">
            {agentUnseenMessagesCount}
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default CustomerChatListItem;
