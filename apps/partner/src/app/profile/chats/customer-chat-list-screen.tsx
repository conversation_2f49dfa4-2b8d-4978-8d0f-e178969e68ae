import { useRouter } from "next/navigation";
import { ChevronLeft } from "lucide-react";
import { Separator } from "@repo/ui/components/ui/separator";
import ChatListItemSkeleton from "./chat-list-item-skeleton";
import type { CustomerAgentConversationsWithExtraDetails } from "./page";
import React, { memo } from "react";
import CustomerChatListItem from "./customer-chat-list-item";
import SearchBar from "~/app/_components/shared/search-bar";
import AgentCustomerToggle from "./agent-customer-toggle";

const CustomerChatListScreen = ({
  customerAgentConversations,
  searchedResults,
  searchQuery,
  //   setSearchQuery,
  isLoadingConversations,
  isLoadingSearchedConversation,
}: {
  customerAgentConversations: CustomerAgentConversationsWithExtraDetails[] | [];
  searchedResults: CustomerAgentConversationsWithExtraDetails[] | [];
  isLoadingConversations: boolean;
  isLoadingSearchedConversation: boolean;
  searchQuery: string | undefined;
  //   setSearchQuery: (v: string | undefined) => void;
}) => {
  const router = useRouter();

  return (
    <div className="w-full">
      <div className="flex items-center justify-between pr-2">
        <span className="flex cursor-pointer items-center gap-4 p-2 text-lg font-medium text-text-550 md:py-3 md:text-xl lg:h-[76px] xl:px-4 xl:text-2xl">
          <ChevronLeft
            onClick={() => router.back()}
            className="size-[18px] text-primary-2-800 lg:size-5 2xl:size-6"
          />
          Chat
        </span>
        <AgentCustomerToggle />
      </div>
      <Separator></Separator>
      <div className="flex w-full items-center gap-2 px-2 py-3 xl:px-4">
        <SearchBar queryKey="convQuery" autoSetParams></SearchBar>
      </div>
      <div className="flex max-h-[calc(100vh-25vh)] flex-col gap-2 overflow-hidden overflow-y-auto lg:px-2 xl:px-4">
        {isLoadingConversations || isLoadingSearchedConversation ? (
          Array.from({ length: 5 }).map((_, idx) => (
            <ChatListItemSkeleton key={idx} />
          ))
        ) : searchedResults.length ? (
          searchedResults.map((item) => (
            <React.Fragment key={item.id}>
              <CustomerChatListItem {...item} />
              <Separator className="h-0.5 bg-primary-200" />
            </React.Fragment>
          ))
        ) : !searchQuery && customerAgentConversations.length ? (
          <>
            {/* Customer Agent conversations */}
            {customerAgentConversations.map((item) => (
              <React.Fragment key={item.id}>
                <CustomerChatListItem {...item} />
                <Separator className="h-0.5 bg-primary-200" />
              </React.Fragment>
            ))}
          </>
        ) : (
          <p className="flex min-h-28 items-center justify-center text-xl font-semibold text-primary-700">
            No Conversation Found
          </p>
        )}
      </div>
    </div>
  );
};

export default memo(CustomerChatListScreen);
