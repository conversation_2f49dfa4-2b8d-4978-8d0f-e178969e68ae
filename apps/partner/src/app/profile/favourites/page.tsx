import React from "react";
import ProfilePageTitleWithBackButton from "~/app/_components/shared/profile-title";
import { api } from "~/trpc/server";
import FavouriteAgentsList from "./favourite-agents-list";

const RecentlyViewed = async () => {
  const data = await api.likedAgents.getLikedAgents();
  const agents = data.length > 0 ? data : [];

  return (
    <div className="space-y-4">
      <ProfilePageTitleWithBackButton title="Favourite Agents" />

      <FavouriteAgentsList agents={agents} />
    </div>
  );
};

export default RecentlyViewed;
