import type { Prisma } from "@repo/database";
import React from "react";
import AgentCard from "@repo/ui/components/shared/agent-card";

type IAgent = Prisma.UserGetPayload<{
  select: {
    id: true;
    name: true;
    experience: true;
    filePublicUrl: true;
    cloudinaryProfileImageUrl: true;
    propertiesSold: true;
    rating: true;
    createdAt: true;
    verifiedAgent: true;
  };
}>;

const FavouriteAgentsList = ({ agents }: { agents: IAgent[] }) => {
  return (
    <div className="grid w-full max-w-full grid-cols-1 gap-4 md:grid-cols-2 lg:gap-[22px] xl:grid-cols-3 xl:gap-5 2xl:grid-cols-4">
      {agents.length > 0 ? (
        agents.map((item) => (
          <AgentCard
            name={item.name}
            experience={item.experience}
            filePublicUrl={item.filePublicUrl}
            cloudinaryProfileImageUrl={item.cloudinaryProfileImageUrl}
            propertiesSold={item.propertiesSold}
            rating={item.rating}
            id={item.id}
            createdAt={item.createdAt}
            verifiedAgent={item.verifiedAgent}
          ></AgentCard>
        ))
      ) : (
        <div className="col-span-full flex min-h-[20vh] items-center justify-center text-center text-sm text-primary-2-800 md:text-base lg:text-xl">
          Your liked agents will appear here.
        </div>
      )}
    </div>
  );
};

export default FavouriteAgentsList;
