import ContactCard from "./contact-card";
import HeadingBadge from "../_components/shared/heading-badge";
import { ContactInfo } from "../profile/constants";

const ContactInfoSection = () => {
  return (
    <div className="flex flex-col items-start gap-6 rounded-3xl border-2 border-primary-2-750 bg-[#FFFDFC] p-4 md:p-5 2xl:p-10">
      {/* title and summary */}
      <HeadingBadge
        content="Help & Support"
        className="bg-white text-secondary-2-700"
        fileUrl="/icons/shared/red-grid.svg"
      />
      <p className="font-airbnb_w_bk text-sm text-text-600 lg:text-base">
        Feel free to reach out to us with any inquiries, feedback, or requests.
        Our dedicated team is committed to providing you with exceptional
        customer service. We look forward to hearing from you!
      </p>

      {/* contact details */}
      <div className="flex w-full flex-col gap-2 md:flex-row xl:flex-col 2xl:flex-row">
        {ContactInfo.map((item, idx) => (
          <ContactCard
            key={idx}
            imageClassname="bg-secondary-2-100 p-1 md:p-2"
            iconClassname="size-6 xl:size-8"
            textClassname="font-bold text-primary-2-800 2xl:text-xl"
            subTextClassname="2xl:text-lg"
            {...item}
          />
        ))}
      </div>
    </div>
  );
};

export default ContactInfoSection;
