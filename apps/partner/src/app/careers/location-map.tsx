"use client";

import Link from "next/link";
import PolygonOnMap from "@repo/ui/components/shared/polygon-on-map";
import HeadingBadge from "../_components/shared/heading-badge";

const LocationMap = () => {
  return (
    <div className="flex flex-col items-start gap-2.5 rounded-3xl border-2 border-primary-2-750 bg-[#FFFDFC] p-4 md:gap-[14px] md:p-5 lg:gap-4 2xl:gap-6 2xl:p-10">
      {/* title and address */}
      <HeadingBadge
        content="Our Locations"
        className="bg-white text-secondary-2-700"
        fileUrl="/icons/shared/red-grid.svg"
      />
      <div className="font-airbnb_w_md text-base font-medium text-text-600 xl:text-lg">
        <p> Barasingha Infratech Private(pvt) Limited (Ltd) </p>
        <Link
          href="https://www.google.com/maps/place/Barasingha+Infratech+Private(pvt)+Limited+(Ltd)/@28.5852342,77.309082,16z/data=!4m10!1m2!2m1!1s15+metro,+Block-F,+office+number+F02,+B69,+Sector+2,+Noida,+Uttar+Pradesh+201301!3m6!1s0x390ce580dfb8e019:0x7c9f40fd0f598a65!8m2!3d28.585581!4d77.3171377!15sClAxNSBtZXRybywgQmxvY2stRiwgb2ZmaWNlIG51bWJlciBGMDIsIEI2OSwgU2VjdG9yIDIsIE5vaWRhLCBVdHRhciBQcmFkZXNoIDIwMTMwMVpMIkoxNSBtZXRybyBibG9jayBmIG9mZmljZSBudW1iZXIgZjAyIGI2OSBzZWN0b3IgMiBub2lkYSB1dHRhciBwcmFkZXNoIDIwMTMwMZIBHWNvbXB1dGVyX3N1cHBvcnRfYW5kX3NlcnZpY2Vz4AEA!16s%2Fg%2F11wxjrjrmm?entry=ttu&g_ep=EgoyMDI1MDMyMy4wIKXMDSoASAFQAw%3D%3D"
          className="cursor-pointer"
          target="_blank"
        >
          {" "}
          15 metro, Block-F, office number F02, B69, Sector 2, Noida, Uttar
          Pradesh 201301
        </Link>
      </div>
      <PolygonOnMap
        disableMapInteraction
        className="h-[160px] w-full rounded-md"
        point={{
          lat: 28.58053,
          lng: 77.31795,
        }}
      />
    </div>
  );
};

export default LocationMap;
