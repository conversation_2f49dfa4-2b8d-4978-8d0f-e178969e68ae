import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@repo/ui/components/ui/accordion";
import HeadingBadge from "../_components/shared/heading-badge";
import { api } from "~/trpc/server";

const CareersFAQ = async () => {
  const faqs = await api.faq.getAllFaq({
    project: "B2B_DEER_CONNECT",
    page: "CAREER_PAGE",
  });

  if (faqs.length === 0) {
    return null;
  }

  return (
    <div className="space-y-5 py-9 md:space-y-6 md:py-10 lg:py-[50px] xl:space-y-[30px] xl:py-[65px] 2xl:space-y-[60px] 2xl:py-[70px]">
      <div className="flex flex-col items-center gap-1 md:gap-2 2xl:gap-3">
        <HeadingBadge
          content="FAQ"
          className="rounded px-2.5 py-1 md:rounded-md xl:rounded-lg xl:px-4 xl:py-1.5"
        />
        <h1 className="text-center font-airbnb_w_bd text-xl font-bold xl:text-2xl 2xl:text-5xl">
          You Have Question. We've Your Answer
        </h1>
      </div>

      <Accordion
        type="single"
        className="space-y-4 md:space-y-[18px] xl:space-y-6 2xl:space-y-8"
        collapsible
      >
        {faqs.map((item) => (
          <AccordionItem
            key={item.id}
            value={`item-${item.id}`}
            className="flex flex-col gap-2.5 rounded-xl border border-text-500 border-opacity-30 p-4 data-[state=open]:border-[#5f3924] data-[state=open]:border-opacity-30 data-[state=open]:bg-[#FFFCF4] md:gap-[14px] md:p-5 xl:gap-5 xl:p-8"
          >
            <AccordionTrigger className="py-0 text-start font-airbnb_w_md text-base font-medium text-text-550 data-[state=open]:text-primary-2-750 xl:text-lg 2xl:text-xl">
              {item.question}
            </AccordionTrigger>
            <AccordionContent className="pb-0 font-airbnb_w_bk text-sm font-normal text-text-550 xl:text-base 2xl:text-lg">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default CareersFAQ;
