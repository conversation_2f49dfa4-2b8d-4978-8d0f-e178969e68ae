"use client";

import Link, { LinkProps } from "next/link";
import Image from "next/image";
import { cn } from "@repo/ui/lib/utils";

interface ContactCardProp extends LinkProps {
  icon: string;
  text: string;
  relevantInfo: string;
  imageClassname?: string;
  iconClassname?: string;
  textClassname?: string;
  subTextClassname?: string;
}

const ContactCard: React.FC<ContactCardProp> = ({
  icon,
  text,
  imageClassname,
  iconClassname,
  textClassname,
  subTextClassname,
  relevantInfo,
  ...props
}) => {
  return (
    <Link
      {...props}
      className="flex w-full items-start gap-2.5 px-4 py-3 md:gap-3 lg:gap-4"
    >
      <div
        className={cn(
          "flex items-center justify-center rounded-full bg-primary-2-100 p-1 md:p-1.5 lg:p-2 xl:p-2.5",
          imageClassname,
        )}
      >
        <Image
          src={icon}
          alt={text}
          height={40}
          width={40}
          className={cn("size-5 md:size-6 lg:size-8 xl:size-10", iconClassname)}
        />
      </div>
      <div className="spze-y-[2px] md:space-y-1 lg:space-y-1.5 xl:space-y-2">
        <p
          className={cn(
            "font-airbnb_w_md text-base font-medium text-primary-2-750 lg:text-lg xl:text-xl 2xl:text-2xl",
            textClassname,
          )}
        >
          {text}
        </p>
        <p
          onClick={() => navigator.clipboard.writeText(relevantInfo)}
          className={cn(
            "font-airbnb_w_md text-sm font-medium text-text-550 lg:text-base xl:text-lg 2xl:text-xl",
            subTextClassname,
          )}
        >
          {relevantInfo}
        </p>
      </div>
    </Link>
  );
};

export default ContactCard;
