"use client";

import { useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { z } from "zod";
import { toast } from "@repo/ui/components/ui/sonner";
import { resumeFormSchema } from "@repo/validators";
import { Button } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { api } from "~/trpc/react";

interface ResumeUploadFormProps {
  jobRoles: {
    id: string;
    name: string;
  }[];
}

const ResumeUploadForm = ({ jobRoles }: ResumeUploadFormProps) => {
  const awsMutation = api.aws.getPresignedUrlWithoutLogin.useMutation();
  const uploadResume = api.uploadResume.resumeFormData.useMutation();
  const ACCEPTED_FILE_TYPES = ["application/pdf"];
  const resumeFieldRef = useRef<HTMLInputElement>(null);

  const uploadFile = async (file: File) => {
    const fileType = file.type;
    const fileName = file.name;
    if (!file) {
      return;
    }

    const date = new Date();

    try {
      const { url, key } = await awsMutation.mutateAsync({
        fileName: `/resume/${date.getTime()}-${fileName}`,
        contentType: fileType,
      });

      if (!url) {
        return;
      }

      console.log("url", url);
      const requestOptions = {
        method: "PUT",
        body: file,
      };
      const res = await fetch(url, requestOptions);
      console.log(res, url);
      if (res.ok) {
        form.setValue("resumeFileKey", key);
        return;
      }
      toast.error("Error uploading file");
    } catch (e) {
      toast.error("Error uploading file");
    }
  };

  const form = useForm<z.infer<typeof resumeFormSchema>>({
    resolver: zodResolver(resumeFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phoneNumber: "",
      role: "",
      resumeFileKey: "",
    },
  });

  async function onSubmit(values: z.infer<typeof resumeFormSchema>) {
    console.log("values are", values);
    await uploadResume
      .mutateAsync({ ...values })
      .then((resp) => {
        if (resp.messageTitle) {
          toast.success(resp.messageTitle);
          form.reset();
          form.setValue("resumeFileKey", "");
          // Reset the resume field (file input)
          if (resumeFieldRef.current) {
            resumeFieldRef.current.value = "";
          }
        }
        if (resp.error) {
          toast.error(resp.error);
          console.log("erro is", resp.error);
        }
      })
      .catch((err) => {
        console.log("error is", err);
      });
  }

  return (
    <div className="order-1 flex w-full flex-col gap-5 rounded-3xl border-2 border-primary-2-750 bg-[#FFFDFC] p-4 md:gap-6 md:p-5 lg:w-[80%] lg:p-8 xl:order-2 xl:w-[38%] xl:gap-8 2xl:gap-6">
      <div className="flex flex-col items-center gap-[2px] lg:gap-2 xl:gap-[14px]">
        <div className="font-airbnb_w_bd text-lg font-bold text-text-700 md:text-xl lg:text-2xl 2xl:text-3xl">
          Fill up the form
        </div>
        <p className="font-airbnb_w_bk text-sm font-normal text-text-600 md:text-base 2xl:text-xl">
          Fill up the form to submit your resume
        </p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-[18px]">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter your name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Enter your email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="eg. 1234567890"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  What is the role that you are applying for?
                </FormLabel>
                <FormControl>
                  <Select {...field} onValueChange={field.onChange}>
                    {jobRoles.length > 0 ? (
                      <SelectTrigger className="">
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                    ) : (
                      <SelectTrigger className="" disabled>
                        <SelectValue placeholder="No roles available" />
                      </SelectTrigger>
                    )}
                    <SelectContent>
                      {jobRoles.map((role) => (
                        <SelectItem value={role.id} key={role.id}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="resumeFileKey"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Upload Resume</FormLabel>
                <FormControl className="flex items-center gap-2">
                  <>
                    <Input
                      ref={resumeFieldRef}
                      type="file"
                      accept={ACCEPTED_FILE_TYPES.join(",")}
                      placeholder="Upload .pdf, .doc"
                      value={undefined}
                      onChange={async (e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          await uploadFile(file);
                        }
                      }}
                    />
                    {awsMutation.isPending && (
                      <div className="text-sm">Uploading...</div>
                    )}
                  </>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="mt-6 w-full xl:mt-8 2xl:mt-9">
            Apply
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default ResumeUploadForm;
