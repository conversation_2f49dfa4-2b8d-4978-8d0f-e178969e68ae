import type { <PERSON>ada<PERSON> } from "next";
import { ReactNode } from "react";
import GoogleRecaptchaWrapper from "~/app/_components/shared/google-recaptcha-wrapper";

export const metadata: Metadata = {
  title: "Delete Account",
  description: "Information and process for deleting your DeerConnect account",
};

export default function DeleteAccountLayout({
  children,
}: {
  children: ReactNode;
}) {
  return <GoogleRecaptchaWrapper>{children}</GoogleRecaptchaWrapper>;
}
