"use client";

import { useRouter, useSearchParams } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@repo/ui/components/ui/dialog";
import AgentDetail from "@repo/ui/components/shared/agent-detail";
import { api } from "~/trpc/react";
import ContactAgentButton from "./_components/agents/contact-agent-button";
import LikeAgentButton from "./_components/agents/agent-like-button";
import AgentPostCardsSwiper from "./_components/agents/agent-post-cards-swiper";
import AgentPropertyCardsSwiper from "./_components/agents/agent-property-cards-swiper";
import AgentTestimonialsSwiper from "./_components/agents/agents-testimonials-swiper";
import { useSession } from "next-auth/react";
import ShareAgentButton from "./_components/shared/share-agent-button";

const AgentProfileDialogprovider = () => {
  const searchParams = useSearchParams();
  const session = useSession();
  const router = useRouter();
  const agentId = searchParams.get("viewAgentId") ?? "";
  const { data: agent } = api.homePage.getAgentDetails.useQuery({
    id: agentId,
  });

  const recordProfileView = api.profileView.createProfileView.useMutation();

  if (!agent) {
    return;
  }

  const profileView = async (agentId: string) => {
    if (session.data?.user?.id != agentId) {
      await recordProfileView.mutateAsync(
        { viewerId: agentId },
        {
          onSuccess: () => {
            console.log("profile view created successfully");
          },
          onError: (err) => {
            console.log("error is", err.message);
          },
        },
      );
    }
  };

  const handleClose = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("viewAgentId");
    router.push(`?${params}`);
  };

  return (
    <>
      <Dialog
        open={agentId ? true : false}
        onOpenChange={(open) => {
          if (!open) handleClose();
        }}
      >
        <DialogContent
          onEscapeKeyDown={() => {
            handleClose();
          }}
        >
          <DialogTitle className="hidden">Agent Details</DialogTitle>
          <AgentDetail
            agent={agent}
            contactAgent={
              <ContactAgentButton
                agentId={agentId}
                className="rounded-lg md:rounded-xl"
              />
            }
            likeAgentBtn={<LikeAgentButton agentId={agentId} />}
            shareAgentProfileButton={<ShareAgentButton agentId={agentId} />}
            agentPostsCardSwiper={<AgentPostCardsSwiper posts={agent.posts} />}
            agentPropertyCardsSwiper={
              <AgentPropertyCardsSwiper properties={agent.properties} />
            }
            videoReviews={<AgentTestimonialsSwiper agentId={agentId} />}
            recordProfileView={() => profileView(agentId)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AgentProfileDialogprovider;
