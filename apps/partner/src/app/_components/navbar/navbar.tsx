"use client";
import React, { Suspense, useRef, useState } from "react";
import Image from "next/image";
import DesktopNavigation from "./desktop-navigation";
import Link from "next/link";
import MobileNavigation from "./mobile-navigation";
import PostPropertyButton from "./post-property-button";
import NotificationBellCount from "./notification-bell-count";
import NotificationChatCount from "./notification-chat-count";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@repo/ui/components/ui/sheet";
import {
  HitsPerPageProps,
  HitsProps,
  InfiniteHits,
  InstantSearch,
  SearchBox,
  useSearchBox,
} from "react-instantsearch";
import { instantMeiliSearch } from "@meilisearch/instant-meilisearch";
// import "instantsearch.css/themes/satellite.css";
import { Button } from "@repo/ui/components/ui/button";
import { Search } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { env } from "~/env";
import Onesignal from "~/app/_components/navbar/onesignal";
import type { Property, User } from "@repo/database";
import { useSession } from "next-auth/react";
import { useOnClickOutside } from "usehooks-ts";

const { searchClient } = instantMeiliSearch(
  env.NEXT_PUBLIC_MEILI_SEARCH_URL,
  env.NEXT_PUBLIC_MEILI_SEARCH_KEY,
  { placeholderSearch: false, keepZeroFacets: true, finitePagination: false },
);

const SearchBar = ({
  setSearchSheetOpen,
}: {
  setSearchSheetOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const router = useRouter();
  const inputRef = useRef<HTMLFormElement | null>(null);
  const resultRef = useRef(null);

  const SearchComponent = () => {
    const { refine, query } = useSearchBox();
    const handleClickOutside = () => {
      // Your custom logic here
      refine("");
      console.log("clicked outside");
    };
    // TODO: fix this and remove the ts-ignore
    // @ts-expect-error : implemented as done in official docs  */
    useOnClickOutside(resultRef, handleClickOutside);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Check if the pressed key is Enter
      if (e.key === "Enter" && inputRef.current !== null) {
        console.log("when e.key === Enter && inputRef.current !== null");
        // Get the input element
        const inputElement = inputRef.current.querySelector("input");

        // Check if input value exists and is not empty
        if (inputElement && inputElement.value.length > 0) {
          console.log("when inputElement && inputElement.value.length > 0 ");
          // Navigate to search page
          router.push(`/search/?query=${inputElement.value}`);

          // Clear the input value
          inputElement.value = "";

          // Reset the query in InstantSearch
          refine("");
          setSearchSheetOpen && setSearchSheetOpen(false);
        }
      }
    };

    return (
      <SearchBox
        formRef={inputRef}
        classNames={{
          input: `flex w-full p-2 px-3 items-center gap-2 rounded-lg border border-[#F4F0EE] bg-white py-2 px-3 pl-10 `,
          submitIcon: "absolute left-2.5 top-1/2 size-5 -translate-y-1/2",
          form: "flex ",
          resetIcon: "hidden",
          loadingIcon: "hidden",
          loadingIndicator: " hidden",
        }}
        className=""
        placeholder="Search for something"
        onKeyDown={handleKeyDown}
      />
    );
  };

  return (
    <div className="relative z-20">
      <InstantSearch
        indexName={env.NEXT_PUBLIC_PARTNER_INDEX}
        /* @ts-expect-error : implemented as done in official docs  */
        searchClient={searchClient}
      >
        <SearchComponent />
        <InfiniteHits
          ref={resultRef}
          classNames={{
            loadMore: "hidden",
            item: "hover:bg-secondary-2-500 w-full py-2 px-1 rounded-xl border-b-2 ",
          }}
          showPrevious={false}
          className="absolute left-0 w-full rounded-2xl bg-white/90 bg-opacity-40 shadow-[0px_1px_8px_0px_rgba(168,137,121,0.2)] backdrop-blur-3xl"
          hitComponent={Hit}
        />
      </InstantSearch>
    </div>
  );
};

type Hit = Pick<User, "id" | "name" | "filePublicUrl" | "userLocation"> & {
  mediaSections: {
    media: {
      filePublicUrl: string;
    }[];
  }[];
};

const Hit = ({ hit }: { hit: Hit }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  return (
    <div
      onClick={() => {
        const params = new URLSearchParams(searchParams);
        params.set("viewAgentId", hit.id);
        router.push(`?${params}`);
      }}
      className="relative z-[12] flex w-full cursor-pointer items-center gap-4"
    >
      <div className="relative aspect-square min-w-10">
        <Image
          src={hit.filePublicUrl ?? "/images/placeholder-user-image.jpg"}
          fill
          className="relative rounded-xl object-cover"
          alt={hit.name}
        />
      </div>
      <div>
        <p>{hit.name}</p>
        <p className="line-clamp-2 text-xs text-text-600">{hit.userLocation}</p>
      </div>
    </div>
  );
};

const Navbar = () => {
  const { data: session, status } = useSession();
  const [searchSheetOpen, setSearchSheetOpen] = useState(false);
  return (
    <nav className="sticky left-0 top-0 z-50 bg-primary-0 bg-opacity-40 shadow-[0px_1px_8px_0px_rgba(168,137,121,0.2)] backdrop-blur-[54.7px]">
      <div className="container mx-auto flex max-w-full items-center justify-between py-[10px] lg:px-[50px] lg:py-[12px] xl:px-[80px] xl:py-4 2xl:gap-[84px]">
        <Link
          href="/"
          className="relative aspect-[100/36] w-[80px] sm:w-[100px] md:aspect-[109/39] md:w-[109px] lg:aspect-[143/50] lg:w-[143px] xl:aspect-[161/57] xl:w-[161px]"
        >
          <Image
            src="/logos/new-logo.svg"
            fill
            alt="logo-main"
            className="relative object-cover"
          />
        </Link>

        {/* Visible on mobile Screens Only !  */}
        <div className="relative flex items-center gap-4 sm:gap-[18px] xl:gap-5 2xl:gap-6">
          {/* <div className="lg:hidden">
            <PostPropertyButton plusIcon></PostPropertyButton>
          </div> */}
          {/* search for mobile */}

          {status === "authenticated" && (
            <Sheet open={searchSheetOpen} onOpenChange={setSearchSheetOpen}>
              <SheetTrigger className="md:hidden" asChild>
                <Search
                  strokeWidth={1.5}
                  className="size-5 text-[#5F2800] lg:size-6 xl:size-[30px]"
                ></Search>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle className="text-center">Search</SheetTitle>
                </SheetHeader>
                <SearchBar setSearchSheetOpen={setSearchSheetOpen}></SearchBar>
              </SheetContent>
            </Sheet>
          )}
          {/* desktop search */}
          {status === "authenticated" && session.user && (
            <div className="hidden md:block">
              <SearchBar></SearchBar>
            </div>
          )}
          <div className="hidden lg:block">
            <DesktopNavigation />
          </div>
          <div className="flex items-center gap-4 lg:hidden">
            <NotificationBellCount />
            <NotificationChatCount />
          </div>
          <div className="">
            <Suspense fallback={<div>Loading...</div>}>
              <PostPropertyButton />
            </Suspense>
          </div>
          {/* Visible on Mobile Screens Only ! */}
          <div className="block lg:hidden">
            <MobileNavigation />
          </div>
          <Onesignal />
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
