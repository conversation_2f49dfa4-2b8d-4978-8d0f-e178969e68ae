import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import AgentReviewCard from "@repo/ui/components/shared/agent-review-card";

import { api } from "~/trpc/react";
const AgentTestimonialsSwiper = ({ agentId }: { agentId: string }) => {
  const { data: agentReviews } = api.agentReviews.getAgentReviews.useQuery({
    agentId: agentId,
  });
  const allAgentReviews = agentReviews ?? [];
  return (
    <>
      {allAgentReviews.length == 0 && (
        <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-sm text-primary-2-800 md:text-base xl:text-lg 2xl:text-xl">
          No user testimonials.
        </div>
      )}
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {allAgentReviews.length > 0 &&
            allAgentReviews.map((item, index) => (
              <CarouselItem key={index} className="basis-[316px]">
                <AgentReviewCard item={item} />
              </CarouselItem>
            ))}
        </CarouselContent>
      </Carousel>
    </>
  );
};

export default AgentTestimonialsSwiper;
