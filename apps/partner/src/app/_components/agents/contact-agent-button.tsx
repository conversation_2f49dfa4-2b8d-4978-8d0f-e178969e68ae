"use client";

import { Button } from "@repo/ui/components/ui/button";
import React from "react";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";
import { toast } from "@repo/ui/components/ui/sonner";

const ContactAgentButton = ({
  agentId,
  className,
}: {
  agentId: string;
  className?: string;
}) => {
  const { data } = useSession();
  const { mutate: sendconnectionReq } =
    api.connection.sendConnectionRequest.useMutation();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    // e.stopPropagation();
    e.preventDefault();
    const userId = data?.user?.id;

    if (!userId) return;

    sendconnectionReq(
      {
        receiverId: agentId,
      },
      {
        onSuccess: (opts) => {
          if (opts.warning) {
            toast.warning(opts.message);
            return;
          }

          toast.success(opts.message);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <Button variant="default" className={className} onClick={handleClick}>
      Contact Agent
    </Button>
  );
};

export default ContactAgentButton;
