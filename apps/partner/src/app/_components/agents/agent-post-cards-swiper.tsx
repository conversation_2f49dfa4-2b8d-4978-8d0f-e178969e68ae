"use client";
import { api } from "~/trpc/react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import PostCard from "@repo/ui/components/shared/post-card";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter, useSearchParams } from "next/navigation";
import type { Prisma } from "@repo/database";
import ReportPostDialog from "~/app/profile/feed/report-post-dialog";
import { useSession } from "next-auth/react";

export type IPost = Prisma.PostGetPayload<{
  include: {
    user: {
      select: {
        id: true;
        filePublicUrl: true;
        cloudinaryProfileImageUrl: true;
        name: true;
        company: {
          select: {
            companyName: true;
          };
        };
      };
    };
    media: {
      select: {
        filePublicUrl: true;
        cloudinaryUrl: true;
        cloudinaryId: true;
        mediaType: true;
      };
    };
    comments: {
      select: {
        comment: true;
        isPinned: true;
        createdAt: true;
        customerId: true;
        userId: true;
        user: {
          select: {
            id: true;
            name: true;
            filePublicUrl: true;
            companyDetails: {
              select: {
                companyName: true;
              };
            };
          };
        };
        customer: {
          select: {
            id: true;
            name: true;
            profileImagePublicUrl: true;
          };
        };
      };
    };
    likes: {
      select: {
        id: true;
        postId: true;
      };
    };
  };
}>;
const AgentPostCardsSwiper = ({ posts }: { posts: IPost[] }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { mutate: likePost } = api.social.likePost.useMutation();
  //   const { mutate: unlikePost } = api.social.unlikePost.useMutation();
  const { mutate: newComment } = api.social.newComment.useMutation();
  const trcpUtils = api.useUtils();

  const handlePostClick = (postId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("postId", postId);
    router.replace(`?${params}`, { scroll: false });
  };
  const session = useSession();

  return (
    <>
      {posts.length == 0 && (
        <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-sm text-primary-2-800 md:text-base xl:text-lg 2xl:text-xl">
          No posts.
        </div>
      )}
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {posts.length > 0 &&
            posts.map((item, index) => {
              const handleLike = () => {
                likePost(
                  { postId: item.id },
                  {
                    onSuccess: () => {
                      void trcpUtils.homePage.getAgentDetails.invalidate();
                      void trcpUtils.user.getPosts.invalidate();
                      void trcpUtils.social.invalidate();
                    },
                    onError: (opts) => {
                      toast.error(opts.message);
                    },
                  },
                );
              };
              const handleComment = (postId: string, comment: string) => {
                newComment(
                  { comment, postId },
                  {
                    onSuccess: () => {
                      void trcpUtils.homePage.getAgentDetails.invalidate();
                      void trcpUtils.user.getPosts.invalidate();
                      void trcpUtils.social.invalidate();
                    },
                    onError: (opts) => {
                      toast.error(opts.message);
                    },
                  },
                );
              };

              return (
                <CarouselItem
                  key={index}
                  className="basis-[360px] md:basis-[390px] 2xl:basis-[500px]"
                >
                  <PostCard
                    isLiked={item.likes.length > 0 ? true : false}
                    onLike={handleLike}
                    onComment={handleComment}
                    onPostClick={handlePostClick}
                    hideReportButton={session.data?.user?.id === item.user.id}
                    hideDeleteButton={session.data?.user?.id === item.user.id}
                    formatTime={(date) => date.toLocaleDateString()}
                    formatCount={(count) => count.toString()}
                    reportPostDialogForm={
                      <ReportPostDialog postId={item.id}></ReportPostDialog>
                    }
                    {...item}
                  />
                </CarouselItem>
              );
            })}
        </CarouselContent>
      </Carousel>
    </>
  );
};

export default AgentPostCardsSwiper;
