"use client";
import { Prisma } from "@repo/database";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
type IProfileViewCard = Prisma.ProfileViewGetPayload<{
  select: {
    viewedByUser: {
      select: {
        id: true;
        name: true;
        email: true;
        filePublicUrl: true;
        company: {
          select: {
            companyName: true;
          };
        };
      };
    };
  };
}>;
const ProfileViewCard = ({ item }: { item: IProfileViewCard }) => {
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const router = useRouter();
  return (
    <>
      <div className="flex flex-row items-center justify-between rounded-[10px] border border-secondary-2-100 bg-primary-0 p-5">
        <div className="flex flex-row justify-start gap-6">
          <div className="relative aspect-square size-[60px]">
            <Image
              src={
                item.viewedByUser.filePublicUrl ??
                "/images/placeholder-user-image.jpg"
              }
              alt="agent-image"
              className="rounded-2xl"
              fill
            />
          </div>
          <div className="flex flex-col">
            <div className="font-airbnb_w_xbd text-2xl font-extrabold text-primary-2-850">
              {item.viewedByUser.name}
            </div>
            <div className="font-airbnb_w_bk text-sm font-normal text-text-550">
              {item.viewedByUser.company?.companyName}
            </div>
          </div>
        </div>
        <button
          onClick={() => {
            params.set("viewAgentId", item.viewedByUser.id);
            router.push(`?${params}`);
          }}
          className="rounded-xl border-[1.4px] border-primary-2-750 bg-text-20 px-4 py-3 font-airbnb_w_md text-sm font-medium text-primary-2-750"
        >
          View Profile
        </button>
      </div>
    </>
  );
};

export default ProfileViewCard;
