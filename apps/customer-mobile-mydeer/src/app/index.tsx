import { useAuth, useIsFirstTime } from '@/lib';
import { api } from '@/utils/api';
import { Redirect } from 'expo-router';
import React from 'react';

const index = () => {
  const { status } = useAuth();
  const [isFirstTime, setIsFirstTime] = useIsFirstTime();
  console.log('FIRST TIME', isFirstTime);

  console.log('status', status);

  if (status === 'signIn') {
    // user is signed in
    if (!isFirstTime) {
      // not first time, go straight to main app
      return <Redirect href={'/(app)/home'} />;
    } else {
      setIsFirstTime(false);
      // first time after sign in so go to onboarding
      const { data: user } = api.user.getProfile.useQuery();
      if (user?.onboardingStatus) {
        // Mark that onboarding is complete
      }
      return <Redirect href={'/home-screens/step1'} />;
    }
  } else {
    // For all other cases (signed out or loading), show onboarding first
    return <Redirect href="/auth/onboarding" />;
  }
};

export default index;
