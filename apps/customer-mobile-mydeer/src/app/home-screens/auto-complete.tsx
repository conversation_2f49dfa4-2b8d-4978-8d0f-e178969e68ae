import React from 'react';
import { View, Platform } from 'react-native';
// import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';

const AutoComplete = () => {
  const apiKey =
    Platform.OS === 'android'
      ? process.env.ANDROID_MAP_API_KEY
      : Platform.OS === 'ios'
        ? process.env.IOS_MAP_API_KEY
        : process.env.COMMON_MAP_API_KEY; // for fallback

  return (
    <View style={{ flex: 1 }}>
      {/* <GooglePlacesAutocomplete
        placeholder="Search"
        fetchDetails={true}
        onPress={(data, details = null) => {
          console.log(data, details);
        }}
        query={{
          key: apiKey,
          language: 'en',
        }}
      /> */}
    </View>
  );
};

export default AutoComplete;
