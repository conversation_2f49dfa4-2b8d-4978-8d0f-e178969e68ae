import { Image as NImage } from 'expo-image';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  View,
  Pressable,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  Animated,
} from 'react-native';

import { useModal } from '@/components/ui';
import { Swiper, type SwiperCardRefType } from 'rn-swiper-list';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getItem } from '@/lib/storage';
import { Preference } from '@/types';
import PropertyDetailsModal from './property-details-modal';
import { api } from '@/utils/api';
import { FilterModal } from '@/components/shared/filter-modal';
import { usePreferenceStore } from '@/lib';

const { height, width } = Dimensions.get('screen');

export default function Home() {
  const { userPreference: storedData } = usePreferenceStore();

  const {
    data: filteredData,
    isLoading: isInitialLoading,
    isError,
    refetch,
  } = api.property.getPropertiesAccordingToFilters.useQuery(
    {
      propertyFor: storedData.propertyFor,
      amenities: storedData.amenities,
      baths: storedData.baths,
      beds: storedData.beds,
      facing: storedData.facing,
      furnishType: storedData.furnishType,
      homeTypes: storedData.homeTypes,
      listingTypes: storedData.listingTypes,
      maxArea: storedData.maxArea,
      stayType: storedData.stayType,
      propertyState: storedData.propertyState,
      rentAmenities: storedData.rentAmenities,
      possessionState: storedData.possessionState,
      pointOfInterests: storedData.pointOfInterests,
      minPrice: storedData.minPrice,
      minArea: storedData.minArea,
      maxPrice: storedData.maxPrice,
      searchQuery: storedData.searchQuery,
      propertyCategory: storedData.propertyCategory,
      take: 10, // Reduced take for more frequent pagination
      page: 1,
      areaUnitId: '',
    },
    {
      enabled: !!storedData && !!storedData.propertyFor,
    }
  );

  const [position, setPosition] = useState(-1);
  const insets = useSafeAreaInsets();
  const modal = useModal();
  const [propertyId, setPropertyId] = useState<string>();
  const likePropertyMutation =
    api.user.handleAddRemoveFavouriteProperty.useMutation();
  const utils = api.useUtils();
  const ref = useRef<SwiperCardRefType>();

  const handleOpenBottomSheet = (id: string) => {
    setPropertyId(id);
    modal.present();
  };

  const handleCloseBottomSheet = () => {
    modal.dismiss();
  };

  const OverlayLabelRight = useCallback(() => {
    return (
      <View
        style={[
          styles.overlayLabelContainer,
          {
            backgroundColor: 'green',
          },
        ]}
      />
    );
  }, []);

  const OverlayLabelLeft = useCallback(() => {
    return (
      <View
        style={[
          styles.overlayLabelContainer,
          {
            backgroundColor: 'red',
          },
        ]}
      />
    );
  }, []);

  const isLoading = isInitialLoading;

  return (
    <View style={{ flex: 1 }} className="bg-[#F4EFED]">
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={true}
        contentContainerStyle={{ flexGrow: 1 }}
        className="flex-1"
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
            progressViewOffset={insets.top + 60}
          />
        }
      >
        {/* Header */}
        <View
          style={{
            paddingTop: insets.top,
            position: 'sticky',
            top: 0,
            zIndex: 10,
          }}
          className="bg-[#F4EFED]"
        >
          <View className="mb-3 px-5 flex-row items-center justify-between">
            <NImage
              source={require('@assets/images/mydeer1.png')}
              style={{ width: width * 0.31, height: height * 0.053 }}
              contentFit="contain"
            />
            <View className="flex-row items-center gap-2.5">
              <FilterModal />
            </View>
          </View>
        </View>

        {/* Content */}
        {isLoading ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size={40} color="#000" />
          </View>
        ) : isError ? (
          <View className="flex-1 items-center justify-center">
            <Text>Error loading Properties</Text>
            <Pressable
              className="mt-4 px-4 py-2 bg-primary-700 rounded-md"
              onPress={() => refetch()}
            >
              <Text className="text-white">Try Again</Text>
            </Pressable>
          </View>
        ) : !filteredData?.properties.length ? (
          <View className="flex-1 items-center justify-center">
            <Text className="text-xl font-semi-bold">No properties found</Text>
            <Text className="mt-2 text-sm text-gray-500">
              Pull down to refresh
            </Text>
          </View>
        ) : (
          <View className="flex-1 items-center px-5">
            <Swiper
              ref={ref}
              cardStyle={styles.cardStyle}
              data={filteredData?.properties!}
              renderCard={(property) => {
                const propertyImages =
                  property.mediaSections?.flatMap((media) => media.media) || [];

                return (
                  <Pressable
                    style={styles.renderCardContainer}
                    onPress={() => {
                      if (property?.id) {
                        handleOpenBottomSheet(property.id);
                      }
                    }}
                  >
                    <View style={{ flex: 1, position: 'relative' }}>
                      {/* Property Image */}
                      {propertyImages.length > 0 && (
                        <NImage
                          id={propertyImages[0].id?.toString()}
                          source={{
                            uri:
                              propertyImages[0].cloudinaryUrl ??
                              propertyImages[0].filePublicUrl ??
                              require('@assets/icons/companylogo.png'),
                          }}
                          placeholderContentFit="fill"
                          placeholder={require('@assets/images/Card.png')}
                          style={styles.renderCardImage}
                          contentFit="cover"
                        />
                      )}

                      {/* Card Content */}
                      <View className="absolute bottom-8 left-0 right-0 mx-4">
                        <View className="bg-white rounded-2xl p-4 shadow-md">
                          <View className="w-full flex-row items-center justify-between">
                            <View className="flex-1">
                              <Text
                                className="font-airbnb_bd text-base font-bold text-text-main700"
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                {property?.propertyTitle ?? 'No Title'}
                              </Text>
                              <View className="mt-0.5 flex-row items-center">
                                <NImage
                                  source={require('@assets/icons/location2.png')}
                                  className="w-2.5 h-2.5"
                                />
                                <Text
                                  className="ml-1 font-airbnb_bk text-[10px] font-normal text-text-600"
                                  numberOfLines={1}
                                  ellipsizeMode="tail"
                                >
                                  {property?.propertyLocation ??
                                    'Location Not Available'}
                                </Text>
                              </View>
                            </View>

                            <View className="items-end">
                              <Text
                                className="font-airbnb_xbd text-lg font-extrabold text-primary-700 text-right"
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                {property?.propertyPrice
                                  ? `₹${Number(property.propertyPrice).toLocaleString()}`
                                  : 'Price N/A'}
                              </Text>
                              <Text
                                className="font-airbnb_xbd text-sm font-extrabold text-primary-700 text-right"
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                {property?.propertyPrice &&
                                property?.areaInSqMeters
                                  ? `₹${Math.round(Number(property.propertyPrice) / Number(property.areaInSqMeters)).toLocaleString()}/sq.mt`
                                  : ''}
                              </Text>
                            </View>
                          </View>

                          <Text
                            className="mt-3 font-airbnb_bk text-[10px] font-normal text-text-600"
                            numberOfLines={5}
                            ellipsizeMode="tail"
                          >
                            {property?.aboutProperty ??
                              'No description available'}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </Pressable>
                );
              }}
              onSwipeRight={(index) => {
                const currentProperty = filteredData.properties[index];
                if (currentProperty?.id) {
                  likePropertyMutation.mutate({
                    propertyId: currentProperty.id,
                  });
                  utils.user.invalidate();
                }
              }}
              OverlayLabelRight={OverlayLabelRight}
              OverlayLabelLeft={OverlayLabelLeft}
            />
          </View>
        )}
      </ScrollView>

      {/* Modal */}
      <View className="pb-[100px]">
        <PropertyDetailsModal
          position={position}
          refModal={modal.ref}
          updatePosition={setPosition}
          ref={modal.ref}
          onClose={handleCloseBottomSheet}
          id={propertyId ?? ''}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  shadow: {
    shadowColor: '#0F5141',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
  },
  filtershadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  button: {
    elevation: 4,
    shadowColor: 'black',
    shadowOpacity: 0.1,
    shadowOffset: {
      width: 0,
      height: 4,
    },
  },
  cardStyle: {
    flex: 1,
    width: '100%',
    height: '95%',
    borderRadius: 15,
    marginVertical: 20,
  },
  renderCardContainer: {
    flex: 1,
    borderRadius: 15,
    height: '100%',
    width: '100%',
  },
  renderCardImage: {
    height: '100%',
    width: '100%',
    borderRadius: 15,
  },
  overlayLabelContainer: {
    width: '100%',
    height: '100%',
    borderRadius: 15,
  },
});
