import {
  View,
  Text,
  Pressable,
  Alert,
  RefreshControl,
  ActivityIndicator,
  ScrollView,
  Dimensions,
} from 'react-native';
import React, { useState, useRef } from 'react';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { api } from '@/utils/api';
import { Video, ResizeMode } from 'expo-av';
import { CustomModal } from '@/components/shared/custom-modal';
import { CustomerReviewData } from '@/types';
import { showMessage } from 'react-native-flash-message';
import { router } from 'expo-router';
const { height, width } = Dimensions.get('screen');

interface ReviewsProps {
  customerReviews: CustomerReviewData[] | undefined;
}
interface UploadState {
  isUploading: boolean;
  reviewId: string | null;
  fileKey: string | null;
  fileUrl: string | null;
}

const Reviews: React.FC<ReviewsProps> = ({ customerReviews }) => {
  const [videoUri, setVideoUri] = useState<string | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [isVideoModalVisible, setIsVideoModalVisible] = useState(false);
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null);
  const [isOptionsMenuVisible, setIsOptionsMenuVisible] = useState(false);
  const videoRef = useRef(null);
  const uploadVideoReview =
    api.customerReviews.createCustomerReviews.useMutation();
  const awsPresignedUrlMutation = api.aws.getPresignedUrl.useMutation();
  const awsGetPublicUrlMutation = api.aws.getPublicFileUrl.useMutation();
  const { refetch } = api.customerReviews.getCustomerReviews.useQuery();
  const { data: userData } = api.user.getProfile.useQuery();

  // Consolidated upload state
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    reviewId: null,
    fileKey: null,
    fileUrl: null,
  });

  const playReviewVideo = (videoUrl: string) => {
    setSelectedVideo(videoUrl);
    setIsVideoModalVisible(true);
  };

  const pickVideo = async (reviewId?: string) => {
    try {
      setUploadState((prev) => ({
        ...prev,
        reviewId: reviewId || null,
        isUploading: true,
      }));

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['videos'],
        allowsEditing: true,
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const videoUri = result.assets[0].uri;
        const fileName = videoUri.split('/').pop() || 'video.mp4';

        await uploadMedia(videoUri, fileName, reviewId);
      } else {
        setUploadState((prev) => ({
          ...prev,
          isUploading: false,
        }));
      }
    } catch (error) {
      console.error('Error picking video:', error);
      Alert.alert('Error picking video');
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
      }));
    }
  };

  const uploadMedia = async (
    fileUri: string,
    fileName: string,
    reviewId?: string
  ) => {
    if (!fileUri || !userData?.id) {
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
      }));
      return;
    }

    try {
      const key =
        `/agent_review_/${userData.id}/` + Date.now() + '-' + fileName;

      const { url, key: presignedUrlKey } =
        await awsPresignedUrlMutation.mutateAsync({
          fileName: key,
          contentType: 'video/mp4',
          publicAvailable: true,
        });

      if (!url) {
        setUploadState((prev) => ({
          ...prev,
          isUploading: false,
        }));
        Alert.alert('Error uploading media');
        return;
      }

      const fileInfo = await FileSystem.getInfoAsync(fileUri);
      if (!fileInfo.exists) {
        setUploadState((prev) => ({
          ...prev,
          isUploading: false,
        }));
        Alert.alert('File does not exist');
        return;
      }

      const uploadResult = await FileSystem.uploadAsync(url, fileUri, {
        httpMethod: 'PUT',
        uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,
        headers: {
          'Content-Type': 'video/mp4',
        },
      });

      if (uploadResult.status !== 200) {
        setUploadState((prev) => ({
          ...prev,
          isUploading: false,
        }));
        Alert.alert('Error uploading video');
        return;
      }

      const filePublicUrl = await awsGetPublicUrlMutation.mutateAsync({
        fileKey: presignedUrlKey,
      });

      setUploadState((prev) => ({
        ...prev,
        fileKey: presignedUrlKey,
        fileUrl: filePublicUrl,
        isUploading: false,
      }));

      setVideoUri(filePublicUrl);

      if (reviewId) {
        try {
          const result = await uploadVideoReview.mutateAsync({
            id: reviewId,
            fileKey: presignedUrlKey,
            fileUrl: filePublicUrl,
          });

          Alert.alert('Video uploaded successfully');

          await refetch();

          setUploadState({
            isUploading: false,
            reviewId: null,
            fileKey: null,
            fileUrl: null,
          });
          setVideoUri(null);
        } catch (mutationError) {
          console.error('Error updating review directly:', mutationError);
          Alert.alert('Failed to update review. Please try again.');
        }
      }
    } catch (e) {
      Alert.alert('Error uploading video');
    } finally {
      if (!uploadState.reviewId) {
        setUploadState((prev) => ({
          ...prev,
          isUploading: false,
        }));
      }
    }
  };

  const handleUploadVideoToReview = async (
    reviewId: string,
    key: string,
    url: string
  ) => {
    if (!reviewId || !key || !url) {
      Alert.alert('Missing required information for video upload');
      return;
    }

    setUploadState((prev) => ({
      ...prev,
      isUploading: true,
    }));

    try {
      const result = await uploadVideoReview.mutateAsync({
        id: reviewId,
        fileKey: key,
        fileUrl: url,
      });

      Alert.alert('Video uploaded successfully.');

      await refetch();

      setUploadState({
        isUploading: false,
        reviewId: null,
        fileKey: null,
        fileUrl: null,
      });
      setVideoUri(null);
    } catch (error) {
      console.error('Error in handleUploadVideoToReview:', error);
      Alert.alert('Failed to upload video review');
    } finally {
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
      }));
    }
  };

  const handleUploadVideo = async () => {
    const { reviewId, fileKey, fileUrl } = uploadState;

    if (!fileUrl || !fileKey) {
      Alert.alert('You need to add the video first to upload it.');
      return;
    }

    if (!reviewId) {
      Alert.alert('Please select a review to update');
      return;
    }

    await handleUploadVideoToReview(reviewId, fileKey, fileUrl);
  };

  const isUploading = (reviewId?: string) => {
    if (!reviewId) {
      return uploadState.isUploading && !uploadState.reviewId;
    }
    return uploadState.isUploading && uploadState.reviewId === reviewId;
  };

  const goToUserProfile = (agentId: string) => {
    // closeMenuBottomSheet();
    router.push(`/agent-routes/${agentId}`);
    setIsOptionsMenuVisible(false);
  };

  const { mutate: deleteReviewMutation } =
    api.customerReviews.deleteCustomerReviewToAgent.useMutation();

  const handleDeleteReview = async (reviewId: string) => {
    try {
      deleteReviewMutation(
        { reviewId },
        {
          onSuccess: () => {
            showMessage({
              message: 'Review deleted successfully',
              type: 'success',
            });
            refetch();
          },
          onError: () => {
            showMessage({
              message: 'Failed to delete review',
              type: 'danger',
            });
          },
        }
      );
    } finally {
      setIsOptionsMenuVisible(false);
      setSelectedReviewId(null);
    }
  };

  const handleOptionsPress = (reviewId: string) => {
    setSelectedReviewId(reviewId);
    setIsOptionsMenuVisible(true);
  };
  console.log('review', customerReviews);

  return (
    <View>
      {customerReviews && customerReviews.length > 0 ? (
        customerReviews.map((review) => (
          <View
            key={review.id}
            className="my-2.5 p-4 border rounded-2xl border-secondary-100"
          >
            <View className="mb-3 px-3 py-1.5 flex-row items-center bg-[#FFFBEF] rounded-md self-start max-w-[35%] gap-1">
              <Text
                className="text-sm font-medium font-airbnb_md text-primary-750"
                numberOfLines={1}
              >
                Rated: {review.userStarsCount}/5
              </Text>
              <Image
                className="mb-0.5 h-3.5 w-3.5"
                source={require('@assets/icons/yostar.png')}
              />
            </View>

            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center flex-1 mr-2">
                <Image
                  source={
                    review.ratedTo.filePublicUrl
                      ? { uri: review.ratedTo.filePublicUrl }
                      : require('@assets/icons/user-defaultimg.jpeg')
                  }
                  className="mr-2 h-16 w-16 rounded-xl"
                />
                <View className="flex-1">
                  <Text
                    className="mb-1.5 text-xl font-extrabold text-primary-750"
                    numberOfLines={1}
                  >
                    {review.ratedTo.name || 'Unknown Agent'}
                  </Text>
                  <Text numberOfLines={1} className="text-sm text-text-600">
                    {review.ratedTo.company?.companyName || 'Company N/A'}
                  </Text>
                  <Text numberOfLines={1} className="text-sm text-text-600">
                    {review.ratedTo.userLocation || 'Location N/A'}
                  </Text>
                </View>
              </View>

              <Pressable
                className="self-end w-8 h-8 items-center justify-center"
                onPress={() => handleOptionsPress(review.id)}
              >
                <Image
                  source={require('@assets/icons/horizontaldot2.png')}
                  className="h-6 w-6"
                  contentFit="contain"
                />
              </Pressable>
            </View>

            <Text className="my-4 text-sm font-normal font-airbnb_bk text-text-500">
              {review.userRatingMessage || 'No review message provided.'}
            </Text>

            <View className="p-3 bg-[#FFFAF9]">
              <View>
                <Text className="text-sm font-medium font-airbnb_md text-text-main700 mb-2">
                  {isUploading(review.id)
                    ? 'Uploading Video...'
                    : review.cloudinaryUrl || review.filePublicUrl
                      ? 'Video Uploaded'
                      : 'Upload Video'}
                </Text>

                <View className="mt-1.5 px-4 py-3 border rounded-xl border-text-100 bg-white">
                  <Text className="text-sm font-normal font-airbnb_bk text-text-600">
                    {isUploading(review.id)
                      ? 'Uploading video...'
                      : review.cloudinaryUrl
                        ? `${review.cloudinaryUrl?.split('/').pop()?.split('.')[0] || 'video'}.${review.cloudinaryUrl?.split('.').pop() || review.filePublicUrl?.split('.').pop()}`
                        : review.filePublicUrl
                          ? `${review.filePublicUrl.split('/').pop()?.split('.')[0] || 'video'}.${review.filePublicUrl.split('.').pop()}`
                          : 'Add your feedback video'}
                  </Text>
                </View>

                <Pressable
                  className={`mt-4 px-4 py-3 border rounded-xl items-center w-full ${
                    isUploading(review.id)
                      ? 'bg-gray-200 border-gray-300'
                      : 'bg-white border-secondary-main700'
                  }`}
                  onPress={
                    isUploading(review.id)
                      ? undefined
                      : review.cloudinaryUrl
                        ? () => playReviewVideo(review.cloudinaryUrl!)
                        : review.filePublicUrl
                          ? () => playReviewVideo(review.filePublicUrl!)
                          : () => pickVideo(review.id)
                  }
                  disabled={uploadState.isUploading}
                >
                  <Text
                    className={`text-sm font-medium font-airbnb_md ${
                      isUploading(review.id)
                        ? 'text-gray-500'
                        : 'text-secondary-main700'
                    }`}
                  >
                    {isUploading(review.id)
                      ? 'Uploading...'
                      : review.cloudinaryUrl
                        ? 'Watch Uploaded Video'
                        : review.filePublicUrl
                          ? 'Watch Uploaded Video'
                          : 'Upload'}
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        ))
      ) : (
        <View
          className="justify-center items-center"
          style={{ height: height * 0.8, width: width * 0.9 }}
        >
          <Text className="text-xl font-semi-bold text-center text-text-600">
            Your reviews will appear here
          </Text>
        </View>
      )}

      {/* options menu mpdal */}
      <CustomModal
        visible={isOptionsMenuVisible}
        onClose={() => {
          setIsOptionsMenuVisible(false);
          setSelectedReviewId(null);
        }}
        Content={
          <View className="w-full bg-white rounded-xl">
            <Pressable
              onPress={() => {
                setIsOptionsMenuVisible(false);
                setSelectedReviewId(null);
              }}
              className="mb-2 self-end bg-black/60 px-2 rounded-full"
            >
              <Text className="font-airbnb_bd text-lg text-white">✕</Text>
            </Pressable>

            <View className="items-center border border-secondary-100 rounded-md p-2">
              <Pressable
                onPress={() => {
                  if (selectedReviewId) {
                    const review = customerReviews?.find(
                      (r) => r.id === selectedReviewId
                    );
                    if (review?.ratedTo?.id) {
                      goToUserProfile(review.ratedTo.id);
                    }
                  }
                  setIsOptionsMenuVisible(false);
                  setSelectedReviewId(null);
                }}
                className="w-full items-center py-3 border-b border-gray-200"
              >
                <Text className="text-base font-airbnb_md text-primary-750">
                  Go to Profile
                </Text>
              </Pressable>

              <Pressable
                onPress={() => {
                  if (selectedReviewId) {
                    handleDeleteReview(selectedReviewId);
                  }
                }}
                className="w-full items-center py-3"
              >
                <Text className="text-base font-airbnb_md text-red-500">
                  Delete Review
                </Text>
              </Pressable>
            </View>
          </View>
        }
      />

      {/* video modal */}
      <CustomModal
        visible={isVideoModalVisible}
        onClose={() => setIsVideoModalVisible(false)}
        Content={
          <View className="w-full">
            <Pressable
              onPress={() => setIsVideoModalVisible(false)}
              className="mb-2 self-end bg-black/60 px-2 rounded-full"
            >
              <Text className="font-airbnb_bd text-lg text-white">✕</Text>
            </Pressable>
            {selectedVideo && (
              <Video
                ref={videoRef}
                source={{ uri: selectedVideo }}
                useNativeControls
                resizeMode={ResizeMode.COVER}
                isLooping={false}
                style={{ width: '100%', height: 250 }}
                shouldPlay={true}
              />
            )}
          </View>
        }
      />
    </View>
  );
};

const MyReviews = () => {
  const [refreshing, setRefreshing] = useState(false);

  const {
    data: customerReviews,
    isError: isErrorCustomerReview,
    isLoading: isLoadingCustomerReview,
    refetch,
  } = api.customerReviews.getCustomerReviews.useQuery(undefined, {
    staleTime: 0,
    refetchOnMount: 'always',
  });

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      const result = await refetch();
    } catch (error) {
      console.error('Error refreshing reviews:', error);
    } finally {
      setRefreshing(false);
    }
  };

  React.useEffect(() => {
    void refetch();
  }, []);

  if (isLoadingCustomerReview && !refreshing) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size={40} color="#555" />
      </View>
    );
  }

  if (isErrorCustomerReview) {
    return (
      <View className="items-center justify-center py-12">
        <Text className="text-red-500 font-airbnb_md">
          Error loading reviews
        </Text>
        <Pressable
          className="mt-4 bg-secondary-main700 px-6 py-2 rounded-lg"
          onPress={onRefresh}
        >
          <Text className="text-white font-airbnb_md">Try Again</Text>
        </Pressable>
      </View>
    );
  }

  return (
    <ScrollView
      className="px-5"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={['#555']}
          tintColor="#555"
        />
      }
    >
      <View className="my-4">
        <Reviews customerReviews={customerReviews || []} />
      </View>
    </ScrollView>
  );
};

export default MyReviews;
