import Entypo from '@expo/vector-icons/Entypo';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { Image } from 'expo-image';
import { useNavigation, useLocalSearchParams, Stack } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Pressable,
  ScrollView,
  Text,
  TextInput,
  View,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { LeftMsg, RightMsg } from '@/components/routes/chat-screen-msgs';
import UserPic from '@/components/shared/user-pic';
import { formatTime } from '@/utils/format-terms';
import { useRouter } from 'expo-router';
import { Button, Modal } from '@/components/ui';
import { CustomModal } from '@/components/shared/custom-modal';
import { StarRating } from '../profile-screens/feedback';
import Feather from '@expo/vector-icons/Feather';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { Video, ResizeMode } from 'expo-av';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { signedVideoUploadToCloudinary } from '@/api/cloudinary/upload';

export default function ChatDetailScreen() {
  const { id, name, image, agentId, rated } = useLocalSearchParams<{
    id: string;
    name: string;
    image: string;
    agentId: string;
    rated: boolean;
  }>();
  console.log('rated', rated);

  const utils = api.useUtils();
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView | null>(null);
  const [newMessage, setNewMessage] = useState<string>('');
  const trpcUtils = api.useUtils();
  const { data: profile } = api.user.getProfile.useQuery();

  const { data: chat, isLoading } = api.chat.getMessages.useQuery(
    { connectionId: id },
    {
      enabled: !!id,
      refetchInterval: 10000,
    }
  );
  const { mutate: sendMessage } = api.chat.sendMessage.useMutation({
    onSuccess: () => {
      setNewMessage('');
      void trpcUtils.invalidate();
    },
    onError: (error) => {
      console.error(error);
      showMessage({
        message: 'Error sending message',
        type: 'danger',
      });
    },
  });

  // upload video part for rating
  const [reviewVideoUri, setReviewVideoUri] = useState<string | null>(null);
  const [uploadingReviewVideo, setUploadingReviewVideo] = useState(false);

  // Create a stable timestamp for the signature query
  const videoTimestampRef = useRef(Math.round(Date.now() / 1000));

  // State for cloudinary video data
  const [cloudinaryVideo, setCloudinaryVideo] = useState<{
    url: string;
    publicId: string;
  }>({
    url: '',
    publicId: '',
  });

  // Fetch signature for Cloudinary video upload
  const { data: videoSignatureData } =
    api.cloudinary.generateSignature.useQuery({
      paramsToSign: {
        timestamp: videoTimestampRef.current,
        folderFor: 'customers',
        forlderPurpose: 'ratings',
      },
    });

  const pickReviewVideo = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'video/*',
        multiple: false,
      });

      if (!result.canceled && result.assets.length > 0) {
        const selectedUri = result.assets[0].uri;
        console.log(
          '[DEBUG] Video selected:',
          selectedUri.substring(0, 50) + '...'
        );

        //file size check
        const fileInfo = await FileSystem.getInfoAsync(selectedUri);

        if (fileInfo.exists) {
          const fileSizeInMB = fileInfo.size / 1024 / 1024;
          console.log(
            '[DEBUG] Video file size:',
            fileSizeInMB.toFixed(2) + 'MB'
          );

          if (fileSizeInMB > 200) {
            showMessage({
              message:
                'File too large. Please select a video smaller than 200MB',
              type: 'warning',
            });
            return;
          }
        } else {
          showMessage({
            message: 'Could not determine file size. Please try another video.',
            type: 'warning',
          });
          return;
        }

        setReviewVideoUri(selectedUri);
      }
    } catch (error) {
      console.error('[ERROR] Video picker error:', error);
      showMessage({
        message: 'An error occurred while picking the video',
        type: 'danger',
      });
    }
  };

  const menuBottomSheetRef = useRef<BottomSheetModal>(null);
  const [isMenuVisible, setIsMenuVisible] = useState(false);

  const insets = useSafeAreaInsets();
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);

  const openMenuBottomSheet = () => {
    menuBottomSheetRef.current?.present();
    setIsMenuVisible(true);
  };
  const closeMenuBottomSheet = () => {
    menuBottomSheetRef.current?.dismiss();
    setIsMenuVisible(false);
  };

  //Report
  const [isReportModalVisible, setIsReportModalVisible] = useState(false);
  const [reasonForReporting, setReasonForReporting] = useState('');
  const openReportModal = () => {
    setIsReportModalVisible(true);
    closeMenuBottomSheet();
  };
  const closeReportModal = () => {
    setIsReportModalVisible(false);
    setReasonForReporting('');
  };
  const { mutate: reportAgent } = api.chat.reportAgentFromChat.useMutation({
    onSuccess: (opts) => {
      showMessage({
        message: opts.message,
        type: 'success',
      });
      closeReportModal();
    },
    onError: (error) => {
      showMessage({
        message: error.message,
        type: 'danger',
      });
    },
  });
  const submitReport = () => {
    if (reasonForReporting.trim()) {
      reportAgent({
        reportedUserId: agentId,
        reasonForReporting: reasonForReporting,
      });
    } else {
      showMessage({
        message: 'Please provide a reason for reporting',
        type: 'warning',
      });
    }
  };

  //Remove Agent
  const [isRemoveAgentModalVisible, setIsRemoveAgentModalVisible] =
    useState(false);
  const openRemoveAgentModal = () => {
    setIsRemoveAgentModalVisible(true);
    closeMenuBottomSheet();
  };
  const closeRemoveAgentModal = () => {
    setIsRemoveAgentModalVisible(false);
  };
  const { mutate: deleteChat } = api.chat.deleteChat.useMutation({
    onSuccess: (opts) => {
      showMessage({
        message: opts.message,
        type: 'success',
      });
      closeRemoveAgentModal();
      utils.invalidate();
      router.back();
    },
    onError: (error) => {
      showMessage({
        message: error.message,
        type: 'danger',
      });
    },
  });
  const removeAgent = () => {
    deleteChat({ connectionId: id });
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !id || !profile?.id) {
      return showMessage({
        message: 'Please enter something.',
        type: 'danger',
      });
    }

    sendMessage({
      message: newMessage.trim(),
      connectionId: id,
    });
  };

  const goToUserProfile = () => {
    closeMenuBottomSheet();
    router.push(`/agent-routes/${agentId}`);
  };

  //Rating Agent
  const [isRatingAgentModalVisible, setIsRatingAgentModalVisible] =
    useState(false);
  const [isVideoPreviewVisible, setIsVideoPreviewVisible] = useState(false);
  const openRatingAgentModal = () => {
    setIsRatingAgentModalVisible(true);
    closeMenuBottomSheet();
  };
  const closeRatingAgentModal = () => {
    setIsRatingAgentModalVisible(false);
  };
  const RatingAgent = api.customerReviews.customerRateToAgentNew.useMutation({
    onSuccess: (opts) => {
      showMessage({
        message: opts.message,
        type: 'success',
      });
      setReviewVideoUri(null);
      closeRatingAgentModal();
    },
    onError: (error) => {
      showMessage({
        message: error.message,
        type: 'danger',
      });
    },
  });
  const [ratingValue, setRatingValue] = useState(0);
  const [ratingMessage, setRatingMessage] = useState('');
  const handleRatingSelected = (rating: number) => {
    setRatingValue(rating);
  };

  const handleRatingAgent = async () => {
    if (ratingValue === 0 && !ratingMessage) {
      showMessage({
        message: 'Please do rate and provide a message',
        type: 'warning',
      });
      return;
    }

    try {
      // If there's a video to upload, handle it first with Cloudinary
      let cloudinaryVideoUrl = null;
      let cloudinaryPublicId = null;

      if (reviewVideoUri) {
        setUploadingReviewVideo(true);

        console.log('[DEBUG] Preparing to upload review video to Cloudinary');

        // Check if signature data is available
        if (!videoSignatureData || !videoSignatureData.signature) {
          showMessage({
            message: 'Unable to prepare video upload. Please try again.',
            type: 'danger',
          });
          setUploadingReviewVideo(false);
          return;
        }

        try {
          console.log('[DEBUG] Starting Cloudinary video upload');

          // Upload the video to Cloudinary
          const result = await signedVideoUploadToCloudinary({
            videoUri: reviewVideoUri,
            folder: videoSignatureData.uploadFolderUrl,
            signature: videoSignatureData.signature,
            timestamp: videoTimestampRef.current.toString(),
            preset: videoSignatureData.cloudPreset,
            apiKey: videoSignatureData.apiKey,
            cloudName: videoSignatureData.cloudName,
          });

          console.log(
            '[DEBUG] Cloudinary video upload result:',
            result.secure_url.substring(0, 50) + '...'
          );

          // Save the Cloudinary data
          cloudinaryVideoUrl = result.secure_url;
          cloudinaryPublicId = result.public_id;

          // Update state
          setCloudinaryVideo({
            url: result.secure_url,
            publicId: result.public_id,
          });
        } catch (uploadError) {
          console.error('[ERROR] Cloudinary video upload failed:', uploadError);
          showMessage({
            message:
              'Failed to upload video. Rating will be submitted without video.',
            type: 'warning',
          });
        }
      }

      // Now submit the rating with the Cloudinary video data
      console.log('[DEBUG] Submitting rating with Cloudinary data:', {
        videoUrl: cloudinaryVideoUrl
          ? cloudinaryVideoUrl.substring(0, 30) + '...'
          : 'none',
        publicId: cloudinaryPublicId || 'none',
      });
      if (rated) {
        return;
      }
      const ratingResult = await RatingAgent.mutateAsync({
        userStarsCount: ratingValue || 0,
        userRatingMessage: ratingMessage,
        ratedToUserId: agentId,
        connectionId: id,
        cloudinaryUrl: cloudinaryVideoUrl || undefined,
        cloudinaryPublicId: cloudinaryPublicId || undefined,
      });
      console.log('>>>>>>>ratingResult>>>>>>\n', ratingResult);

      showMessage({
        message: ratingResult.message || 'Rating submitted successfully',
        type: 'success',
      });

      setReviewVideoUri(null);
      setCloudinaryVideo({ url: '', publicId: '' });
      closeRatingAgentModal();
    } catch (error) {
      console.error('[ERROR] Failed to submit rating:', error);
      showMessage({
        message:
          error instanceof Error ? error.message : 'Failed to submit rating',
        type: 'danger',
      });
    } finally {
      setUploadingReviewVideo(false);
    }
  };

  const [agentIsActive, setAgentIsActive] = useState(false);
  const { mutate: toggleStatus } = api.chat.toggleStatus.useMutation();
  const handleGoBack = () => {
    setAgentIsActive(false);
    router.back();
  };
  useEffect(() => {
    setAgentIsActive(true);
    toggleStatus({ status: agentIsActive });
  }, [agentIsActive]);

  // Custom header component
  const Header = () => (
    <View
      className="bg-[#FFFAF9] px-5 "
      style={{
        paddingTop: insets.top,
        paddingBottom: 10,
      }}
    >
      <View className="flex-row items-center justify-between h-fit">
        <View className="flex-row items-center">
          <Pressable onPress={handleGoBack}>
            <Entypo
              name="chevron-thin-left"
              size={18}
              color="#1E1E1E"
              className="pr-3"
            />
          </Pressable>
          <View className="relative">
            <UserPic
              picUrl={image}
              size={34}
              color="#784100"
              className="aspect-square h-10 w-10 rounded-full"
            />
            <Image
              source={require('@assets/icons/indicator.png')}
              className="h-3 w-3 absolute bottom-0 right-0"
              tintColor={agentIsActive ? '#00FF00' : ''}
            />
          </View>

          <View className="px-3 flex-row items-center">
            <Text className="font-airbnb_bd text-base font-bold text-primary-700">
              {name}
            </Text>
            <View className="ml-2 rounded-lg bg-primary-0 px-2 py-1.5">
              <Text className="font-airbnb_md text-[8px] font-medium text-secondary-main700">
                Agent
              </Text>
            </View>
          </View>
        </View>

        <Pressable onPress={openMenuBottomSheet}>
          <Image
            source={require('../../../assets/icons/dot2.png')}
            className="h-10 w-10"
          />
        </Pressable>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1 }} className="bg-[#FFFAF9]">
      <Stack.Screen options={{ header: Header, headerShown: true }} />
      <KeyboardAvoidingView
        behavior={'padding'}
        className="flex-1"
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 70 : 20}
      >
        {isMenuVisible && (
          <View className="absolute z-10 h-full w-full bg-black opacity-50" />
        )}

        {/* Messages */}
        <ScrollView
          className="flex-1 px-5"
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          ref={scrollViewRef}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd()}
        >
          {isLoading ? (
            <View className="flex-1 justify-center items-center">
              <ActivityIndicator size={40} color="#f04d24" />
            </View>
          ) : (
            <>
              {chat?.messages.map((msg) =>
                msg.senderId === profile?.id ? (
                  <RightMsg
                    message={msg.content}
                    time={formatTime(msg.createdAt)}
                    key={msg.id}
                  />
                ) : (
                  <LeftMsg
                    message={msg.content}
                    time={formatTime(msg.createdAt)}
                    key={msg.id}
                  />
                )
              )}
            </>
          )}
          {chat?.messages.length === 0 && (
            <View className="items-center justify-center py-10">
              <Text className="font-airbnb_md text-base text-gray-400">
                Start a conversation with {name}
              </Text>
            </View>
          )}
        </ScrollView>

        {/* Input Area */}
        <View
          className="border-t border-[#FFFBF9] bg-white"
          style={{
            paddingBottom: insets.bottom,
          }}
        >
          <View className="py-2 px-5">
            <View className="flex-row items-center justify-between rounded-2xl border border-primary-0 bg-white px-4">
              <TextInput
                ref={inputRef}
                className="h-12 w-[90%] font-airbnb_bk text-sm font-normal text-primary-2-800"
                placeholder="Type your message"
                placeholderTextColor="#525252"
                value={newMessage}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                onChangeText={setNewMessage}
              />
              <Pressable onPress={handleSendMessage}>
                <Image
                  source={require('../../../assets/icons/send.png')}
                  className="w-5 h-5"
                  contentFit="contain"
                  tintColor="#333333"
                />
              </Pressable>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>

      {/* Bottom Sheet Modal */}
      <Modal
        ref={menuBottomSheetRef}
        index={0}
        snapPoints={['50%']}
        onDismiss={closeMenuBottomSheet}
        backgroundStyle={{ backgroundColor: 'white' }}
        handleIndicatorStyle={{ backgroundColor: 'gray' }}
        enablePanDownToClose={true}
      >
        <View className="flex-1 px-5">
          <Pressable
            onPress={goToUserProfile}
            className="flex-row items-center justify-between py-5"
          >
            <View className="flex-row items-center">
              <Image
                source={require('../../../assets/icons/profile.png')}
                contentFit="contain"
                className="mr-5 h-6 w-6"
                tintColor={'#1E1E1E'}
              />
              <Text className="font-airbnb_md text-base font-medium text-text-600">
                View Profile
              </Text>
            </View>
            <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
          </Pressable>

          <Pressable
            className="flex-row items-center justify-between py-5"
            onPress={openRatingAgentModal}
          >
            <View className="flex-row items-center">
              <Image
                source={require('../../../assets/icons/profile/feedback.png')}
                contentFit="contain"
                className="mr-5 h-6 w-6"
                tintColor={'#1E1E1E'}
              />
              <Text className="font-airbnb_md text-base font-medium text-text-600">
                Rate Agent
              </Text>
            </View>
            <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
          </Pressable>

          <Pressable
            className="flex-row items-center justify-between py-5"
            onPress={() => [
              closeMenuBottomSheet(),
              router.push('/profile-screens/help-center'),
            ]}
          >
            <View className="flex-row items-center">
              <Image
                source={require('../../../assets/icons/helpcenter.png')}
                contentFit="contain"
                className="mr-5 h-6 w-6"
                tintColor={'#1E1E1E'}
              />
              <Text className="font-airbnb_md text-base font-medium text-text-600">
                Help Center
              </Text>
            </View>
            <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
          </Pressable>

          <Pressable
            className="flex-row items-center justify-between py-5"
            onPress={openReportModal}
          >
            <View className="flex-row items-center">
              <Image
                source={require('../../../assets/icons/report.png')}
                contentFit="contain"
                className="mr-5 h-6 w-6"
                tintColor={'#1E1E1E'}
              />
              <Text className="font-airbnb_md text-base font-medium text-text-600">
                Report Agent
              </Text>
            </View>
            <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
          </Pressable>

          <Pressable
            onPress={openRemoveAgentModal}
            className="flex-row items-center justify-between py-5"
          >
            <View className="flex-row items-center">
              <Image
                source={require('../../../assets/icons/bin.png')}
                contentFit="contain"
                className="mr-5 h-6 w-6"
                tintColor={'#1E1E1E'}
              />
              <Text className="font-airbnb_md text-base font-medium text-text-600">
                Remove Agent
              </Text>
            </View>
            <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
          </Pressable>
        </View>
      </Modal>

      {/* Report Modal */}
      <CustomModal
        visible={isReportModalVisible}
        onClose={closeReportModal}
        Content={
          <>
            <Text className="font-airbnb_bd text-lg text-secondary-main700 mb-2">
              Report Agent
            </Text>
            <Text className="font-airbnb_md text-base font-normal text-text-500 mb-4">
              Please provide a brief explanation for why you are reporting this
              agent.
            </Text>
            <TextInput
              placeholder="Type your message"
              className="h-[100px] border border-[#ECE9E8] bg-[#FFFAF9] rounded-xl p-4 mb-4 font-airbnb_bk text-primary-main700"
              multiline={true}
              textAlignVertical="top"
              placeholderTextColor="#9E9E9E"
              onChangeText={(text) => {
                setReasonForReporting(text);
              }}
            />
            <View className="flex-row justify-end gap-3 mt-2">
              <Button
                label="Cancel"
                variant="outline"
                onPress={closeReportModal}
              />
              <Button
                label="Report"
                variant="default"
                className="w-1/2"
                onPress={submitReport}
              />
            </View>
          </>
        }
      />

      {/* Remove Agent Modal */}
      <CustomModal
        visible={isRemoveAgentModalVisible}
        onClose={closeRemoveAgentModal}
        Content={
          <>
            <Text className="font-airbnb_bd text-lg text-secondary-main700 mb-2">
              Remove Agent
            </Text>
            <Text className="font-airbnb_md text-base font-normal text-text-500 mb-1">
              Are you sure you want to remove this agent?
            </Text>
            <Text className="font-airbnb_md text-base font-normal text-text-600 mb-6 italic">
              Please note that submitting this request will also delete your
              chat history with this agent.
            </Text>
            <View className="flex-row justify-end gap-3 mt-2">
              <Button
                label="Cancel"
                variant="outline"
                onPress={closeRemoveAgentModal}
                className="flex-1"
              />
              <Button
                label="Remove"
                variant="default"
                className="flex-1"
                onPress={removeAgent}
              />
            </View>
          </>
        }
      />

      {/* Rating Agent Modal */}
      <CustomModal
        visible={isRatingAgentModalVisible}
        onClose={closeRatingAgentModal}
        Content={
          <>
            {rated ? (
              <View className="items-center justify-center gap-4">
                <Text>You have already rated this agent</Text>
                <Text>Thank you for your feedback!</Text>
                <Pressable
                  className="bg-primary-700 px-3 py-3 rounded-md w-fit"
                  onPress={closeRatingAgentModal}
                >
                  <Text className="font-airbnb_md text-lg text-white w-fit">
                    Close
                  </Text>
                </Pressable>
              </View>
            ) : (
              <>
                <View className="pb-8">
                  <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
                    Rate the agent
                  </Text>
                  <View className="flex-row items-center">
                    <StarRating onRatingSelected={handleRatingSelected} />
                  </View>
                </View>

                <View className="pb-8">
                  <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
                    Agent Review Message
                  </Text>
                  <TextInput
                    placeholder="Rating message for the agent"
                    className="h-[100px] border border-[#ECE9E8] bg-[#FFFAF9] rounded-xl p-4 mb-4 font-airbnb_bk text-primary-main700"
                    multiline={true}
                    textAlignVertical="top"
                    placeholderTextColor="#9E9E9E"
                    value={ratingMessage}
                    onChangeText={(text) => {
                      setRatingMessage(text);
                    }}
                  />
                </View>

                <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
                  Upload Video Review (Optional)
                </Text>

                {!reviewVideoUri ? (
                  <Pressable
                    className="mb-4 border-2 border-dashed border-[#6B7280] rounded-xl p-5 items-center justify-center gap-2"
                    onPress={pickReviewVideo}
                    disabled={uploadingReviewVideo}
                  >
                    <Feather name="upload" size={24} color="#6B7280" />
                    <Text className="font-airbnb_md text-sm text-[#6B7280]">
                      Click to upload video (Max 200MB)
                    </Text>
                  </Pressable>
                ) : (
                  <View className="mb-4">
                    <View className="flex-row justify-between items-center mb-2">
                      <Text className="font-airbnb_md text-sm text-text-600">
                        Video selected
                      </Text>
                      <View className="flex-row">
                        <Pressable
                          className="bg-primary-700 px-3 py-1 rounded-md mr-2"
                          onPress={() => setIsVideoPreviewVisible(true)}
                        >
                          <Text className="font-airbnb_md text-sm text-white">
                            Preview
                          </Text>
                        </Pressable>
                        <Pressable
                          className="bg-gray-200 px-3 py-1 rounded-md"
                          onPress={() => setReviewVideoUri(null)}
                        >
                          <Text className="font-airbnb_md text-sm text-gray-700">
                            Remove
                          </Text>
                        </Pressable>
                      </View>
                    </View>
                    <View className="h-20 bg-gray-100 rounded-lg flex-row items-center justify-center">
                      <Feather name="video" size={24} color="#6B7280" />
                    </View>
                  </View>
                )}

                <View className="flex-row gap-3 mt-2">
                  <Button
                    label="Cancel"
                    variant="outline"
                    onPress={closeRatingAgentModal}
                    className="flex-1"
                  />
                  <Button
                    label="Submit"
                    variant="default"
                    className="flex-1"
                    onPress={handleRatingAgent}
                  />
                </View>
              </>
            )}
          </>
        }
      />

      {/* Video preview modal */}
      <CustomModal
        visible={isVideoPreviewVisible}
        onClose={() => setIsVideoPreviewVisible(false)}
        Content={
          <View className="w-full">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="font-airbnb_bd text-lg text-secondary-main700">
                Video Preview
              </Text>
              <Pressable
                onPress={() => setIsVideoPreviewVisible(false)}
                className="bg-black/60 rounded-full w-8 h-8 items-center justify-center"
              >
                <Text className="font-airbnb_bd text-lg text-white">✕</Text>
              </Pressable>
            </View>
            {reviewVideoUri && (
              <Video
                source={{ uri: reviewVideoUri }}
                useNativeControls
                resizeMode={ResizeMode.CONTAIN}
                style={{ width: '100%', height: 250, borderRadius: 8 }}
                shouldPlay
              />
            )}
          </View>
        }
      />
    </View>
  );
}
