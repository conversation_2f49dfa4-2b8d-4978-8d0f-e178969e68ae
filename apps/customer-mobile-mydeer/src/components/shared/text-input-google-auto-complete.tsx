import React, { useState, useEffect } from 'react';
import { FieldError } from 'react-hook-form';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Platform,
  Pressable,
  Text,
  Modal,
  SafeAreaView,
  View,
  TextInput,
} from 'react-native';
import { Button } from '../ui';
import { Env } from '@env';
import { showMessage } from 'react-native-flash-message';
import {
  GooglePlacesAutocomplete,
  Point,
  Query,
} from 'react-native-google-places-autocomplete';
import { iosMapApiKey, androidMapApiKey } from '@/utils/google-api-keys';

interface InputGoogleAutoCompleteProps {
  onLatLngChange?: (lat: string, lng: string) => void;
  placeholder?: string;
  onAddressComponentsChange?: (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => void;
  children: React.ReactNode;
  value?: string;
  northMaxLat?: number | null;
  southMaxLat?: number | null;
  westMaxLng?: number | null;
  eastMaxLng?: number | null;
  error?: FieldError;
  getAddressValue?: (text: string) => void;
}

const TextInputGoogleAutoComplete = ({
  onLatLngChange,
  onAddressComponentsChange,
  error,
  value,
  placeholder,
  northMaxLat,
  southMaxLat,
  westMaxLng,
  eastMaxLng,
  children,
  ...props
}: InputGoogleAutoCompleteProps) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [addressComponents, setAddressComponents] = useState({
    state: '',
    country: '',
    googleAutocompletePlaceId: '',
    pincode: '',
  });
  const [showSuggestion, setShowSuggestion] = useState(true);
  const query: Query = {
    key: Platform.OS === 'ios' ? iosMapApiKey : androidMapApiKey,
    language: 'en',
    components: 'country:in',
  };
  console.log('query recieved ', query);

  if (northMaxLat && southMaxLat && westMaxLng && eastMaxLng) {
    console.log(
      'south, west, north, east',
      southMaxLat,
      westMaxLng,
      northMaxLat,
      eastMaxLng
    );
  }

  return (
    <>
      <Pressable onPress={() => setModalVisible(true)}>
        {children ? (
          children
        ) : (
          <Text>{addressComponents.state || 'Select Address'}</Text>
        )}
      </Pressable>

      <Modal
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
        animationType="slide"
        transparent={false}
        supportedOrientations={['portrait', 'landscape']}
      >
        <LinearGradient
          colors={['#FFFCFB', '#FACABD']}
          start={{ x: 0, y: 0.69 }}
          end={{ x: 0, y: 1 }}
          style={{ flex: 1 }}
        >
          <SafeAreaView className="flex-1">
            <View className="flex-1 m-5">
              <GooglePlacesAutocomplete
                placeholder="Search"
                fetchDetails={true}
                onFail={(error) => {
                  console.log('failed to fetch', error);

                  showMessage({
                    message: 'failed to fetch',
                    type: 'danger',
                  });
                }}
                autoFillOnNotFound
                onPress={(data, details = null) => {
                  if (details && details.geometry?.location) {
                    setShowSuggestion(true);
                    console.log('showing result on press data');

                    const { lat, lng } = details.geometry.location;
                    console.log(lat, lng);
                    onAddressComponentsChange &&
                      onAddressComponentsChange({
                        lat: lat.toString(),
                        lng: lng.toString(),
                        place_id: details.place_id,
                        address_components: details.address_components,
                        address: details.formatted_address,
                      });
                    setModalVisible(false);
                  } else {
                    showMessage({
                      message: 'No location found',
                      type: 'danger',
                    });
                  }
                }}
                query={query}
                styles={{
                  textInputContainer: {
                    backgroundColor: 'white',
                    borderColor: '#F04D24',
                    borderWidth: 1,
                    borderRadius: 4,
                    marginHorizontal: 20,
                  },
                  textInput: {
                    cursorCGolor: '#F04D24',
                  },
                  row: {
                    flexDirection: 'row',
                  },
                  description: {
                    // flexDirection: 'row',
                    flex: 1,
                    // width: '100%',
                    flexWrap: 'wrap',
                  },
                }}
              />
            </View>
            <View style={{ paddingHorizontal: 15 }}>
              <Button label="Cancel" onPress={() => setModalVisible(false)} />
            </View>
          </SafeAreaView>
        </LinearGradient>
      </Modal>
    </>
  );
};

export default TextInputGoogleAutoComplete;
