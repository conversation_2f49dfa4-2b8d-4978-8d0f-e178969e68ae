/* eslint-disable max-lines-per-function */
import type { ConfigContext, ExpoConfig } from '@expo/config';
import type { AppIconBadgeConfig } from 'app-icon-badge/types';

import { ClientEnv, Env } from './env';

// const appIconBadgeConfig: AppIconBadgeConfig = {
//   enabled: Env.APP_ENV !== 'production',
//   badges: [
//     {
//       text: Env.APP_ENV,
//       type: 'banner',
//       color: 'white',
//     },
//     {
//       text: Env.VERSION.toString(),
//       type: 'ribbon',
//       color: 'white',
//     },
//   ],
// };

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'MyDeer',
  description: `MyDeer`,
  owner: 'nextfly_technologies',
  scheme: 'com.nextflytech.mydeer',
  slug: 'mydeer-customer',
  version: '2',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'light',
  // splash: {
  //   image: './assets/splash.png',
  //   resizeMode: 'cover',
  //   backgroundColor: '#2E3C4B',
  // },
  updates: {
    url: 'https://u.expo.dev/0d682abe-2acf-43bb-bc44-555db00b25ef',
    fallbackToCacheTimeout: 0,
  },
  runtimeVersion: '0.0.2',
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.nextflytech.mydeer',
    runtimeVersion: '0.0.2',
    infoPlist: {
      NSCameraUsageDescription:
        'The app accesses your camera to let you take photos.',
      NSPhotoLibraryUsageDescription:
        'The app accesses your photos to let you share them.',
      NSPhotoLibraryAddUsageDescription:
        'The app accesses your photo library to save photos.',
      LSApplicationQueriesSchemes: ['tel', 'mailto'],
    },
    // config: {
    //   googleMapsApiKey: "AIzaSyA8xcWnQtKcNfWlbST_AE0GOHAYs1mNC9w",
    // },
  },
  experiments: {
    typedRoutes: true,
  },
  android: {
    // edgeToEdgeEnabled: true,
    softwareKeyboardLayoutMode: 'pan',
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#2E3C4B',
    },
    package: 'com.nextflytech.mydeer', //Env.PACKAGE,
    runtimeVersion: '0.0.2',
    versionCode: 2,
    permissions: [
      'CAMERA',
      'READ_EXTERNAL_STORAGE',
      'WRITE_EXTERNAL_STORAGE',
      'READ_MEDIA_IMAGES',
    ],
    config: {
      googleMaps: {
        apiKey: 'AIzaSyDU7HhmW9U0VikdE77EiHpVKK3ZhoV1EBU',
      },
    },
  },
  web: {
    favicon: './assets/favicon.png',
    bundler: 'metro',
  },
  plugins: [
    'expo-secure-store',
    [
      'expo-font',
      {
        fonts: [
          './assets/fonts/Inter.ttf',
          './assets/fonts/AirbnbCereal_W_XBd.otf',
          './assets/fonts/AirbnbCereal_W_Md.otf',
          './assets/fonts/AirbnbCereal_W_Lt.otf',
          './assets/fonts/AirbnbCereal_W_Blk.otf',
          './assets/fonts/AirbnbCereal_W_Bk.otf',
          './assets/fonts/AirbnbCereal_W_Bd.otf',
        ],
      },
    ],
    'expo-localization',
    'expo-router',
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
      },
    ],
    [
      'app-icon-badge',
      {
        enabled: Env.APP_ENV !== 'production',
        badges: [
          {
            text: Env.APP_ENV,
            type: 'banner',
            color: 'white',
          },
          {
            text: Env.VERSION.toString(),
            type: 'ribbon',
            color: 'white',
          },
        ],
      },
    ],
    [
      'expo-image-picker',
      {
        photosPermission: 'The app accesses your photos to let you share them.',
        cameraPermission:
          'The app accesses your camera to let you take photos.',
        microphonePermission:
          'The app accesses your microphone to let you take photos.',
      },
    ],
    [
      'expo-document-picker',
      {
        iCloudContainerEnvironment: 'Production',
      },
    ],
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
      },
    ],
    [
      'expo-video',
      {
        supportsBackgroundPlayback: false,
        supportsPictureInPicture: false,
      },
    ],
    [
      'expo-splash-screen',
      {
        backgroundColor: '#FFFFFF',
        image: './assets/splash-icon.png',
        // dark: {
        //   image: './assets/splash-icon.png',
        //   backgroundColor: '#000000',
        // },
        imageWidth: 250,
      },
    ],
    [
      'expo-build-properties',
      {
        android: {
          targetSdkVersion: 35,
        },
      },
    ],
    [
      "react-native-edge-to-edge",
      {
        android: {
          parentTheme: 'Light',
          enforceNavigationBarContrast: false,
        },
      },
    ]
    // ['app-icon-badge', appIconBadgeConfig],
  ],
  extra: {
    ...ClientEnv,
    eas: {
      projectId: 'f5caf0ea-c9cf-43b5-98b0-64e644a869b1', //Env.EAS_PROJECT_ID,
    },
  },
});
