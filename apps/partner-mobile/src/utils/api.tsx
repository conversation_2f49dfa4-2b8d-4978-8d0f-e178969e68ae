import React, { useState } from 'react';
// Polyfills for web-streams-polyfill
import { ReadableStream, TransformStream } from 'web-streams-polyfill/es5';

globalThis.ReadableStream = globalThis.ReadableStream || ReadableStream;
globalThis.TransformStream = globalThis.TransformStream || TransformStream;
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  splitLink,
  httpBatchLink,
  httpSubscriptionLink,
  loggerLink,
} from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import superjson from 'superjson';

import type { AppRouter } from '@repo/partner-api';

import { getBaseUrl } from './base-url';
import { getToken } from './session-store';

/**
 * A set of typesafe hooks for consuming your API.
 */
export const api = createTRPCReact<AppRouter>();
export { type RouterInputs, type RouterOutputs } from '@repo/partner-api';

/**
 * A wrapper for your app that provides the TRPC context.
 * Use only in _app.tsx
 */
export function TRPCProvider(props: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient());
  const [trpcClient] = useState(() =>
    api.createClient({
      links: [
        loggerLink({
          enabled: (opts) =>
            process.env.NODE_ENV === 'development' ||
            (opts.direction === 'down' && opts.result instanceof Error),
          colorMode: 'ansi',
        }),
        // httpBatchLink({
        //   transformer: superjson,
        //   url: `${getBaseUrl()}/api/trpc`,
        //   headers() {
        //     const headers = new Map<string, string>();
        //     headers.set('x-trpc-source', 'expo-react');
        //
        //     const token = getToken();
        //     if (token) headers.set('Authorization', `Bearer ${token}`);
        //     console.log('token', Object.fromEntries(headers));
        //     return Object.fromEntries(headers);
        //   },
        // }),
        splitLink({
          condition: (op) => op.type === 'subscription',
          true: httpSubscriptionLink({
            transformer: superjson,
            url: getBaseUrl() + '/api/trpc',
            // TODO: remove this @ts-ignore
            // @ts-ignore
            headers: () => {
              const headers = new Map<string, string>();
              headers.set('x-trpc-source', 'expo-react');

              const token = getToken();
              if (token) headers.set('Authorization', `Bearer ${token}`);
              console.log('token', Object.fromEntries(headers));
              return Object.fromEntries(headers);
            },
          }),
          false: httpBatchLink({
            transformer: superjson,
            url: `${getBaseUrl()}/api/trpc`,
            headers: () => {
              const headers = new Map<string, string>();
              headers.set('x-trpc-source', 'expo-react');

              const token = getToken();
              if (token) headers.set('Authorization', `Bearer ${token}`);

              return Object.fromEntries(headers);
            },
          }),
        }),
      ],
    }),
  );

  return (
    <api.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {props.children}
      </QueryClientProvider>
    </api.Provider>
  );
}
