import React from 'react';
import { useVideoPlayer, VideoView } from 'expo-video';
import { cn } from '@/ui/utils';
import { Pressable } from 'react-native';

const VideoComponent = React.memo(
  ({ videoSource, className }: { videoSource: string; className?: string }) => {
    const player = useVideoPlayer(videoSource, (player) => {
      console.log('player', player, videoSource);
      player.loop = false;
      // player.play();
    });

    console.log('player', player);

    return (
      <Pressable className="mb-5">
        <VideoView
          style={{ aspectRatio: 16 / 9, width: '100%' }}
          className={cn('mb-2', className)}
          player={player}
          allowsFullscreen={false}
          allowsPictureInPicture={false}
        />
      </Pressable>
    );
  },
);

export default VideoComponent;
