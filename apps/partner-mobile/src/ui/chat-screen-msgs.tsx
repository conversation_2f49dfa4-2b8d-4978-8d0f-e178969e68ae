import { Image as NImage } from 'expo-image';
import React from 'react';
import { Pressable, Text, View } from 'react-native';

type PropsType = {
  message: string;
  isTrue?: boolean;
  time: string;
  unread?: boolean;
};

export const LeftMsg = ({ message, isTrue, time, unread }: PropsType) => {
  return (
    <View className="my-1 w-auto max-w-[85%] self-start rounded-t-[10px] rounded-br-[10px] bg-primary-100 p-3">
      <Text className="font-airbnb_bk text-sm font-normal text-text-600">
        {message}
      </Text>
      {isTrue ? (
        <Pressable>
          <Text className="font-airbnb_bd text-sm font-bold text-secondary-650">
            Read more
          </Text>
        </Pressable>
      ) : null}
      <View className="flex-row items-center justify-end gap-1">
        <Text
          className={`font-airbnb_md text-[10px] font-medium ${unread ? 'text-[#999999]' : 'text-secondary-main700'}`}
        >
          {time}
        </Text>
        {/*<NImage*/}
        {/*  source={require('../../assets/icons/doublecheck.png')}*/}
        {/*  className="h-2.5 w-2.5"*/}
        {/*  tintColor={unread ? '#999999' : '#F04D24'}*/}
        {/*/>*/}
      </View>
    </View>
  );
};

export const RightMsg = ({ message, isTrue, time, unread }: PropsType) => {
  return (
    <View className="my-4 w-auto max-w-[85%] items-end self-end rounded-b-[10px] rounded-tl-[10px] bg-[#F2F2F2] p-3">
      <Text className="font-airbnb_bk text-sm font-normal text-text-600">
        {message}
      </Text>
      {isTrue ? (
        <Pressable>
          <Text className="font-airbnb_bd text-sm font-bold text-secondary-650">
            Read more
          </Text>
        </Pressable>
      ) : null}
      <View className="flex-row items-center justify-end gap-1">
        <Text
          className={`font-airbnb_md text-[10px] font-medium ${unread ? 'text-[#999999]' : 'text-secondary-main700'}`}
        >
          {time}
        </Text>
        {/*<NImage*/}
        {/*  source={require('../../assets/icons/doublecheck.png')}*/}
        {/*  className="h-2.5 w-2.5"*/}
        {/*  tintColor={unread ? '#999999' : '#F04D24'}*/}
        {/*/>*/}
      </View>
    </View>
  );
};
