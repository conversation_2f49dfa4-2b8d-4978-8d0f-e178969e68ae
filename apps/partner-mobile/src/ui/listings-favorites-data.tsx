export interface Item {
  id: number;                
  locationname: string;     
  location: string;          
  locationarea: string;      
  locationdetails: string;   
  sale: string;              
  features: string[];        
  backgroundimg: boolean;    
}

export interface FavouritesComponentProps {
  item: Item;
  handlePress: (id: number) => void;
}


export const Data = [
  {
    id: 1,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: [
      'Semi-furnished',
      '3.5km away from mall',
      'Ready to move',
      'Posted by agent',
    ],
    backgroundimg: true,
  },
  {
    id: 2,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: [
      'Semi-furnished',
      '3.5km away from mall',
      'Ready to move',
      'Posted by agent',
    ],
    backgroundimg: true,
  },
  {
    id: 3,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: [
      'Semi-furnished',
      '3.5km away from mall',
      'Ready to move',
      'Posted by agent',
    ],
    backgroundimg: true,
  },
  {
    id: 4,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: [
      'Semi-furnished',
      '3.5km away from mall',
      'Ready to move',
      'Posted by agent',
    ],
    backgroundimg: true,
  },
];
