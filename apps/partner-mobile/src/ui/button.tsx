import React from 'react';
import type { PressableProps, View } from 'react-native';
import { ActivityIndicator, Pressable, Text } from 'react-native';
import type { VariantProps } from 'tailwind-variants';
import { tv } from 'tailwind-variants';

const button = tv({
  slots: {
    container: 'my-3 flex-row items-center justify-center rounded-md px-4',
    label: 'font-inter text-base font-semibold',
    indicator: 'h-6 text-white',
  },

  variants: {
    variant: {
      default: {
        container: 'bg-secondary-650',
        label: 'text-primary-50',
        indicator: 'text-white',
      },
      onboarding: {
        container: 'bg-secondary-650',
        label: 'text-white',
        indicator: 'text-white',
      },
      outline: {
        container: 'border border-neutral-400',
        label: 'text-black',
        indicator: 'text-black',
      },
      chat: {
        container: 'bg-secondary-main700',
        label: 'text-primary-50',
        indicator: 'text-white',
      },
      otp: {
        container: 'bg-primary-50',
        label:
          'items-center font-airbnb_md text-base font-medium text-secondary-650',
        indicator: 'text-white',
      },
      step: {
        container: 'bg-secondary-main700',
        label: 'text-white',
        indicator: 'text-white',
      },
      step4: {
        container: 'bg-secondary-main700',
        label: 'text-white',
        indicator: 'text-white',
      },
      ghost: {
        container: 'bg-transparent',
        label: 'text-black underline',
        indicator: 'text-black',
      },
      link: {
        container: 'bg-transparent',
        label: 'text-black',
        indicator: 'text-black',
      },
    },
    size: {
      default: {
        container: 'h-14 rounded-xl px-4',
        label: 'text-base',
      },
      onboarding: {
        container: 'h-14 rounded-xl',
        label: 'font-airbnb_md text-base font-medium',
      },
      sm: {
        container: 'h-8 px-3',
        label: 'text-sm',
        indicator: 'h-2',
      },
      chat: {
        container: 'h-14 w-4/5 rounded-xl',
        label: 'text-base font-medium text-white',
        indicator: 'h-2',
      },
      step: {
        container: 'h-14 px-6 py-3.5 w-1/3 rounded-xl',
        label: 'text-base font-medium text-white',
        indicator: 'h-2',
      },
      step4: {
        container: 'h-14 w-1/2 rounded-xl',
        label: 'text-base font-medium text-white',
        indicator: 'h-2',
      },
      icon: { container: 'h-9 w-9' },
    },
    disabled: {
      true: {
        container: 'bg-neutral-300',
        label: 'text-neutral-600',
        indicator: 'text-neutral-400',
      },
    },
    fullWidth: {
      true: {
        container: 'w-90%',
      },
      false: {
        container: 'self-center',
      },
    },
  },
  defaultVariants: {
    variant: 'default',
    disabled: false,
    fullWidth: true,
    size: 'default',
  },
});

type ButtonVariants = VariantProps<typeof button>;
interface Props extends ButtonVariants, Omit<PressableProps, 'disabled'> {
  label?: string;
  loading?: boolean;
  className?: string;
  textClassName?: string;
}

export const Button = React.forwardRef<View, Props>(
  (
    {
      label: text,
      loading = false,
      variant = 'default',
      disabled = false,
      size = 'default',
      className = '',
      testID,
      textClassName = '',
      ...props
    },
    ref,
  ) => {
    const styles = React.useMemo(
      () => button({ variant, disabled, size }),
      [variant, disabled, size],
    );

    return (
      <Pressable
        disabled={disabled || loading}
        className={styles.container({ className })}
        {...props}
        ref={ref}
        testID={testID}
      >
        {props.children ? (
          props.children
        ) : (
          <>
            {loading ? (
              <ActivityIndicator
                size="small"
                className={styles.indicator()}
                testID={testID ? `${testID}-activity-indicator` : undefined}
              />
            ) : (
              <Text
                testID={testID ? `${testID}-label` : undefined}
                className={styles.label({ className: textClassName })}
              >
                {text}
              </Text>
            )}
          </>
        )}
      </Pressable>
    );
  },
);
