interface DataItem {
  id: number;
  name: string;
  company: string;
  sold: string;
  experience: string;
}

export const Data: DataItem[] = [
  {
    id: 1,
    // starRating: 4.5,
    // activeProperties: 37,
    // yearsExperience: 5.2,
    name: '<PERSON><PERSON>',
    company: 'Omkiron Properties',
    sold: '27 Properties Sold',
    experience: '5yrs Experience',
  },
  {
    id: 2,
    // starRating: 4.5,
    // activeProperties: 37,
    // yearsExperience: 5.2,
    name: '<PERSON><PERSON>',
    company: 'Omkiron Properties',
    sold: '27 Properties Sold',
    experience: '5yrs Experience',
  },
  {
    id: 3,
    // starRating: 4.5,
    // activeProperties: 37,
    // yearsExperience: 5.2,
    name: '<PERSON><PERSON>',
    company: 'Omkiron Properties',
    sold: '27 Properties Sold',
    experience: '5yrs Experience',
  },
  {
    id: 4,
    // starRating: 4.5,
    // activeProperties: 37,
    // yearsExperience: 5.2,
    name: '<PERSON><PERSON>',
    company: 'Omkiron Properties',
    sold: '27 Properties Sold',
    experience: '5yrs Experience',
  },
  {
    id: 5,
    // starRating: 4.5,
    // activeProperties: 37,
    // yearsExperience: 5.2,
    name: '<PERSON><PERSON>',
    company: 'Omkiron Properties',
    sold: '27 Properties Sold',
    experience: '5yrs Experience',
  },
];
