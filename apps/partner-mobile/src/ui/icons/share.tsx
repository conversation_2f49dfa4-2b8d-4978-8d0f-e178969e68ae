import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { Path } from 'react-native-svg';

import colors from '../colors';

export const Share = ({ color = colors.neutral[500], ...props }: SvgProps) => (
  <Svg width={24} height={24} fill="none" viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17 23.5H7A2.5 2.5 0 0 1 4.5 21v-9A2.5 2.5 0 0 1 7 9.5h2a.5.5 0 1 1 0 1H7A1.5 1.5 0 0 0 5.5 12v9A1.5 1.5 0 0 0 7 22.5h10a1.5 1.5 0 0 0 1.5-1.5v-9a1.5 1.5 0 0 0-1.5-1.5h-2a.5.5 0 0 1 0-1h2a2.5 2.5 0 0 1 2.5 2.5v9a2.5 2.5 0 0 1-2.5 2.5ZM15.667 5.358 12.5 2.19V15a.5.5 0 0 1-1 0V2.19L8.333 5.357a.489.489 0 0 1-.69-.69L11.595.715c.016-.024.026-.051.047-.073a.487.487 0 0 1 .353-.141L12 .5l.005.001a.486.486 0 0 1 .352.141c.**************.046.07l3.955 3.955a.488.488 0 0 1-.691.69Z"
      fill={color}
    />
  </Svg>
);
