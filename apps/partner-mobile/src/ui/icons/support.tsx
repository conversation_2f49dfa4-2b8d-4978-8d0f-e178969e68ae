import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { ClipPath, Defs, G, Path } from 'react-native-svg';

import colors from '../colors';

export const Support = ({
  color = colors.neutral[500],
  ...props
}: SvgProps) => (
  <Svg width={24} height={24} fill="none" viewBox="0 0 24 24" {...props}>
    <G clipPath="url(#a)">
      <Path
        d="m12.533 19.866 7.594-7.594c1.865-1.875 2.137-4.94.375-6.9a4.875 4.875 0 0 0-7.078-.197L11.999 6.61 10.77 5.372c-1.874-1.866-4.94-2.137-6.9-.375a4.875 4.875 0 0 0-.196 7.078l7.79 7.79a.76.76 0 0 0 1.069 0v0Z"
        stroke={color}
        strokeWidth={1.219}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h24v24H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
