export const Data = [
  {
    id: 1,
    source: require('../../assets/images/options/av7.png'),
    name: ' <PERSON>',
    detail: 'Platina House: Property',
    type: 'Agent',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 2,
    source: require('../../assets/images/options/av7.png'),
    name: ' <PERSON>',
    detail: 'Platina House: Property',
    type: 'Client',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 3,
    source: require('../../assets/images/options/av7.png'),
    name: ' <PERSON>',
    detail: 'Platina House: Property',
    type: 'Agent',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 4,
    source: require('../../assets/images/options/av7.png'),
    name: ' <PERSON>',
    detail: 'Platina House: Property',
    type: 'Client',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 5,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Agent',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 6,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Client',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 7,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Agent',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 8,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Client',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 9,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Agent',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 10,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Client',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 11,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Agent',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 12,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Client',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 13,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Agent',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
  {
    id: 14,
    source: require('../../assets/images/options/av7.png'),
    name: ' Jason Allen',
    detail: 'Platina House: Property',
    type: 'Client',
    time: ' 10.59pm',
    noofmsg: '2',
    msg: 'It was really great to meet you, hope y..',
  },
];
