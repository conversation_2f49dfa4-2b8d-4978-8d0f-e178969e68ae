export const Data = [
  {
    id: 1,
    title: ' New Match !',
    time: ' 10.59pm',
    text: ' <PERSON><PERSON> has sent you a request to be your travel companion! Checkout their profile and ...',
    source: require('../../assets/images/options/av3.png'),
    
  },
  {
    id: 2,
    title: 'We’ve Got You!',
    time: ' 10.59pm',
    text: 'Thedal Support has responded to your inquiry. Check your message for their helpful reply...',
    source: require('../../assets/images/options/av4.png'),
    
  },
  {
    id: 3,
    title: 'Profile Updated',
    time: ' 10.59pm',
    text: `Your profile information has been successfully updated. Now you're one step closer to...`,
    source: require('../../assets/images/options/av5.png'),
   
  },
  {
    id: 4,
    title: 'Yash Messaged You',
    time: ' 10.59pm',
    text: ' ( 2 unread message) Your companion has just messaged you , reply ....',
    source: require('../../assets/images/options/av7.png'),
  
  },
  {
    id: 5,
    title: 'New Message',
    time: ' 10.59pm',
    text: 'Thank you for your payment! you’re all set for your upcoming trip with your travel companion...',
    source: require('../../assets/images/options/av10.png'),
  
  },
  {
    id: 6,
    title: 'New Message',
    time: ' 10.59pm',
    text: ` Your profile information has been successfully updated. Now you're one step closer to...`,
    source: require('../../assets/images/options/av12.png'),
  
  },
  {
    id: 7,
    title: 'New Message',
    time: ' 10.59pm',
    text: ` Your profile information has been successfully updated. Now you're one step closer to...`,
    source: require('../../assets/images/options/av14.png'),
  
  },
  {
    id: 8,
    title: ' New Match !',
    time: ' 10.59pm',
    text: ' Sriya has sent you a request to be your travel companion! Checkout their profile and ...',
    source: require('../../assets/images/options/av3.png'),
  },
  {
    id: 9,
    title: 'We’ve Got You!',
    time: ' 10.59pm',
    text: 'Thedal Support has responded to your inquiry. Check your message for their helpful reply...',
    source: require('../../assets/images/options/av4.png'),

  },
  {
    id: 10,
    title: 'Profile Updated',
    time: ' 10.59pm',
    text: `Your profile information has been successfully updated. Now you're one step closer to...`,
    source: require('../../assets/images/options/av5.png'),
  
  },
  {
    id: 11,
    title: 'Yash Messaged You',
    time: ' 10.59pm',
    text: ' ( 2 unread message) Your companion has just messaged you , reply ....',
    source: require('../../assets/images/options/av7.png'),
  
  },
  {
    id: 12,
    title: 'New Message',
    time: ' 10.59pm',
    text: 'Thank you for your payment! you’re all set for your upcoming trip with your travel companion...',
    source: require('../../assets/images/options/av10.png'),
  
  },
  {
    id: 13,
    title: 'New Message',
    time: ' 10.59pm',
    text: ` Your profile information has been successfully updated. Now you're one step closer to...`,
    source: require('../../assets/images/options/av12.png'),
  
  },
  {
    id: 14,
    title: 'New Message',
    time: ' 10.59pm',
    text: ` Your profile information has been successfully updated. Now you're one step closer to...`,
    source: require('../../assets/images/options/av14.png'),
 
  },
];
