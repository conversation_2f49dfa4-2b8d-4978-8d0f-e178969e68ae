export interface ListingItem {
  id: number;
  locationname: string;
  location: string;
  locationarea: string;
  locationdetails: string;
  sale: string;
  features: string[];
}

export interface ListingItemProps {
  item: ListingItem;
}

export const Data: ListingItem[] = [
  {
    id: 1,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: ['Semi-furnished', 'Ready to move', 'Posted by agent'],
  },
  {
    id: 2,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: ['Semi-furnished', 'Ready to move', 'Posted by agent'],
  },
  {
    id: 3,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: ['Semi-furnished', 'Ready to move', 'Posted by agent'],
  },

  {
    id: 4,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: ['Semi-furnished', 'Ready to move', 'Posted by agent'],
  },
  {
    id: 5,
    locationname: 'Platina House',
    location: ' Delhi, Sector 48, NCR',
    locationarea: '3900/ sq. ft',
    locationdetails:
      ' This upscale hill in a contemporary high-rise is a 4-minute walk from Surfers Paradise Beach...',
    sale: 'For Sale',
    features: ['Semi-furnished', 'Ready to move', 'Posted by agent'],
  },
];
