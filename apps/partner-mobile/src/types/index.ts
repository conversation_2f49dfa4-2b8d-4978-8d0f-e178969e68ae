export enum ConnectionRequestsEnum {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING'
}

export interface FeedType {
  id: string;
  content: string;
  totalComments: number;
  totalLikes: number;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    name: string;
    company: {
      companyName: string;
    } | null;
  };
  media: {
    filePublicUrl: string | null;
    cloudinaryUrl: string | null;
    cloudinaryId: string | null;
    mediaType: string;
  }[];
  comments: {
    comment: string;
    isPinned: boolean;
    createdAt: Date;
    userId: string | null;
    customerId: string | null;
    user: {
      id: string;
      name: string;
      filePublicUrl: string | null;
      companyDetails: {
        companyName: string;
      } | null;
    } | null;
    customer: {
      id: string;
      name: string;
      profileImagePublicUrl: string | null;
      cloudinaryImagePublicUrl: string | null;
    } | null;
  }[];
  likes: {
    id: string;
    postId: string;
  }[];
};

export interface AgentProps {
  name: string;
  id: string;
  createdAt: Date;
  verifiedAgent: boolean | null;
  experience: string | null;
  propertiesSold: number | null;
  rating: string | null;
  filePublicUrl: string | null;
  cloudinaryProfileImageUrl: string | null;
  coustomerConnections?: { id: string }[];
  operationArea?: any[];
  customerRatingsToAgents?: any[];
  properties?: any[];
  company?: any;
  userLocation?: string;
  bio?: string;
  languages?: any[];
  reviews?: number;
}

export interface IProperty {
  id: string;
  areaUnit: {
    id: string;
    name: string;
  } | null;
  utilities: {
    id: string;
    name: string;
  }[];
  amenities: {
    id: string;
    name: string;
  }[];
  user: {
    id: string;
    company: any | null;
    companyDetails: any | null;
    name: string;
    rating: string | null;
    reviews: number | null;
  };
  customerFavourites: {
    id: string;
    customerId: string;
    propertyId: string;
  }[];
  mediaSections: {
    id: string;
    propertyId: string;
    media: {
      id: string;
      fileKey: string | null;
      filePublicUrl: string | null;
      cloudinaryUrl: string | null;
      cloudinaryId: string | null;
    }[];
  }[];
};

export interface Message {
  id: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  senderId: string;
  connectionId: string;
  read: boolean;
}

export interface Rating {
  id: string;
  propertyId: string | null;
  connectionId: string;
  propertyRatingMessage: string | null;
  propertyStarsCount: number | null;
  userStarsCount: number | null;
  userRatingMessage: string | null;
  ratedByUserId: string;
  ratedToUserId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BaseChat {
  id: string;
  status: ConnectionRequestsEnum;
  blocked: boolean;
  createdAt: Date;
  messages: Message[];
  property: {
    id: string;
    propertyTitle: string | null;
  } | null;
  rating: Rating[];
}

export interface AgentChat extends BaseChat {
  user: {
    id: string;
    name: string;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    accountType: string;
    isOnline: boolean;
    lastActive: Date | null;
  };
  customer: null;
}

export interface CustomerChat {
  id: string;
  createdAt: Date;
  messages: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    content: string;
    senderId: string;
    connectionId: string;
    read: boolean;
  }[];
  property: {
    id: string;
    propertyTitle: string | null;
  } | null;
  user?: undefined;
  customer: {
    id: string;
    name: string;
    profileImagePublicUrl: string | null;
    cloudinaryImagePublicUrl: string | null;
    active: boolean;
  };
}

export type Chat = AgentChat;

export interface AgentCardConversation {
  id: string;
  property: {
    propertyTitle: string | null;
  } | null;
  customer: {
    name: string;
    profileImagePublicUrl: string | null;
    cloudinaryImagePublicUrl: string | null;
    filePublicUrl: string | null;
    accountType: string;
    isOnline: boolean;
    lastActive: Date | null;
  } | null;
  agent: {
    name: string;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    accountType: string;
    isOnline: boolean;
    lastActive: Date | null;
  } | null;
  messages: {
    id: string;
    content: string;
    createdAt: Date;
    senderId: string;
    read: boolean;
  }[];
  rating: {
    id: string;
    propertyRatingMessage: string | null;
    propertyStarsCount: number | null;
    userStarsCount: number | null;
    userRatingMessage: string | null;
  }[];
};
export interface CustomerCardConversation {
  id: string;
  property: {
    propertyTitle: string | null;
  } | null;
  customer: {
    name: string;
    profileImagePublicUrl: string | null;
    cloudinaryImagePublicUrl: string | null;
    active: boolean;
  } | null;
  messages: {
    id: string;
    content: string;
    createdAt: Date;
    senderId: string;
    read: boolean;
  }[];
  rating: {
    id: string;
    propertyRatingMessage: string | null;
    propertyStarsCount: number | null;
    userStarsCount: number | null;
    userRatingMessage: string | null;
  }[];
};

export interface ConnectionRequest {
  id: string;
  status: ConnectionRequestsEnum;
  sender: {
    id: string;
    name: string;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    rating: string | null;
    propertiesSold: number | null;
    experience: string | null;
    properties: {
      id: string;
    }[];
    companyDetails: {
      id: string;
      fileKey: string | null;
      email: string | null;
      phoneNumber: string | null;
      filePublicUrl: string | null;
      companyName: string | null;
      companyLocation: string | null;
    } | null;
  };
  receiver: {
    id: string;
    name: string;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    rating: string | null;
    propertiesSold: number | null;
    experience: string | null;
    properties: {
      id: string;
    }[];
    companyDetails: {
      id: string;
      fileKey: string | null;
      email: string | null;
      phoneNumber: string | null;
      filePublicUrl: string | null;
      companyName: string | null;
      companyLocation: string | null;
    } | null;
  };
};

export interface CustomerConnectionRequest {
  id: string;
  state: ConnectionRequestsEnum;
  customer: {
    id: string;
    name: string;
    profileImagePublicUrl: string | null;
    cloudinaryImagePublicUrl: string | null;
    onboardingPreference: string | null;
    locationAddress: string | null;
    city: string | null;
  } | null;
  agent: {
    id: string;
    name: string;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    rating: string | null;
    propertiesSold: number | null;
    experience: string | null;
    properties: {
      id: string;
    }[];
    companyDetails: {
      id: string;
      fileKey: string | null;
      email: string | null;
      phoneNumber: string | null;
      filePublicUrl: string | null;
      companyName: string | null;
      companyLocation: string | null;
    } | null;
  } | null;
};
