/* eslint-disable max-lines-per-function */
import { FlashList } from '@shopify/flash-list';
import React from 'react';
import { ScrollView, RefreshControl } from 'react-native';
import { useRouter } from 'expo-router';
import FavouritesComponent from '@/components/favourites-component';
import { api } from '@/utils/api';

const Favorites: React.FC = () => {
  const {
    data: favouriteProperties,
    isLoading: isLoadingGetLikedProperties,
    refetch,
  } = api.likeProperty.getLikedProperties.useQuery();
  const router = useRouter();

  function handlePress(id: string): void {
    router.navigate(`/property/${id}`);
  }

  return (
    <ScrollView
      className="flex-1 px-5 mt-5"
      refreshControl={
        <RefreshControl
          refreshing={isLoadingGetLikedProperties}
          onRefresh={refetch}
        />
      }
    >
      <FlashList
        showsVerticalScrollIndicator={false}
        data={favouriteProperties}
        estimatedItemSize={210}
        keyExtractor={(item) => item.id.toString()}
        className="flex-1"
        renderItem={({ item }) => {
          return (
            <FavouritesComponent
              item={item}
              handlePress={() => handlePress(item.id)}
            />
          );
        }}
      />
    </ScrollView>
  );
};

export default Favorites;
