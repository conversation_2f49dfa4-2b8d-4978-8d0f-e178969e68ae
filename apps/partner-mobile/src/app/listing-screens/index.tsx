import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import React from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import Favorites from './favorites';
import MyListings from './my-listings';

const Tab = createMaterialTopTabNavigator();

const initialLayout = {
  width: Dimensions.get('window').width,
};

interface TabLabelProps {
  focused: boolean;
  title: string;
}

const TabLabel: React.FC<TabLabelProps> = ({ focused, title }) => {
  return (
    <Text style={focused ? styles.activeLabel : styles.inactiveLabel}>
      {title}
    </Text>
  );
};

const CustomTabBarIndicator = ({ layout, navigation }: any) => {
  const activeIndex = navigation.getState().index;
  const tabWidth = layout.width / 2;
  const indicatorPercentage = 0.5;
  const indicatorWidth = tabWidth * indicatorPercentage;

  const leftPosition = activeIndex * tabWidth + (tabWidth - indicatorWidth) / 2;

  return (
    <View style={styles.tabIndicatorContainer}>
      <View
        style={[
          styles.tabIndicator,
          { left: leftPosition, width: indicatorWidth },
        ]}
      />
    </View>
  );
};

const CustomTabBar = (props: {
  state: { routes: { name: string }[]; index: number };
  layout: any;
  navigation: any;
}) => {
  return (
    <View style={styles.tabBar}>
      {props.state.routes.map((route, index) => {
        const isFocused = index === props.state.index;

        return (
          <TouchableOpacity
            style={styles.tabItem}
            key={index}
            onPress={() => {
              props.navigation.navigate(route.name);
            }}
          >
            <TabLabel focused={isFocused} title={route.name} />
          </TouchableOpacity>
        );
      })}
      <CustomTabBarIndicator
        layout={props.layout}
        navigation={props.navigation}
      />
    </View>
  );
};

export default function Index() {
  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      initialLayout={initialLayout}
    >
      <Tab.Screen
        name="My Listings"
        component={MyListings}
        options={{ tabBarLabel: 'My Listings' }}
      />
      <Tab.Screen
        name="My Favorites"
        component={Favorites}
        options={{ tabBarLabel: 'Favorites' }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#fff',
    height: 40,
    position: 'relative',
    flexDirection: 'row',
    // marginHorizontal:20
  },
  activeLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'bold',
    fontFamily: 'AirbnbW_Md',
    color: '#252525',
  },
  inactiveLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'normal',
    fontFamily: 'AirbnbW_Bk',
    color: '#252525',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIndicatorContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 2,
    backgroundColor: '#FDECC1',
  },
  tabIndicator: {
    backgroundColor: '#C58E00',
    height: '100%',
    position: 'absolute',
    bottom: 0,
  },
});
