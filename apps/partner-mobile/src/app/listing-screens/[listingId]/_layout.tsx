/* eslint-disable max-lines-per-function */
import Entypo from '@expo/vector-icons/Entypo';
import { Image as NImage } from 'expo-image';
import {
  Stack,
  useGlobalSearchParams,
  useNavigation,
  // useRouter,
} from 'expo-router';
import React from 'react';
import { Pressable, StyleSheet } from 'react-native';

export default function Layout() {
  const navigation = useNavigation();
  const params = useGlobalSearchParams();
  // const router = useRouter();
  return (
    <Stack>
      <Stack.Screen
        name="check-response"
        options={{
          title: params?.locationName?.toString() || 'Check Responses',
          headerTintColor: '#F15F3A',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFCFB' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => navigation.goBack()}>
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="check-responses-enquiry"
        options={{
          title:
            params?.locationName?.toString() || 'Check Responses Enquiries',
          headerTintColor: '#F15F3A',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFCFB' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => navigation.goBack()}>
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
          headerRight: () => (
            <Pressable
              className="rounded-lg bg-white px-3 py-2"
              style={styles.shadow}
            >
              <NImage
                source={require('../../../../assets/icons/filter.png')}
                className="h-6 w-6"
              />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="property-enquiry"
        options={{
          title: params?.locationName?.toString() || 'Property Enquiry',
          headerTintColor: '#F15F3A',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFCFB' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => navigation.goBack()}>
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
    </Stack>
  );
}

const styles = StyleSheet.create({
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
});
