import { FlashList } from '@shopify/flash-list';
import React from 'react';
import { View } from 'react-native';

import CheckResponseCard from '@/components/check-response-card';
import { Data } from '@/ui/check-response-data';

const CheckResponsesEnquiry = () => {
  return (
    <View className="flex-1 px-5">
      {/* FlashList for rendering multiple CheckResponseCard components */}
      <FlashList
        showsVerticalScrollIndicator={false}
        data={Data}
        keyExtractor={(item) => item.id.toString()}
        estimatedItemSize={200}
        className="pt-6"
        renderItem={({ item }) => <CheckResponseCard item={item} />}
      />
    </View>
  );
};

export default CheckResponsesEnquiry;
