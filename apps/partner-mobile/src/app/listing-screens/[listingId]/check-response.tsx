/* eslint-disable max-lines-per-function */
import { FlashList } from '@shopify/flash-list';
import { Image as NImage, ImageBackground } from 'expo-image';
import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import { Dimensions, Pressable, Text, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

import CheckResponseCard from '@/components/check-response-card';
import { Data } from '@/ui/check-response-data';
import { api } from '@/utils/api';
import UserPic from '@/components/user-pic';
import { formatTime } from '@/utils/format-terms';

const { height, width } = Dimensions.get('screen');

// type ProgressBarProps = {
//   value: number;
//   maxValue: number;
// };
//
// const ProgressBar: React.FC<ProgressBarProps> = ({ value, maxValue }) => {
//   const fillWidth = (value / maxValue) * 100;
//
//   return (
//     <View
//       className="rounded-lg bg-secondary-200"
//       style={{ width: width * 0.18 }}
//     >
//       <View
//         className="h-2 rounded-lg bg-secondary-main700"
//         style={{ width: `${fillWidth}%` }}
//       />
//     </View>
//   );
// };

const CheckResponse: React.FC = () => {
  const { data: profile } = api.user.profile.useQuery();
  const params = useLocalSearchParams<{ listingId: string }>();
  const { data, isSuccess, isLoading, isError } =
    api.chat.getRecentEnquiries.useQuery(
      {
        propertyId: params.listingId,
      },
      {
        enabled: !!params.listingId,
      },
    );

  const propertyUrl =
    data?.property?.mediaSections?.[0]?.media?.[0]?.cloudinaryUrl ??
    data?.property?.mediaSections?.[0]?.media?.[0]?.filePublicUrl;
  // console.log('data', data);

  return (
    <ScrollView>
      <View className="mt-6 flex-1 px-5">
        <ImageBackground
          source={
            propertyUrl
              ? { uri: propertyUrl }
              : require('../../../../assets/images/bgbg.png')
          }
          contentFit="cover"
          className="rounded-lg"
          style={{
            width: '100%',
            paddingTop: 16,
            aspectRatio: 1,
            // height: height * 0.11
          }}
        >
          <View className="ml-4 self-start rounded-lg bg-secondary-100 px-3 py-2">
            <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700">
              {data?.property?.propertyFor === 'SALE' ? 'Sale' : 'Rent'}
            </Text>
          </View>

          {/* Card details part */}
          {/*<View className="mt-8 flex-row items-center justify-between rounded-t-xl bg-white px-4 pt-4">*/}
          {/*  {features.map((label) => (*/}
          {/*    <View key={label} className="mx-2 p-0.5">*/}
          {/*      <Text className="font-airbnb_md text-[10px] font-medium text-primary-700">*/}
          {/*        {label}*/}
          {/*      </Text>*/}
          {/*    </View>*/}
          {/*  ))}*/}
          {/*</View>*/}
        </ImageBackground>

        <View className="bg-white p-4">
          <Text className="font-airbnb_bd text-lg font-bold text-text-main700">
            {data?.property?.propertyTitle}
          </Text>
          <View className="mt-0.5 flex-row items-center">
            <NImage
              source={require('../../../../assets/icons/location3.png')}
              className="aspect-square w-[3%]"
            />
            <Text className="ml-1 font-airbnb_bk text-xs font-normal text-text-600">
              {data?.property?.propertyAddress}
            </Text>
          </View>
          <Text className="mt-2.5 self-start font-airbnb_bk text-sm font-normal text-text-600">
            {data?.property?.aboutProperty}
          </Text>
        </View>

        {/* Property enquiry part */}
        <View className="flex-row items-center justify-between px-4 py-5">
          <View>
            <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700">
              Property Enquiry
            </Text>
            <View className="mt-1 flex-row items-center">
              <Text className="font-airbnb_bk text-[10px] font-normal text-text-550">
                {data?.conversations.length} new enquiries
              </Text>
            </View>
          </View>

          <View className="flex-row items-center">
            <Text className="mr-2 font-airbnb_bd text-xl font-bold text-text-600">
              {data?.conversations.length}
            </Text>
            {/*<View className="items-center">*/}
            {/*  <ProgressBar value={totalEnquiries} maxValue={maxEnquiries} />*/}
            {/*  <Text className="mt-1 font-airbnb_bk text-[8px] font-normal text-text-600">*/}
            {/*    Total Enquires*/}
            {/*  </Text>*/}
            {/*</View>*/}
          </View>
        </View>
      </View>

      <View className="bg-primary-0 px-5">
        {/* Heading */}
        <View className="flex-1 flex-row items-center justify-between py-3">
          <Text className="font-airbnb_bd text-base font-bold text-text-600">
            Recent Enquiries
          </Text>
          {/*<Pressable*/}
          {/*  onPress={() =>*/}
          {/*    router.push('/listing-screens/check-responses-enquiry')*/}
          {/*  }*/}
          {/*>*/}
          {/*  <Text className="font-airbnb_md text-xs font-medium text-secondary-main700">*/}
          {/*    View All Enquiries*/}
          {/*  </Text>*/}
          {/*</Pressable>*/}
        </View>

        {/* Card */}
        {/* FlashList for rendering multiple CheckResponseCard components */}
        <FlashList
          showsVerticalScrollIndicator={false}
          data={data?.conversations}
          keyExtractor={(item) => item.id.toString()}
          estimatedItemSize={200}
          className="mb-5"
          renderItem={({ item }) => {
            const isCurrentUser =
              profile?.id ===
              (item.messages.length ? item.messages[0]?.senderId : '');
            const oppositeSideUserDetails = isCurrentUser
              ? item.messages[0]?.senderId === profile?.id
                ? item.sender
                : item.receiver
              : !(item.messages[0]?.senderId === profile?.id)
                ? item.sender
                : item.receiver;

            const isMessagePresent = item.messages.length;
            return (
              <View className="mb-2.5 rounded-2xl bg-[#FFF9F5] p-3">
                {/* Top Part */}
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <UserPic
                      picUrl={
                        oppositeSideUserDetails.cloudinaryProfileImageUrl ??
                        oppositeSideUserDetails.filePublicUrl
                      }
                      size={54}
                      color="#784100"
                      className="h-10 w-10 rounded-full"
                    />
                    <View className="ml-1.5">
                      <Text className="font-airbnb_bd text-base font-bold text-text-550">
                        {oppositeSideUserDetails.name}
                      </Text>
                      <Text className="mt-0.5 font-airbnb_bk text-[11px] font-normal text-text-550">
                        {formatTime(item.createdAt)}
                      </Text>
                    </View>
                  </View>

                  {/*{isFocused ? (*/}
                  <Pressable
                    className=" self-start rounded-[4px] bg-secondary-100 p-1.5"
                    onPress={() =>
                      router.push('/listing-screens/property-enquiry')
                    }
                  >
                    <Text className="font-airbnb_md text-[11px] font-medium text-secondary-main700">
                      Check Response
                    </Text>
                  </Pressable>
                  {/*) : (*/}
                  {/*  <Pressable*/}
                  {/*    className=" self-start rounded-[4px] bg-secondary-main700 p-1.5"*/}
                  {/*    onPress={() =>*/}
                  {/*      router.push('/listing-screens/property-enquiry')*/}
                  {/*    }*/}
                  {/*  >*/}
                  {/*    <Text className="font-airbnb_md text-[11px] font-medium text-[#F4FFFD]">*/}
                  {/*      New Reponses*/}
                  {/*    </Text>*/}
                  {/*  </Pressable>*/}
                  {/*)}*/}
                </View>

                {/* Mid part */}
                <View className="flex-row items-center justify-between py-3.5">
                  <View className="flex-row items-center">
                    {/*<Pressable className="mr-1 self-start rounded-[4px] p-0.5">*/}
                    {/*  <NImage*/}
                    {/*    source={require('../../../../assets/icons/moneybag.png')}*/}
                    {/*    className="h-4 w-4"*/}
                    {/*  />*/}
                    {/*</Pressable>*/}
                    {/*<Text className="font-airbnb_md text-xs font-medium text-primary-750">*/}
                    {/*  {item.budget}*/}
                    {/*</Text>*/}
                  </View>

                  <View className="flex-row items-center">
                    <Pressable className="mr-1 self-start rounded-[4px] p-0.5">
                      <NImage
                        source={require('../../../../assets/icons/location2.png')}
                        className="h-3.5 w-3"
                        contentFit="contain"
                      />
                    </Pressable>
                    <Text className="font-airbnb_md text-xs font-medium text-primary-750">
                      {oppositeSideUserDetails.userLocation}
                    </Text>
                  </View>
                </View>

                {/* ControlledInput */}
                {/*{isFocused ? (*/}
                {/*  <View className="py-2 flex-row items-center">*/}
                {/*    <Text className="flex-1 font-airbnb_bk text-xs font-normal text-text-600">*/}
                {/*      {item.msg}*/}
                {/*    </Text>*/}
                {/*    <Pressable*/}
                {/*      className="self-end rounded-full bg-secondary-main700 p-1.5"*/}
                {/*      onPress={() => router.push('/chat-screens/chat-screen')}*/}
                {/*    >*/}
                {/*      <NImage*/}
                {/*        source={require('../../assets/icons/chats1.png')}*/}
                {/*        className="h-6 w-6"*/}
                {/*      />*/}
                {/*    </Pressable>*/}
                {/*  </View>*/}
                {/*) : (*/}
                {/*  <View*/}
                {/*    className={`w-full flex-row items-center justify-between rounded-2xl border ${isFocused ? 'border-secondary-main700' : 'border-secondary-200'} bg-white px-4`}*/}
                {/*  >*/}
                {/*    <TextInput*/}
                {/*      className="py-4 flex-1 items-center justify-center font-airbnb_bk text-xs font-normal text-text-600"*/}
                {/*      placeholder={'Reply'}*/}
                {/*      placeholderTextColor={'#252525'}*/}
                {/*      multiline={true}*/}
                {/*      onFocus={() => setIsFocused(true)}*/}
                {/*      onBlur={() => setIsFocused(false)}*/}
                {/*    />*/}

                {/*    <NImage*/}
                {/*      source={require('../../assets/icons/send.png')}*/}
                {/*      className="aspect-square w-[6%]"*/}
                {/*      contentFit="contain"*/}
                {/*      tintColor={'#F04D24'}*/}
                {/*    />*/}
                {/*  </View>*/}
                {/*}}*/}
              </View>
            );
          }} // Removed key prop here
        />
      </View>
    </ScrollView>
  );
};

export default CheckResponse;
