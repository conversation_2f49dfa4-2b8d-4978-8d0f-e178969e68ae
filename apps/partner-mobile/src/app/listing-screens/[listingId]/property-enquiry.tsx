import Slider from '@react-native-community/slider';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Pressable, ScrollView, Text, View } from 'react-native';

import { Button, ControlledInput } from '@/ui';

import CustomPicker from '../../../components/post-property/custom-picker';

interface OptionsProps {
  options: string[];
}

//Options Component
const Options: React.FC<OptionsProps> = ({ options }) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  return (
    <View className="flex-row flex-wrap items-center">
      {options.map((option) => (
        <Pressable
          key={option}
          onPress={() => setSelectedOption(option)}
          className={`mb-2 mr-3 self-start rounded-xl px-5 py-3 ${
            selectedOption === option ? 'bg-secondary-200' : 'bg-white'
          }`}
        >
          <Text
            className={`font-airbnb_bk text-[15px] font-normal ${
              selectedOption === option ? 'text-secondary-850' : 'text-text-600'
            }`}
          >
            {option}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

// eslint-disable-next-line max-lines-per-function
const PropertyEnquiry = () => {
  const {
    control,
    //  handleSubmit
  } = useForm();
  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      <View className="flex-1 px-5 py-6">
        <View className="mb-5">
          <CustomPicker
            control={control}
            items={[
              { label: 'Builder Floor', value: 'builder floor' },
              { label: 'Land', value: 'land' },
              { label: 'Plot', value: 'plot' },
              { label: 'Flat', value: 'flat' },
              { label: 'Apartment', value: 'apartment' },
              { label: 'Townhouse', value: 'townhouse' },
              { label: 'Shop', value: 'shop' },
              { label: 'Villa', value: 'villa' },
              { label: 'Office Area', value: 'office area' },
              { label: 'Commercial Space', value: 'commercial space' },
            ]}
            name={'Property Type'}
            label="Which type of property are you interested in?"
          />
        </View>

        <View className="mb-5">
          <Text className="mb-2 font-airbnb_md text-base font-medium text-text-600">
            Are you looking to _________ the property?
          </Text>
          <Options options={['Buy', 'Lease', 'Rent', 'Sell']} />
        </View>

        <View className="mb-5">
          <ControlledInput
            control={control}
            name="citytype"
            label="In which city or area are you interested?"
            placeholder="Enter"
          />
        </View>

        <View className="mb-5 flex-1 flex-col justify-between">
          <Text className=" font-airbnb_md text-base font-medium text-text-600">
            What is your budget range?
          </Text>
          <View className="mt-3 flex-1 items-center justify-center ">
            <Slider
              style={{ width: '100%', height: 8 }}
              minimumValue={0}
              maximumValue={1}
              minimumTrackTintColor="#F04D24"
              maximumTrackTintColor="#ECECEC"
              //    thumbTintColor="#12725B"
            />
          </View>
          <View className="flex-row items-center justify-between">
            <Text className="font-airbnb_bk text-sm text-text-600">
              INR 10,000
            </Text>
            <Text className="font-airbnb_bk text-sm text-text-600">
              INR 10,00000
            </Text>
          </View>
        </View>

        <View className="mb-5">
          <Text className="mb-2 font-airbnb_md text-base font-medium text-text-600 ">
            Are you looking to _________ the property?
          </Text>
          <Options
            options={[
              'Immediate',
              'Within 3 months',
              'Under 1 year',
              'Below 3 years',
            ]}
          />
        </View>

        <View className="mb-5">
          <ControlledInput
            control={control}
            name="additionalrequirement"
            label="In which city or area are you interested?"
            placeholder="Enter"
            multiline={true}
          />
        </View>

        <Button label="Submit" />
      </View>
    </ScrollView>
  );
};

export default PropertyEnquiry;
