import { FlashList } from '@shopify/flash-list';
import { Image as NImage, ImageBackground } from 'expo-image';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Pressable,
  Text,
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Animated,
} from 'react-native';
import { api } from '@/utils/api';
import PropertyShareBtn from '@/components/property/share-btn';
import { Button } from '@/ui';

const TabsList = [
  {
    name: 'All',
    key: 'All',
  },
  {
    name: 'Active',
    key: 'Active',
  },
  {
    name: 'Under Review',
    key: 'Under Review',
  },
  {
    name: 'Rejected',
    key: 'Rejected',
  },
  {
    name: 'Draft',
    key: 'Draft',
  },
  {
    name: 'Deleted',
    key: 'Deleted',
  },
  {
    name: 'Sold',
    key: 'Sold',
  },
  // {
  //   name: "All Time",
  //   key: "all-time",
  // },
] as const;

type IFilterType = (typeof TabsList)[number]['key'];

const MyListings: React.FC = () => {
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState<IFilterType>('All');
  const [buttonMeasurements, setButtonMeasurements] = useState<
    Record<string, { x: number; width: number }>
  >({});
  const [isReady, setIsReady] = useState(false);

  const {
    data: listings,
    isLoading: isLoadingGetAllProperties,
    refetch,
  } = api.postProperty.getFilteredProperties.useQuery(
    {
      filter: selectedFilter,
      q: '',
    },
    {
      enabled: !!selectedFilter,
      // The 'staleTime' option is used to specify the duration (in milliseconds)
      // for which the data is considered fresh. After this time, the data is
      // considered stale and will be refetched when a new query is made.
      // Here, the stale time is set to 5000 milliseconds (or 5 seconds).
      staleTime: () => {
        return 5000;
      },
    },
  );

  const sliderPosition = useRef(new Animated.Value(0)).current;
  const sliderWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (
      !isReady &&
      Object.keys(buttonMeasurements).length === TabsList.length
    ) {
      const firstButton = buttonMeasurements['All'];
      if (firstButton) {
        sliderPosition.setValue(firstButton.x);
        sliderWidth.setValue(firstButton.width);
        setIsReady(true);
      }
    }
  }, [buttonMeasurements, isReady]);

  const handleTimeSelect = (filter: IFilterType) => {
    setSelectedFilter(filter);
    const button = buttonMeasurements[filter];
    if (!button) return;

    Animated.parallel([
      Animated.spring(sliderPosition, {
        toValue: button.x,
        useNativeDriver: false,
        tension: 50,
        friction: 7,
      }),
      Animated.spring(sliderWidth, {
        toValue: button.width,
        useNativeDriver: false,
        tension: 50,
        friction: 7,
      }),
    ]).start();
  };

  return (
    <View className="flex-1 ">
      <ScrollView
        className="flex-1 "
        refreshControl={
          <RefreshControl
            refreshing={isLoadingGetAllProperties}
            onRefresh={refetch}
          />
        }
      >
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 0 }}
          className="w-full my-2.5"
        >
          <View className="px-5 flex-row gap-2.5 items-center justify-between relative">
            {TabsList.map((item) => (
              <Pressable
                key={item.key}
                onPress={() => handleTimeSelect(item.key)}
                onLayout={({
                  nativeEvent: {
                    layout: { x, width },
                  },
                }) => {
                  setButtonMeasurements((prev) => ({
                    ...prev,
                    [item.key]: { x, width },
                  }));
                }}
                className={`px-4 py-2 rounded-lg justify-center items-center z-10 ${
                  selectedFilter === item.key
                    ? 'bg-primary-100'
                    : 'border border-text-100'
                }`}
              >
                <Text
                  className={`font-normal text-xs font-airbnb_bk ${
                    selectedFilter === item.key
                      ? 'text-secondary-850'
                      : 'text-text-600'
                  }`}
                >
                  {item.name}
                </Text>
              </Pressable>
            ))}

            <Animated.View
              style={[
                styles.slider,
                {
                  transform: [{ translateX: sliderPosition }],
                  width: sliderWidth,
                  opacity: isReady ? 1 : 0,
                },
              ]}
            />
          </View>
        </ScrollView>
        <FlashList
          showsVerticalScrollIndicator={false}
          data={listings}
          estimatedItemSize={210}
          ListEmptyComponent={
            <View className="flex-1 justify-center items-center">
              <Text className="mt-10">No property listed</Text>
              <Button
                variant="step"
                onPress={() => router.push('/upload-post/post-property')}
              >
                <Text className="text-white">Post Property</Text>
              </Button>
            </View>
          }
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => {
            const mediaSections = item?.mediaSections;
            const urls =
              mediaSections?.flatMap((section) =>
                section.media?.map(
                  (media) => media.cloudinaryUrl ?? media.filePublicUrl,
                ),
              ) || [];
            return (
              <View>
                <Pressable
                  className="mx-5 flex-1 border-b border-text-50"
                  onPress={() => router.push(`/property/${item.id}`)}
                >
                  <View className="my-3 flex-row items-center">
                    <ImageBackground
                      source={urls[0] ? { uri: urls[0] } : ''}
                      contentFit="contain"
                      style={{
                        aspectRatio: 3 / 4,
                        width: '28.5%',
                        padding: 6,
                        borderRadius: 10,
                        overflow: 'hidden',
                        backgroundColor: urls[0] ? 'transparent' : '#898e8c',
                      }}
                    >
                      <View className="self-start rounded-md bg-primary-0 px-2 py-1.5">
                        <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700">
                          {item.propertyFor === 'SALE'
                            ? 'For Sale'
                            : 'For Rent'}
                        </Text>
                      </View>
                    </ImageBackground>

                    <View className="w-[72%] pl-3 ">
                      <View className="flex-row items-center justify-between">
                        {item.amenities.map((label) => (
                          <View key={label.id} className="mx-2.5">
                            <Text className="font-airbnb_md text-[8px] font-medium text-primary-700">
                              {label.name}
                            </Text>
                          </View>
                        ))}
                      </View>

                      <View className="pt-2">
                        <Text className="font-airbnb_bd text-base font-bold text-text-main700">
                          {item.propertyTitle}
                        </Text>
                        {item.propertyAddress && (
                          <View className="mt-0.5 flex-row items-center">
                            <NImage
                              source={require('../../../assets/icons/location2.png')}
                              contentFit="contain"
                              className="w-2.5 h-2.5"
                            />
                            <Text className="ml-1 font-airbnb_bk text-[10px] font-normal text-text-600">
                              {item.propertyAddress}
                            </Text>
                          </View>
                        )}
                        <Text
                          className="mt-2 self-start font-airbnb_bk text-[10px] font-normal text-text-600"
                          numberOfLines={5}
                          ellipsizeMode="tail"
                        >
                          {item.aboutProperty}
                        </Text>
                      </View>

                      <View className="mt-2 flex-row items-center justify-around">
                        <Pressable
                          className="self-start rounded-lg border border-secondary-main700 px-5 py-2.5"
                          onPress={() =>
                            router.push({
                              pathname:
                                '/listing-screens/[listingId]/check-response',
                              params: { listingId: item.id },
                            })
                          }
                        >
                          <Text className="text-secondary-main700">
                            Check Responses
                          </Text>
                        </Pressable>

                        <Pressable
                          onPress={() => {
                            const serializedItem = JSON.stringify(item); // Serialize the item
                            router.push(
                              `/upload-post/post-property?propertyId=${item.id}`,
                            );
                          }}
                        >
                          <NImage
                            source={require('../../../assets/icons/edit.png')}
                            className="h-6 w-6"
                          />
                        </Pressable>
                        {/* <Pressable>
                        <NImage
                          source={require('../../../assets/icons/sharelistings.png')}
                          className="h-6 w-6"
                        />
                      </Pressable> */}
                        <PropertyShareBtn
                          propertyId={item.id}
                          propertyTitle={item.propertyTitle ?? ''}
                          aboutProperty={item.aboutProperty ?? ''}
                        />
                      </View>
                    </View>
                  </View>
                </Pressable>
              </View>
            );
          }}
        />
      </ScrollView>
    </View>
  );
};

export default MyListings;

const styles = StyleSheet.create({
  slider: {
    position: 'absolute',
    height: 36,
    backgroundColor: '#fef9ea',
    borderRadius: 8,
    zIndex: 1,
  },
  gradientBox: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    marginRight: 12,
    height: 124,
    // width: '44%',
    width: '46%',
    // width: 'auto',
    // flex: 1,
  },
});
