// import {
//   Pressable,
//   View,
//   Image,
//   Dimensions,
//   Text,
//   StyleSheet,
//   ImageSourcePropType,
//   ActivityIndicator,
//   Platform,
//   RefreshControl,
// } from 'react-native';
// import React, { useState, useRef } from 'react';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';
// import { ImageBackground } from 'expo-image';
// import Entypo from '@expo/vector-icons/Entypo';
// import { router, useLocalSearchParams } from 'expo-router';
// import { Button } from '@/components/ui';
// import { ScrollView } from 'react-native-gesture-handler';
// import { api } from '@/utils/api';
// import { formatDistanceToNow } from 'date-fns';
// import { BlurView } from 'expo-blur';
// import AgentInfoCard from '@/components/shared/agent-info-card';
// import AgentWishlistBtn from '@/components/agent/wishlist-agent-btn';
// import AgentShareBtn from '@/components/agent/share-agent-btn';
// import { Video, ResizeMode } from 'expo-av';
// import PropertiesComponent from '@/components/routes/properties-component/properties-component';
// import Post from '@/components/shared/post';
// import { SwitchAddAgent } from '@/components/shared/switch-add-agent';
// import { AgentProps } from '@/types';

// const { height, width } = Dimensions.get('screen');

// const Stats = ({
//   value,
//   label,
//   img,
// }: {
//   value: string | number;
//   label: string;
//   img: ImageSourcePropType;
// }) => {
//   return (
//     <View className=" items-center">
//       <Text className="text-lg font-semi-bold font-airbnb_bd text-primary-700">
//         {value}
//       </Text>
//       <View className="mt-1.5 flex-row items-center">
//         <Image source={img} className="h-5 w-5" />
//         <Text className="ml-1 text-sm font-airbnb_bk font-normal text-text-500">
//           {label}
//         </Text>
//       </View>
//     </View>
//   );
// };

// const AgentProfileDetails = () => {
//   const insets = useSafeAreaInsets();
//   const [selected, setSelected] = useState<'post' | 'property'>('post');
//   const scrollViewRef = useRef<ScrollView>(null);
//   const scrollToRef = useRef<View>(null);
//   const postSectionRef = useRef<View>(null);
//   const handleListingPress = () => {
//     setSelected('property');
//     if (scrollToRef.current && scrollViewRef.current) {
//       scrollToRef.current.measure((x, y, width, height, pageX, pageY) => {
//         scrollViewRef.current?.scrollTo({ y: pageY, animated: true });
//       });
//     }
//   };
//   const handlePostPress = () => {
//     setSelected('post');
//     if (postSectionRef.current && scrollViewRef.current) {
//       postSectionRef.current.measure((x, y, width, height, pageX, pageY) => {
//         scrollViewRef.current?.scrollTo({ y: pageY, animated: true });
//       });
//     }
//   };
//   // utils for invalidating the queries
//   const utils = api.useUtils();

//   const videoRef = useRef(null);

//   const { agentId } = useLocalSearchParams<{
//     agentId: string;
//   }>();

//   const { data: agentReviews } = api.agentReviews.getAgentReviews.useQuery({
//     agentId: agentId,
//   });

//   const {
//     data: agentData,
//     isLoading: isLoadingAgentData,
//     isError: isErrorAgentData,
//     refetch,
//   } = api.agent.getAgentDetails.useQuery({
//     id: agentId,
//   });

//   const operationalAreas = React.useMemo(() => {
//     try {
//       if (!agentData?.operationArea) return [];

//       if (Array.isArray(agentData.operationArea)) {
//         return agentData.operationArea;
//       }

//       if (typeof agentData.operationArea === 'string') {
//         return JSON.parse(agentData.operationArea);
//       }

//       return [];
//     } catch (error) {
//       console.error('Error parsing operational areas:', error);
//       return [];
//     }
//   }, [agentData?.operationArea]);

//   const CompanyDetails = [
//     {
//       title: 'Website',
//       icon: require('@assets/icons/web.png'),
//       value: `${agentData?.company?.companyWebsiteLink || ''}`,
//     },
//     {
//       title: 'Location',
//       icon: require('@assets/icons/location.png'),
//       value: `${agentData?.userLocation || ''}`,
//     },
//     {
//       title: 'Mobile',
//       value: `${agentData?.company?.phoneNumber || ''}`,
//     },
//     {
//       title: 'Email',
//       value: `${agentData?.company?.email || ''}`,
//     },
//     {
//       title: 'Fax',
//       value: `${agentData?.company?.fax || ''}`,
//     },
//   ];

//   // checking if the user connected by the agent who listed this property
//   const customerDetail = api.user.getProfile.useQuery();
//   const connectedAgentId = customerDetail.data?.connections[0]?.agentId ?? '';
//   const isConnectedAgent = agentId === connectedAgentId;

//   // getting the connection id of with the connected agent
//   const { data: connectedAgentData } = api.agent.getProfileById.useQuery({
//     agentId: connectedAgentId,
//   });

//   // switch add agent
//   const [isSwitchAddAgentVisible, setIsSwitchAddAgentVisible] = useState(false);

//   const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

//   if (isLoadingAgentData) {
//     return (
//       <View className="flex-1 items-center justify-center">
//         <ActivityIndicator
//           className="text-center mt-5"
//           color="#F04D24"
//           size="large"
//         />
//       </View>
//     );
//   }

//   if (isErrorAgentData) {
//     return (
//       <View className="flex-1 items-center justify-center">
//         <Text className="text-center mt-5">Error</Text>
//       </View>
//     );
//   }

//   if (!agentData) {
//     return (
//       <View className="flex-1 items-center justify-center">
//         <Text className="text-center mt-5">No data</Text>
//       </View>
//     );
//   }

//   return (
//     <View className="flex-1">
//       <ScrollView
//         ref={scrollViewRef}
//         showsVerticalScrollIndicator={false}
//         className="mb-5"
//         contentContainerStyle={{ paddingBottom: 100 }}
//         refreshControl={
//           <RefreshControl refreshing={isLoadingAgentData} onRefresh={refetch} />
//         }
//       >
//         <View style={{ paddingTop: insets.top }} className="px-5 relative">
//           {/* Agent Profile */}
//           <View
//             className="relative rounded-xl overflow-hidden bg-white"
//             style={[
//               styles.Shadow,
//               { borderBottomLeftRadius: 12, borderBottomRightRadius: 12 },
//             ]}
//           >
//             <ImageBackground
//               className="items-center"
//               key={agentData?.bgFileKey}
//               source={
//                 agentData?.bgFilePublicUrl
//                   ? { uri: agentData?.bgFilePublicUrl }
//                   : require('@assets/images/propertyyo.png')
//               }
//               style={{
//                 height: height * 0.1931,
//                 width: width * 0.907,
//                 padding: 20,
//               }}
//               imageStyle={{ borderTopLeftRadius: 12, borderTopRightRadius: 12 }}
//               contentFit="cover"
//               onLoadStart={() => {
//                 if (agentData?.bgFilePublicUrl) {
//                   setIsLoading((prev) => ({ ...prev, bgImage: true }));
//                 }
//               }}
//               onLoadEnd={() =>
//                 setIsLoading((prev) => ({ ...prev, bgImage: false }))
//               }
//               onError={() => {
//                 setIsLoading((prev) => ({ ...prev, bgImage: false }));
//               }}
//             >
//               <View className="flex-row items-center justify-between">
//                 <Pressable
//                   className="bg-primary-100 rounded-full self-start p-2"
//                   onPress={() => router.back()}
//                 >
//                   <Entypo name="chevron-thin-left" size={16} color="#252525" />
//                 </Pressable>
//                 <Pressable className="bg-primary-100 rounded-md self-start">
//                   <Image
//                     source={require('@assets/icons/dot1.png')}
//                     className="h-8 w-8"
//                   />
//                 </Pressable>
//               </View>

//               <View className="absolute bottom-[-24px] left-6">
//                 <View className="bg-white rounded-full p-1">
//                   <Image
//                     key={agentData?.fileKey}
//                     source={
//                       agentData?.filePublicUrl
//                         ? { uri: agentData?.filePublicUrl }
//                         : require('@assets/icons/default-user.png')
//                     }
//                     className="h-24 w-24 rounded-full"
//                     onLoadStart={() => {
//                       if (agentData?.filePublicUrl) {
//                         setIsLoading((prev) => ({
//                           ...prev,
//                           profileImage: true,
//                         }));
//                       }
//                     }}
//                     onLoadEnd={() =>
//                       setIsLoading((prev) => ({ ...prev, profileImage: false }))
//                     }
//                     onError={() => {
//                       setIsLoading((prev) => ({
//                         ...prev,
//                         profileImage: false,
//                       }));
//                     }}
//                   />
//                 </View>
//               </View>
//               {(isLoading.bgImage || isLoading.profileImage) && (
//                 <View className="absolute top-0 left-0 right-0 bottom-0 items-center justify-center bg-black/30">
//                   <ActivityIndicator size="large" color="#F04D24" />
//                 </View>
//               )}
//             </ImageBackground>

//             <View
//               className="px-4 pb-4 bg-white border-b-[1.5px]  border-secondary-100"
//               style={{
//                 borderBottomLeftRadius: 12,
//                 borderBottomRightRadius: 12,
//                 marginTop: 24,
//               }}
//             >
//               {/* details */}
//               <View>
//                 <View className="flex-row items-center justify-between">
//                   <View style={{ maxWidth: '60%' }}>
//                     <View>
//                       <Text
//                         numberOfLines={1}
//                         ellipsizeMode="tail"
//                         className="font-airbnb_xbd text-xl font-extrabold text-secondary-800"
//                       >
//                         {agentData?.name}
//                       </Text>
//                       <Text
//                         numberOfLines={2}
//                         ellipsizeMode="tail"
//                         className="mt-1 font-airbnb_md text-sm font-medium text-text-600"
//                       >
//                         {agentData?.company?.companyName}
//                       </Text>
//                     </View>
//                   </View>

//                   {/* rating & review part */}
//                   <View style={{ maxWidth: '40%' }} className="ml-auto">
//                     <View className="mb-2 flex-row items-center self-end">
//                       <Image
//                         source={require('../../../assets/icons/star.png')}
//                         className="h-4 w-4"
//                       />
//                       <Text
//                         className="ml-0.5 font-airbnb_md text-sm font-medium text-text-600"
//                         numberOfLines={1}
//                         ellipsizeMode="tail"
//                       >
//                         {agentData?.rating ?? 'N/A'}
//                       </Text>
//                     </View>

//                     <View className="mt-2">
//                       <Text
//                         className="font-airbnb_md text-xs font-medium text-text-600"
//                         numberOfLines={1}
//                         ellipsizeMode="tail"
//                       >
//                         {agentData?.customerRatingsToAgents?.length ?? 0}{' '}
//                         Reviews
//                       </Text>
//                     </View>
//                   </View>
//                 </View>

//                 <View className="mt-2">
//                   <Text className="text-sm font-normal font-airbnb_bk text-text-500">
//                     {agentData?.userLocation ?? 'N/A Location'}
//                   </Text>
//                 </View>
//               </View>

//               {/* Stats */}
//               <View className="mt-5 space-y-5">
//                 <View className="mb-2.5 flex-row items-center justify-between px-4">
//                   <Stats
//                     value={agentData?.propertiesSold ?? 'N/A'}
//                     label="Total sales closed"
//                     img={require('@assets/icons/homeicon.png')}
//                   />
//                   <Stats
//                     value={`${agentData?.experience ?? '0'} yrs`}
//                     label="Experience"
//                     img={require('@assets/icons/experience3.png')}
//                   />
//                 </View>
//                 <View className="mt-2.5 flex-row items-center justify-between px-4">
//                   <Stats
//                     value={
//                       agentData?.createdAt
//                         ? formatDistanceToNow(new Date(agentData.createdAt)) +
//                           ' ago'
//                         : 'N/A'
//                     }
//                     label="Active"
//                     img={require('@assets/icons/clock.png')}
//                   />
//                   <Stats
//                     value={
//                       agentData?.coustomerConnections
//                         ? `${agentData.coustomerConnections.length + agentData.sentConnectionRequests.length + agentData.receivedConnectionRequests.length}`
//                         : 'N/A Connections'
//                     }
//                     label="Connections"
//                     img={require('@assets/icons/handshake.png')}
//                   />
//                 </View>
//               </View>

//               {/* Buttons */}
//               <View className="mt-5 space-y-3">
//                 <View className="items-center flex-row justify-between gap-3">
//                   <Button
//                     label={isConnectedAgent ? 'Connected' : 'Contact Agent'}
//                     className="w-[45%]"
//                     onPress={() => setIsSwitchAddAgentVisible(true)}
//                     disabled={isConnectedAgent || isSwitchAddAgentVisible}
//                     loading={isSwitchAddAgentVisible}
//                   />

//                   <AgentWishlistBtn agentId={agentId} key={agentId} />
//                 </View>

//                 <View className="flex-row">
//                   <AgentShareBtn
//                     agentId={agentId}
//                     agentName={agentData?.name}
//                     aboutAgent={agentData?.company?.companyName ?? ''}
//                   />
//                 </View>
//               </View>
//             </View>
//           </View>

//           <View
//             className="mt-3 py-4 border-[1.5px] rounded-xl border-secondary-100 bg-white"
//             style={styles.Shadow}
//           >
//             <Text
//               numberOfLines={1}
//               ellipsizeMode="tail"
//               className="px-4 font-airbnb_bd text-lg font-extrabold text-secondary-750"
//             >
//               Activity
//             </Text>
//             <Text className="my-3 h-0.5 bg-[#E0DFDC] rounded-full" />

//             {/* switchable button */}
//             <View className="mt-5 w-3/5 px-4 flex-row items-center rounded-lg">
//               <Pressable
//                 className={`flex-1 items-center self-start rounded-l-lg px-2.5 py-2 ${selected === 'post' ? 'bg-primary-500' : 'bg-[#f1f1f1]'}`}
//                 onPress={handlePostPress}
//               >
//                 <Text
//                   className={`font-airbnb_bk text-sm font-normal ${selected === 'post' ? 'text-primary-800' : 'text-text-500'}`}
//                 >
//                   Post
//                 </Text>
//               </Pressable>

//               <Pressable
//                 className={`flex-1 items-center self-start rounded-r-lg px-2.5 py-2 ${selected === 'property' ? 'bg-primary-500' : 'bg-[#f1f1f1]'}`}
//                 onPress={handleListingPress}
//               >
//                 <Text
//                   className={`font-airbnb_bk text-sm font-normal ${selected === 'property' ? 'text-primary-800' : 'text-text-500'}`}
//                 >
//                   Property
//                 </Text>
//               </Pressable>
//             </View>

//             {/* posts */}
//             <ScrollView
//               horizontal
//               showsHorizontalScrollIndicator={false}
//               contentContainerStyle={{ paddingHorizontal: 16 }}
//             >
//               {selected === 'post' && (
//                 <View ref={scrollToRef} className="flex-row">
//                   {agentData?.posts.map((item) => (
//                     <View
//                       key={item.id}
//                       style={{
//                         width: width * 0.8,
//                         marginRight: 10,
//                         marginTop: 10,
//                       }}
//                     >
//                       <Post post={item} />
//                     </View>
//                   ))}
//                 </View>
//               )}
//             </ScrollView>

//             {/* properties */}
//             <ScrollView
//               horizontal
//               showsHorizontalScrollIndicator={false}
//               contentContainerStyle={{ paddingHorizontal: 16 }}
//             >
//               {selected === 'property' && (
//                 <View ref={scrollToRef} className="flex-row">
//                   {agentData?.properties.map((item) => (
//                     <View
//                       key={item.id}
//                       style={{ width: width * 0.8, marginRight: 10 }}
//                     >
//                       <PropertiesComponent
//                         item={item}
//                         handlePress={() =>
//                           router.push(`/property-routes/${item.id}`)
//                         }
//                         isLiked={!!item.customerFavourites.length}
//                       />
//                     </View>
//                   ))}
//                 </View>
//               )}
//             </ScrollView>
//           </View>

//           <View className="mt-5 ">
//             <AgentInfoCard<false>
//               heading={'Bio'}
//               description={agentData?.bio ?? 'N/A'}
//             />
//           </View>
//           <View className="mt-5 ">
//             <AgentInfoCard<true>
//               heading={'About Company'}
//               description={agentData?.company?.about ?? 'N/A'}
//               companyDetails={CompanyDetails}
//             />
//           </View>

//           <View className="mt-5 p-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
//             <Text className="mb-3 text-lg font-airbnb_bd font-bold text-primary-750">
//               Operational Areas
//             </Text>
//             <Text className="mb-3 text-base font-medium font-airbnb_md text-text-500">
//               {agentData?.company?.companyLocation}
//             </Text>

//             {operationalAreas.length > 0 ? (
//               <View className="flex-row flex-wrap gap-2">
//                 {operationalAreas.map(
//                   (area: { name: string; id: string }, index: number) => (
//                     <View
//                       key={index}
//                       className="mb-2 self-start bg-primary-100 rounded-md py-1 px-3 items-center"
//                     >
//                       <Text className="text-base font-medium font-airbnb_md text-primary-750">
//                         {area.name}
//                       </Text>
//                     </View>
//                   ),
//                 )}
//               </View>
//             ) : (
//               <Text className="mb-3 text-base font-medium font-airbnb_md text-text-500">
//                 N/A
//               </Text>
//             )}
//           </View>

//           <View className="mt-5 p-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
//             <Text className="mb-3 text-lg font-airbnb_bd font-bold text-primary-750">
//               Language
//             </Text>

//             {agentData?.languages && agentData?.languages.length > 0 ? (
//               <View className="flex-row flex-wrap gap-2">
//                 {agentData.languages.map(
//                   (language: { name: string; id: string }, index: number) => (
//                     <View
//                       key={index}
//                       className="mb-2 self-start bg-primary-100 rounded-md py-1 px-3 items-center"
//                     >
//                       <Text className="text-base font-medium font-airbnb_md text-primary-750">
//                         {language.name}
//                       </Text>
//                     </View>
//                   ),
//                 )}
//               </View>
//             ) : (
//               <Text className="mb-3 text-base font-medium font-airbnb_md text-text-500">
//                 N/A
//               </Text>
//             )}
//           </View>

//           {/* testimonials */}
//           <View className="mt-5 py-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
//             <Text className="mb-3 px-4 text-lg font-airbnb_bd font-bold text-primary-750">
//               Users Testimonials
//             </Text>

//             <ScrollView
//               horizontal
//               showsHorizontalScrollIndicator={false}
//               contentContainerStyle={{ paddingHorizontal: 16 }}
//             >
//               {agentReviews?.map((review, index) => (
//                 <Pressable
//                   key={index}
//                   className="mr-4 rounded-xl overflow-hidden aspect-[9/16] object-cover"
//                   style={{ height: 460, width: 316 }}
//                 >
//                   <View className="h-full w-full bg-primary-100 rounded-xl shadow-md relative object-cover">
//                     {/* Video Player yo */}
//                     <View className="justify-center items-center relative">
//                       {review.filePublicUrl ? (
//                         <>
//                           <Video
//                             ref={videoRef}
//                             source={{ uri: review.filePublicUrl }}
//                             useNativeControls
//                             resizeMode={ResizeMode.COVER}
//                             isLooping={false}
//                             style={{ height: '100%', width: '100%' }}
//                             shouldPlay={true}
//                             onLoadStart={() =>
//                               setIsLoading((prev) => ({
//                                 ...prev,
//                                 [review.id]: true,
//                               }))
//                             }
//                             onLoad={() =>
//                               setIsLoading((prev) => ({
//                                 ...prev,
//                                 [review.id]: false,
//                               }))
//                             }
//                           />
//                           {isLoading[review.id] && (
//                             <View className="absolute top-0 left-0 right-0 bottom-0 items-center justify-center bg-black/30">
//                               <ActivityIndicator size="large" color="#F04D24" />
//                             </View>
//                           )}
//                         </>
//                       ) : (
//                         <Text className="text-center text-text-500 font-airbnb_md text-base font-medium">
//                           Not Available now
//                         </Text>
//                       )}
//                     </View>

//                     {/* user info yo */}
//                     <View className="p-4 bg-transparent absolute bottom-0 left-0 right-0">
//                       <View className="flex-row items-center">
//                         <Image
//                           key={review?.ratedBy?.profileImageKey}
//                           source={
//                             review?.ratedBy?.profileImagePublicUrl
//                               ? { uri: review.ratedBy.profileImagePublicUrl }
//                               : require('@assets/icons/default-user.png')
//                           }
//                           className="h-10 w-10 rounded-full"
//                         />
//                         <View className="ml-2">
//                           <Text className="font-airbnb_bd text-base font-bold text-white">
//                             {review?.ratedBy?.name || 'N/A Customer Name'}
//                           </Text>
//                           <View className="flex-row items-center">
//                             <Image
//                               source={require('@assets/icons/star.png')}
//                               className="h-3 w-3 mr-1"
//                             />
//                             <Text className="font-airbnb_md text-xs text-text-100">
//                               {review?.userStarsCount || 'N/A'}
//                             </Text>
//                             <Text
//                               className="max-w-xs ml-2 font-airbnb_md font-medium text-sm text-text-100"
//                               numberOfLines={1}
//                               ellipsizeMode="tail"
//                             >
//                               {review?.connection?.customer?.city?.name ||
//                                 'N/A city'}
//                             </Text>
//                           </View>
//                         </View>
//                       </View>
//                       <Text
//                         className="mt-2 self-start font-airbnb_md text-center text-text-100"
//                         numberOfLines={1}
//                         ellipsizeMode="tail"
//                       >
//                         {review?.userRatingMessage || ''}
//                       </Text>
//                     </View>
//                   </View>
//                 </Pressable>
//               ))}
//             </ScrollView>
//           </View>
//         </View>
//       </ScrollView>

//       {/* Bottom Button */}
//       <View className="absolute bottom-5 w-full bg-primary-50 px-5 py-3">
//         {Platform.OS === 'ios' ? (
//           <BlurView
//             intensity={30}
//             tint="light"
//             blurReductionFactor={4}
//             experimentalBlurMethod="dimezisBlurView"
//           >
//             <Button
//               label={isConnectedAgent ? 'Connected' : 'Contact Agent'}
//               disabled={isConnectedAgent || isSwitchAddAgentVisible}
//               loading={isSwitchAddAgentVisible}
//               onPress={() => setIsSwitchAddAgentVisible(true)}
//             />
//           </BlurView>
//         ) : (
//           <Button
//             label={isConnectedAgent ? 'Connected' : 'Contact Agent'}
//             disabled={isConnectedAgent || isSwitchAddAgentVisible}
//             loading={isSwitchAddAgentVisible}
//             onPress={() => setIsSwitchAddAgentVisible(true)}
//           />
//         )}
//       </View>
//       <SwitchAddAgent
//         isVisible={isSwitchAddAgentVisible}
//         currentAgent={connectedAgentData?.agent as AgentProps}
//         newAgent={agentData as unknown as AgentProps}
//         onClose={() => setIsSwitchAddAgentVisible(false)}
//         onSuccess={() => {
//           utils.invalidate();
//         }}
//       />
//     </View>
//   );
// };

// export default AgentProfileDetails;

// const styles = StyleSheet.create({
//   Shadow: {
//     backgroundColor: '#fff',
//     shadowColor: '#000',
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,

//     elevation: 4,
//   },
// });
