// import { Stack } from 'expo-router';
// import React from 'react';

// // import { StatusBar } from 'react-native';

// export default function Layout() {
//   return (
//     <Stack>
//       <Stack.Screen
//         name="[agentId]"
//         options={{
//           headerShown: false,
//         }}
//       />
//       <Stack.Screen
//         name="new"
//         options={{
//           headerShown: false,
//         }}
//       />
//     </Stack>
//   );
// }
