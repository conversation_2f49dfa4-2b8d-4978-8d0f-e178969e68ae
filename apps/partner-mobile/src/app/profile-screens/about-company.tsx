import { useEffect, useRef, useState } from 'react';
import {
  Alert,
  Platform,
  Text,
  View,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import { Image as NImage } from 'expo-image';
import { useNavigation } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { ScrollView } from 'react-native-gesture-handler';
import * as ImagePicker from 'expo-image-picker';
import { Button } from '@/ui'; // Replace with your UI components
import { Modal, useModal } from '@/ui/modal';
import Entypo from '@expo/vector-icons/Entypo';
import { BlurView } from 'expo-blur';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FieldErrors } from 'react-hook-form';
import * as z from 'zod';
import PhotoOptionsModal from './photo-options-modal';
import EditCompanyProfile from '@/components/edit-company-profile';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { api } from '@/utils/api';
import { companyDetailsSchema } from '@/utils/form-validators';
import { showMessage } from 'react-native-flash-message';
import AntDesign from '@expo/vector-icons/AntDesign';
import { router } from 'expo-router';
import { useActionSheet } from '@expo/react-native-action-sheet';
import AgentCard from '@/components/agent-card';
import { signedUploadToCloudinary } from '@/api/cloudinary/upload';

interface CloudinaryImageData {
  url: string;
  publicId: string;
}

// eslint-disable-next-line max-lines-per-function
const AboutCompany = React.memo(() => {
  const [modalVisible, setModalVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const utils = api.useUtils();
  const createCompany = api.company.createCompany.useMutation();
  const [cloudinaryImage, setCloudinaryImage] = useState<CloudinaryImageData>({
    url: '',
    publicId: '',
  });
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { present, dismiss, ref } = useModal();

  // Create a stable timestamp for the signature query
  const timestampRef = useRef(Math.round(Date.now() / 1000));

  // Fetch signature for Cloudinary upload
  const { data: signatureData } = api.cloudinary.generateSignature.useQuery({
    paramsToSign: {
      timestamp: timestampRef.current,
      folderFor: 'users',
      forlderPurpose: 'company',
    },
  });

  const updateCompanyLogoMutation = api.user.updateCompanyLogo.useMutation({
    onSuccess: ({ message }) => {
      showMessage({
        message,
        type: 'success',
      });
      void utils.invalidate();
    },
    onError: ({ message }) => {
      showMessage({
        message: message,
        type: 'danger',
      });
    },
  });

  const { data: company } = api.company.getCompany.useQuery();

  const {
    data: AddedAgents,
    isLoading: isLoadingAddedAgents,
    isError: isErrorAddedAgents,
  } = api.company.getCompanyAgents.useQuery();

  const { control, setValue, reset, handleSubmit } = useForm<
    z.infer<typeof companyDetailsSchema>
  >({
    resolver: zodResolver(companyDetailsSchema),
  });

  // Update cloudinaryImage when company data is loaded
  useEffect(() => {
    if (company) {
      reset({
        name: company.companyName,
        email: company.email,
        phoneNumber: company.phoneNumber,
        latitude: company.companyLatitude,
        longitude: company.companyLongitude,
        address: company.companyLocation,
        website: company.companyWebsiteLink ?? undefined,
        fax: company.fax ?? undefined,
        about: company.about,
      });

      // Set cloudinary image data if available
      if (company.cloudinaryCompanyLogoUrl) {
        setCloudinaryImage({
          url: company.cloudinaryCompanyLogoUrl,
          publicId: company.cloudinaryCompanyLogoPublicId || '',
        });
      }
    }
  }, [company, reset]);

  const uploadCompanyImage = async (filePath: string) => {
    if (!company?.id) {
      showMessage({
        message:
          'Company is not created. Please create the company from the form below',
        type: 'danger',
      });
      return;
    }

    try {
      setIsUploading(true);
      setModalVisible(false);

      if (!signatureData || !signatureData.signature) {
        throw new Error('Signature not available');
      }

      // Upload the new image to Cloudinary
      const result = await signedUploadToCloudinary({
        imageUri: filePath,
        folder: signatureData.uploadFolderUrl,
        signature: signatureData.signature,
        timestamp: timestampRef.current.toString(),
        preset: signatureData.cloudPreset,
        apiKey: signatureData.apiKey,
        cloudName: signatureData.cloudName,
      });

      // Update state with new image data
      const newImageData = {
        url: result.secure_url,
        publicId: result.public_id,
      };

      setCloudinaryImage(newImageData);

      // Call the updateCompanyLogo mutation
      await updateCompanyLogoMutation.mutateAsync({
        purpose: 'update',
        cloudinaryId: result.public_id,
        cloudinaryUrl: result.secure_url,
      });
    } catch (err) {
      console.error('Error uploading image:', err);
      showMessage({
        message: 'Failed to upload company logo',
        type: 'danger',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const requestPermissions = async () => {
    const { status: cameraStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: libraryStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
      Alert.alert(
        'Permission denied',
        'Sorry, we need camera and photo permissions to make this work!',
      );
      return false;
    }
    return true;
  };

  const pickImage = async () => {
    if (!(await requestPermissions())) return;

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.75,
    });

    if (!result.canceled && result.assets[0]) {
      await uploadCompanyImage(result.assets[0].uri);
    }

    setModalVisible(false);
  };

  const takePhoto = async () => {
    if (!(await requestPermissions())) return;

    let result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      await uploadCompanyImage(result.assets[0].uri);
    }

    setModalVisible(false);
  };

  const removePhoto = async () => {
    try {
      setIsUploading(true);

      // Reset the local state
      setCloudinaryImage({
        url: '',
        publicId: '',
      });

      // Call the updateCompanyLogo mutation with delete purpose
      await updateCompanyLogoMutation.mutateAsync({
        purpose: 'delete',
      });

      showMessage({
        message: 'Company logo removed successfully',
        type: 'success',
      });
    } catch (err) {
      console.error('Error removing image:', err);
      showMessage({
        message: 'Failed to remove company logo',
        type: 'danger',
      });
    } finally {
      setIsUploading(false);
      setModalVisible(false);
    }
  };

  const handleFormSubmit = async (
    values: z.infer<typeof companyDetailsSchema>,
  ) => {
    console.log('values are', values);
    createCompany
      .mutateAsync(values)
      .then((resp) => {
        showMessage({
          message: resp.messageTitle,
          type: 'success',
        });
      })
      .catch((err) => {
        console.log('Error', err.message);
      });
  };

  const handleError = (
    error: FieldErrors<z.infer<typeof companyDetailsSchema>>,
  ) => {
    console.log('Error', error);
    showMessage({
      message: 'Please check form fields',
      type: 'danger',
    });
  };

  const { showActionSheetWithOptions } = useActionSheet();
  const HandleActionSheet = () => {
    const options = [
      'Add new agent',
      'Add existing agent',
      'View added agents',
      'Cancel',
    ];
    const cancelButtonIndex = 3;

    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
      },
      (selectedIndex?: number) => {
        switch (selectedIndex) {
          case 0:
            router.push('/profile-screens/add-agent');
            break;
          case 1:
            router.push('/profile-screens/add-existing-agent');
            break;
          case 2:
            present();
            break;
          case cancelButtonIndex:
            break;
          default:
            break;
        }
      },
    );
  };

  // Get the appropriate image URL to display
  const displayImageUrl =
    company?.cloudinaryCompanyLogoUrl || company?.filePublicUrl || '';

  return (
    <>
      <View className="flex-1">
        {/* Header */}
        <LinearGradient
          colors={['#F04D24', '#C33D12', '#962C00']}
          start={[0, 0]}
          end={[0.7, 0]}
        >
          <BlurView intensity={50} tint="light">
            <View className="h-[250px] px-5" style={{ paddingTop: insets.top }}>
              {/* Top part */}
              <View className="my-2.5 flex-row items-center justify-between">
                <Pressable
                  onPress={() => navigation.goBack()}
                  className="w-[85px] "
                >
                  <Entypo name="chevron-thin-left" size={18} color="#FFFBF9" />
                </Pressable>

                <Text className="font-airbnb_xbd text-xl font-extrabold text-white flex-1 text-center">
                  About Company
                </Text>

                {company?.id ? (
                  <Pressable onPress={HandleActionSheet}>
                    <View className="flex flex-row items-center w-[85px]">
                      <AntDesign name="plus" size={16} color="white" />
                      <Text className="text-white font-medium text-sm font-airbnb_md">
                        Add Agents
                      </Text>
                    </View>
                  </Pressable>
                ) : (
                  <View className="w-[85px]" />
                )}
              </View>

              <View className="pb-6 flex-1 items-center justify-center">
                {isUploading ? (
                  <View className="h-32 w-32 items-center justify-center rounded-full bg-gray-200">
                    <ActivityIndicator size="large" color="#F04D24" />
                  </View>
                ) : (
                  <>
                    <NImage
                      source={
                        displayImageUrl
                          ? { uri: displayImageUrl }
                          : require('../../../assets/icons/company-defaultimg.png')
                      }
                      className="h-32 w-32 rounded-full"
                    />

                    <Pressable
                      className="absolute bottom-6 flex-row items-center rounded-xl bg-primary-0 p-1.5"
                      onPress={() => setModalVisible(true)}
                    >
                      <NImage
                        source={require('../../../assets/icons/camera.png')}
                        className="mr-1 h-3 w-3"
                      />
                      <Text className="font-airbnb_md text-[10px] font-medium text-[#5F3924]">
                        Add Company Logo
                      </Text>
                    </Pressable>
                  </>
                )}
              </View>
            </View>
          </BlurView>
        </LinearGradient>

        <EditCompanyProfile
          setValue={setValue}
          onSubmit={handleFormSubmit}
          control={control}
          label={false}
        />
      </View>

      <View className="mb-5">
        {Platform.OS === 'ios' ? (
          <BlurView
            intensity={15}
            tint="light"
            blurReductionFactor={4}
            className="absolute bottom-0 w-full flex-1 flex-col justify-end bg-primary-50/30 px-5 py-3"
          >
            <Button
              loading={createCompany.isPending}
              label={company?.id ? 'Save Changes' : 'Create Company'}
              onPress={handleSubmit(handleFormSubmit, handleError)}
            />
          </BlurView>
        ) : Platform.OS === 'android' ? (
          <View className="absolute bottom-0 w-full flex-1 flex-col justify-end bg-primary-50 px-5 py-3">
            <Button
              loading={createCompany.isPending}
              label={company?.id ? 'Save Changes' : 'Create Company'}
              onPress={handleSubmit(handleFormSubmit, handleError)}
            />
          </View>
        ) : null}
      </View>

      <PhotoOptionsModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onChoosePhoto={pickImage}
        onTakePhoto={takePhoto}
        onRemovePhoto={removePhoto}
      />

      <Modal ref={ref} title="Added Agents" snapPoints={['90%']}>
        <ScrollView className="mb-8">
          {AddedAgents?.companyAgents?.map((item, index) => (
            <View
              key={index}
              className="my-4 flex-1 justify-center items-center "
            >
              <AgentCard
                key={index}
                agent={{
                  ...item,

                  company: {
                    id: item.id,
                    companyName: item.company?.companyName ?? '',
                  },
                }}
                RemoveAgent={true}
              />
            </View>
          ))}
        </ScrollView>
      </Modal>
    </>
  );
});

export default AboutCompany;
