import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { StyleSheet, Switch, Text, View } from 'react-native';

type SettingOptionsProps = {
  title?: string;
  name: string;
  info: string;
  isEnabled: boolean;
  source: any;
  toggleSwitch: () => void;
};

const SettingOptions: React.FC<SettingOptionsProps> = ({
  title,
  name,
  info,
  source,
  isEnabled,
  toggleSwitch,
}) => {
  return (
    <View className="rounded-xl bg-white p-3">
      {title && (
        <Text className="font-airbnb_bd text-lg font-bold text-text-600">
          {title}
        </Text>
      )}
      <View className="mt-3 flex-row items-center justify-between py-3">
        <View className="flex-1 mr-3" style={{ maxWidth: '75%' }}>
          <View className="flex-row items-center">
            <NImage source={source} className="mr-4 h-6 w-6" />
            <View style={{ flex: 1 }}>
              <Text
                className="mb-1 font-airbnb_md text-base font-medium text-primary-750"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {name}
              </Text>
              <Text
                className="font-airbnb_bk text-[10px] font-normal text-text-550"
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {info}
              </Text>
            </View>
          </View>
        </View>
        <View>
          <Switch
            trackColor={{ false: '#CECECE', true: '#C58E00' }}
            thumbColor={isEnabled ? '#FFFBF9' : '#FFF'}
            ios_backgroundColor="#CECECE"
            onValueChange={toggleSwitch}
            value={isEnabled}
          />
        </View>
      </View>
    </View>
  );
};

const Settings = () => {
  const [settings, setSettings] = useState({
    notification: false,
    chatNotification: false,
    microphoneAccess: false,
  });

  const toggleSwitch = (key: keyof typeof settings) => {
    setSettings((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
  };

  return (
    <LinearGradient
      colors={['#FFFBF9', '#FFF9F5', '#B39889']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradient}
    >
      <View className="h-full w-full px-5 pt-6">
        <View>
          <SettingOptions
            title="General Settings"
            name="Notification"
            info="Toggle on to mute the notification, you will not receive any notification related to app"
            source={require('../../../assets/icons/notify.png')}
            isEnabled={settings.notification}
            toggleSwitch={() => toggleSwitch('notification')}
          />
        </View>

        <View className="pt-2.5">
          <SettingOptions
            title="Chat Settings"
            name="Notification"
            info="Mute companion chat notification on home screen"
            source={require('../../../assets/icons/notify.png')}
            isEnabled={settings.chatNotification}
            toggleSwitch={() => toggleSwitch('chatNotification')}
          />
          <SettingOptions
            name="Microphone access"
            info="Slide in to give access to microphone while chatting"
            source={require('../../../assets/icons/microphone.png')}
            isEnabled={settings.microphoneAccess}
            toggleSwitch={() => toggleSwitch('microphoneAccess')}
          />
        </View>
      </View>
    </LinearGradient>
  );
};

export default Settings;

const styles = StyleSheet.create({
  gradient: {
    borderRadius: 32,
    flex: 1,
  },
});
