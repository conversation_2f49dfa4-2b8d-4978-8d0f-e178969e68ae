import { useAuth } from '@/core';
import Entypo from '@expo/vector-icons/Entypo';
import { Image as NImage } from 'expo-image';
import { Redirect, Stack, useNavigation } from 'expo-router';
import React from 'react';
import { Alert, Pressable, StyleSheet } from 'react-native';

// eslint-disable-next-line max-lines-per-function
export default function Layout() {
  const navigation = useNavigation();
  const { status } = useAuth();
  if (status === 'signOut') {
    return <Redirect href="/auth/sign-in" />;
  }
  return (
    <Stack>
      <Stack.Screen
        name="edit-profile"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="edit-profile-new"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="about-company"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add-agent"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add-existing-agent"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="my-wallet"
        options={{
          title: 'My Wallet',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFF9F6' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="recently-viewed"
        options={{
          title: 'Recently Viewed',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFF9F6' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="settings"
        options={{
          title: 'Settings',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFF9F6' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="refer"
        options={{
          title: '',
          headerTintColor: '#12725B',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFCF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="feedback"
        options={{
          title: 'Give Your Feedback',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFF9F6' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="stats"
        options={{
          title: 'Your Stats',
          headerTintColor: '#F15F3A',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFCFB' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={16} color="#451F0A" />
            </Pressable>
          ),
          // headerRight: () => (
          //   <Pressable
          //     className="rounded-lg bg-white px-3 py-2"
          //     style={styles.shadow}
          //     onPress={() => Alert.alert('Not Implemented!!')}
          //   >
          //     <NImage
          //       source={require('../../../assets/icons/filter2.png')}
          //       className="h-6 w-6"
          //     />
          //   </Pressable>
          // ),
        }}
      />
    </Stack>
  );
}

const styles = StyleSheet.create({
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
});
