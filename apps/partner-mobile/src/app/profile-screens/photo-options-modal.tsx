import { Image as NImage } from 'expo-image';
import React from 'react';
import { Modal, Pressable, StyleSheet, Text, View } from 'react-native';

interface ModalOptionProps {
  onPress: () => void;
  icon?: any;
  label: string;
}

interface PhotoOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  onChoosePhoto: () => void;
  onTakePhoto: () => void;
  onRemovePhoto?: () => void;
}

const ModalOption: React.FC<ModalOptionProps> = ({
  onPress,
  icon,
  label,
}: {
  onPress: () => void;
  icon?: any;
  label: string;
}) => (
  <Pressable onPress={onPress} className="flex-row items-center py-3">
    {icon && <NImage source={icon} className="mr-5 h-6 w-6" />}
    <Text className="font-airbnb_md text-base font-medium text-text-500">
      {label}
    </Text>
  </Pressable>
);

const PhotoOptionsModal: React.FC<PhotoOptionsModalProps> = ({
  visible,
  onClose,
  onChoosePhoto,
  onTakePhoto,
  onRemovePhoto,
}) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <Pressable style={styles.modalOverlay} onPress={onClose}>
        <View
          className="rounded-2xl bg-[#FDFDFD] px-5 py-6"
          style={{ backgroundColor: '#FDFDFD' }}
        >
          <Text className="mb-3 font-airbnb_bd text-xl font-bold text-primary-700">
            Change photo
          </Text>

          <ModalOption
            onPress={onChoosePhoto}
            icon={require('../../../assets/icons/gallery.png')}
            label="Choose Photo"
          />
          <ModalOption
            onPress={onTakePhoto}
            icon={require('../../../assets/icons/takephoto.png')}
            label="Take Photo"
          />
          {onRemovePhoto && (
            <ModalOption
              onPress={onRemovePhoto}
              icon={require('../../../assets/icons/bin1.png')}
              label="Remove Current Photo"
            />
          )}
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
});

export default PhotoOptionsModal;
