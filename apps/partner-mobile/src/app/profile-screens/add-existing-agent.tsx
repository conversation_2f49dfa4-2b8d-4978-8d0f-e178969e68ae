import {
  View,
  Text,
  Pressable,
  ActivityIndicator,
  ScrollView,
  Image,
  Dimensions,
  Animated,
} from 'react-native';
import React from 'react';
import TextInputComponent from '@/components/text-input-component';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Entypo from '@expo/vector-icons/Entypo';
import { router } from 'expo-router';
import { api } from '@/utils/api';
import AgentCard from '@/components/agent-card';
import { FlashList } from '@shopify/flash-list';

const AddExistingAgent = () => {
  const insets = useSafeAreaInsets();
  const [text, setText] = React.useState('');

  return (
    <View className="px-5 flex-1" style={{ paddingTop: insets.top }}>
      <Pressable onPress={() => router.back()} className="mb-4 w-[85px] py-2.5">
        <Entypo name="chevron-thin-left" size={18} color="black" />
      </Pressable>

      <TextInputComponent
        placeholder="Enter Agent Name"
        background="bg-[#fff]"
        searchiconimagecolor="#3A3A3A"
        textinputcolor="text-[#3A3A3A]"
        bordercolor="border-secondary-main700"
        value={text}
        onTextChange={(text) => setText(text)}
      />

      <AddExistingAgentResult text={text} />
    </View>
  );
};

const AddExistingAgentResult = ({ text }: { text: string }) => {
  const {
    data: getData,
    isLoading: isLoadingAutoComplete,
    isError: isErrorAutoComplete,
  } = api.company.existingAgents.useQuery({
    val: text,
  });

  console.log('request and response', text, getData);

  if (isLoadingAutoComplete) {
    return (
      <View className="pt-safe flex-1 justify-center items-center ">
        <ActivityIndicator size={40} color="#d6330a" />
      </View>
    );
  }

  if (isErrorAutoComplete) {
    return (
      <View className="pt-safe flex-1  justify-center items-center">
        <Text>Searched data not available</Text>
      </View>
    );
  }

  if (!isLoadingAutoComplete && !isErrorAutoComplete && !getData) {
    return (
      <View className="pt-safe flex-1 justify-center items-center">
        <Text>Agent not Available</Text>
      </View>
    );
  }

  if (text && getData) {
    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        {getData &&
          getData.map((item) => (
            <View key={item.id} className="my-4 flex-row  items-center">
              <AgentCard agent={item} AddAgent={true} />
            </View>
          ))}
      </ScrollView>
      // <View className="w-[200px] flex-1 justify-center items-center bg-amber-300">
      //   <FlashList
      //     showsVerticalScrollIndicator={false}
      //     data={getData}
      //     estimatedItemSize={210}
      //     keyExtractor={(item) => item.id.toString()}
      //     className="flex-1"
      //     renderItem={({ item }) => {
      //       return (
      //         <View
      //           key={item.id}
      //           style={{ width: '200px' }}
      //           className="bg-red-500 w-[200px]"
      //         >
      //           <AgentCard agent={item} AddAgent={true} />
      //         </View>
      //       );
      //     }}
      //   />
      // </View>
    );
  }
};

export default AddExistingAgent;
