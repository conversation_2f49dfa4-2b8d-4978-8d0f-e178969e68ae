import React, { useState, useEffect } from 'react';
import { Al<PERSON>, Platform } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import {
  Button,
  ControlledInput,
  ControlledSelect,
  Pressable,
  Text,
  View,
} from '@/ui';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Image as NImage } from 'expo-image';

export type FormType = z.infer<typeof signUpInputValidation>;

import PhotoOptionsModal from './photo-options-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { signUpInputValidation } from '@/utils/form-validators';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import TextInputGoogleAutocomplete from '@/components/text-input-google-autocomplete';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from 'expo-router';
import Entypo from '@expo/vector-icons/Entypo';
import * as ImagePicker from 'expo-image-picker';

// eslint-disable-next-line max-lines-per-function
const AddAgent = () => {
  const addAgent = api.company.addAgent.useMutation();
  const utils = api.useUtils();
  const awsPreSignedUrlMutation = api.aws.getPresignedUrl.useMutation();
  const awsGetPublicUrlMutation = api.aws.getPublicFileUrl.useMutation();
  const userImageMutation = api.user.createUserMedia.useMutation();

  const [isUploading, setIsUploading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const navigation = useNavigation();
  const [image, setImage] = useState<string | null>(null);
  const insets = useSafeAreaInsets();

  // Setup react-hook-form
  const { handleSubmit, setValue, control } = useForm<FormType>({
    resolver: zodResolver(signUpInputValidation),
  });

  const { data: cityData } = api.user.getCities.useQuery();

  const requestPermissions = async () => {
    const { status: cameraStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: libraryStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
      Alert.alert(
        'Permission denied',
        'Sorry, we need camera and photo permissions to make this work!',
      );
      return false;
    }
    return true;
  };

  const pickImage = async () => {
    if (!(await requestPermissions())) return;

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.75,
    });

    if (!result.canceled) {
      const file = result.assets[0];
      if (!file) {
        return;
      }
      setModalVisible(false);
      await uploadUserImage(
        file.fileName || 'profile-image.jpeg',
        file.type || 'image/jpeg',
        file.uri,
      );
    }
  };

  const takePhoto = async () => {
    if (!(await requestPermissions())) return;

    let result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled) {
      const file = result.assets[0];
      if (!file) {
        return;
      }
      await uploadUserImage(
        file.fileName || 'profile-image.jpeg',
        file.type || 'image/jpeg',
        file.uri,
      );
    }
    setModalVisible(false);
  };

  const removePhoto = () => {
    setImage(null);
    setModalVisible(false);
  };

  const onSubmit = async (data: FormType) => {
    try {
      const response = await addAgent.mutateAsync(data);

      if (response.messageTitle) {
        showMessage({
          message: response.messageTitle,
          type: 'success',
        });
      }

      if (response.error) {
        showMessage({
          message: response.error,
          type: 'danger',
        });
        return;
      }
      navigation.goBack();
    } catch (error) {
      console.log(error);
      showMessage({
        message: 'Error adding agent' + String(error),
        type: 'danger',
      });
    }
  };

  // const uploadUserImage = async (
  //   fileName: string,
  //   fileType: string,
  //   filePath: string,
  // ) => {
  //   try {
  //     setIsUploading(true);
  //     const key = `/user_image/${Date.now()}-${fileName}`;
  //     const { url, key: preSignedUrlKey } =
  //       await awsPreSignedUrlMutation.mutateAsync({
  //         fileName: key,
  //         contentType: 'image/jpeg',
  //         publicAvailable: true,
  //       });

  //     if (!url) {
  //       setIsUploading(false);
  //       showMessage({
  //        message: 'Something went wrong',
  //         type: 'danger',
  //       });
  //       return;
  //     }

  //     const formData = new FormData();
  //     const response = await fetch(filePath);
  //     const blob = await response.blob(); // Convert the image to a blob

  //     const requestOptions = {
  //       method: 'PUT',
  //       headers: {
  //         'Content-Type': blob.type,
  //       },
  //       body: blob,
  //     };
  //     const res = await fetch(url, requestOptions);

  //     const filePublicUrl = await awsGetPublicUrlMutation.mutateAsync({
  //       fileKey: preSignedUrlKey,
  //     });

  //     if (res.ok) {
  //       await userImageMutation.mutateAsync({
  //         fileKey: preSignedUrlKey,
  //         filePublicUrl: filePublicUrl,
  //       });
  //       utils.invalidate();
  //     }
  //     showMessage({
  //       message: 'User Image uploaded successfully',
  //       type: 'success',
  //     });
  //     setModalVisible(false);
  //   } catch (e) {
  //     setIsUploading(false);
  //     showMessage({
  //       message: 'Error uploading user image',
  //       type: 'danger',
  //     });
  //   } finally {
  //     setIsUploading(false);
  //   }
  // };

  return (
    <>
      <View className="flex-1">
        {/* Header */}
        <LinearGradient
          colors={['#F04D24', '#C33D12', '#962C00']}
          start={[0, 0]}
          end={[0.7, 0]}
        >
          <BlurView intensity={50} tint="light">
            <View className="h-[120px] px-5" style={{ paddingTop: insets.top }}>
              {/* 250px */}
              {/* top part */}
              <View className="my-2.5 flex-row items-center justify-between">
                <Pressable
                  onPress={() => navigation.goBack()}
                  className="w-[85px]"
                >
                  <Entypo name="chevron-thin-left" size={18} color="#FFFBF9" />
                </Pressable>

                <Text className="font-airbnb_xbd text-xl font-extrabold text-white">
                  Add Agent
                </Text>
                <Text className="w-[85px]"> </Text>
              </View>

              {/* <View className="pb-6 flex-1 items-center justify-center">
                <NImage
                  source={
                    image
                      ? { uri: image }
                      : require('../../../assets/icons/user-defaultimg.png')
                  }
                  className="h-32 w-32 rounded-full"
                />

                <Pressable
                  className="absolute bottom-6 flex-row items-center rounded-xl bg-primary-0 p-1.5"
                  onPress={() => setModalVisible(true)}
                >
                  <NImage
                    source={require('../../../assets/icons/camera.png')}
                    className="mr-1 h-3 w-3"
                  />
                  <Text className="font-airbnb_md text-[10px] font-medium text-[#5F3924] dark:text-[#5F3924]">
                    Add Photo
                  </Text>
                </Pressable>
              </View> */}
            </View>
          </BlurView>
        </LinearGradient>

        {/* Data entry fields */}
        <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
          <View className={'px-4 pt-6'}>
            <ControlledInput
              testID="agent_name"
              control={control}
              name="name"
              label="Agent Name"
            />
            <ControlledInput
              testID="email-input"
              control={control}
              name="email"
              label="Email Id"
              autoCapitalize="none"
            />
            <ControlledInput
              testID="phone-number"
              control={control}
              keyboardType="numeric"
              name="phoneNumber"
              label="Phone Number"
            />
            <ControlledInput
              testID="aadhaar-number"
              control={control}
              keyboardType="numeric"
              name="adharcardNumber"
              label="Aadhaar Card Number*"
            />
            <ControlledInput
              testID="pancard-number"
              control={control}
              keyboardType="default"
              name="pancardNumber"
              label="PAN Card Number*"
              transformToUppercase={true}
            />
            <ControlledInput
              testID="rera-number"
              control={control}
              keyboardType="default"
              name="reraNumber"
              label="RERA Number (optional)"
              transformToUppercase={true}
            />
            <ControlledInput
              testID="gst-number"
              control={control}
              keyboardType="default"
              name="gstNumber"
              label="GST Number"
            />
            <ControlledSelect
              name="cityId"
              label="City"
              control={control}
              placeholder="Select your city"
              options={cityData?.map((city) => ({
                label: city.name,
                value: city.id,
              }))}
            />
          </View>
        </ScrollView>

        {/* Bottom Button */}
        <View className="mb-5 px-5">
          <Button
            onPress={handleSubmit(onSubmit)}
            variant="default"
            size="default"
            label="Save Changes"
            loading={false} // You can modify to show a loader if needed
          />
        </View>
      </View>
      {/* modal
      <PhotoOptionsModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onChoosePhoto={pickImage}
        onTakePhoto={takePhoto}
        onRemovePhoto={removePhoto}
      /> */}
    </>
  );
};

export default AddAgent;
