import {
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { Image, ImageBackground } from 'expo-image';
import { api } from '@/utils/api';
import { Button } from '@/ui';

const { height, width } = Dimensions.get('screen');

const Transactions = ({
  tag,
  message,
  time,
  amount,
}: {
  tag: boolean;
  message: string;
  time: Date;
  amount: number;
}) => {
  console.log('referal message :', message);
  return (
    <View className="my-2 px-3 py-2 flex-row items-center justify-between border-b border-text-50">
      <View>
        <Text className="mb-1 text-sm font-medium font-airbnb_md text-text-600">
          Referral points
        </Text>
        <Text className="text-xs font-normal font-airbnb_bk text-text-500">
          {message ?? ` Received from abhishek agent`}
        </Text>
      </View>

      <View>
        {tag ? (
          <View className="mb-1 py-1 px-2 bg-[#F1FFF6] self-end">
            <Text className="text-base font-medium font-airbnb_md text-[#009E27]">
              + {amount ?? 232}
            </Text>
          </View>
        ) : (
          <View className="mb-1 py-1 px-2 bg-[#FFF4F4] self-end">
            <Text className="text-base font-medium font-airbnb_md text-secondary-800">
              - {amount ?? 232}
            </Text>
          </View>
        )}

        <Text
          className={'text-[10px] font-medium font-airbnb_md text-text-300'}
        >
          On {time.toLocaleTimeString() ?? `28 Mar, 09:49 AM`}
        </Text>
      </View>
    </View>
  );
};

const MyWallet = () => {
  const { data, isLoading, isError } = api.wallet.getWalletDetails.useQuery();

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#009E27" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Error loading data</Text>
      </View>
    );
  }

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <View className="flex-1 px-5 pt-4">
        <ImageBackground
          source={require('../../../assets/images/bgji.png')}
          className="rounded-xl mt-5 my-8"
          style={{
            width: width * 0.9,
            height: height * 0.2,
          }}
        >
          <View className="flex-1 items-center justify-center">
            <Text className="mb-2 text-xl font-normal font-airbnb_bk text-white">
              Total Earning
            </Text>
            <Text className="text-5xl font-black font-airbnb_blk text-white">
              {((data && data[0]?.user?.walletBalanceInCents) ?? 0) / 100}
            </Text>
          </View>
        </ImageBackground>

        <Text className="mb-3 text-lg font-bold font-airbnb_bd text-main700">
          Wallet History
        </Text>
        {data &&
          data.map((transactions, index) => (
            <Transactions
              key={index}
              amount={(transactions.amountInCents ?? 0) / 100}
              message={transactions.message}
              time={new Date(transactions.createdAt)}
              tag={transactions.amountInCents > 0}
            />
          ))}

        {data?.length === 0 && (
          <View className="flex-1 items-center justify-center ">
            <Text className="text-xl font-normal font-airbnb_bk text-black">
              No transactions yet
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

export default MyWallet;

const styles = StyleSheet.create({});

// const PopUp = ({}) => {
//   return (
//     <View className="items-center justify-center">
//       <Image
//         source={require('../../../assets/images/coinsamigo.png')}
//         style={{
//           height: height * 0.215,
//           width: width * 0.463,
//           resizeMode: 'contain',
//         }}
//       />

//       <Text className="text-2xl font-airbnb_bd font-bold text-text-main700">
//         <Text>Congratulations!</Text>
//         <Text>You have just earned {'50'} pts</Text>
//       </Text>

//       <Text className="text-base font-airbnb_bk font-normal text-text-300">
//         One of your agent has joined by your referral code. Do more invitations
//         to earn more.
//       </Text>

//       <Button label="View " />
//     </View>
//   );
// };
