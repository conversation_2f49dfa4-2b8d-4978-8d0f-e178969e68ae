import Entypo from '@expo/vector-icons/Entypo';
import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from 'expo-router';
import React, { useState, useEffect, useRef } from 'react';
import { Alert, Text, View, Dimensions } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Button, ControlledInput, ControlledSelect, Pressable } from '@/ui';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

export type FormType = z.infer<typeof signUpInputValidation>;

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { signUpInputValidation } from '@/utils/form-validators';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { EditBottomsheet } from '@/components/profile/edit-bottomsheet';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

const EditProfile = () => {
  const utils = api.useUtils();
  const profile = api.user.profile.useQuery();
  const profileUpdateMutation = api.user.profileUpdate.useMutation();

  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  const { handleSubmit, setValue, control } = useForm<FormType>({
    resolver: zodResolver(signUpInputValidation),
  });

  const { data: cityData } = api.user.getCities.useQuery();

  useEffect(() => {
    if (profile.data) {
      setValue('name', profile.data.name);
      setValue('email', profile.data.email);
      setValue('phoneNumber', profile.data.phoneNumber);
      setValue('adharcardNumber', profile.data.adharcardNumber ?? '');
      setValue('pancardNumber', profile.data.pancardNumber);
      setValue('reraNumber', profile.data.reraNumber ?? '');
      setValue('gstNumber', profile.data.gstNumber ?? '');
      setValue('cityId', profile.data.cityId ?? '');
    }
  }, [profile.data]);

  const onSubmit = async (data: FormType) => {
    try {
      // Ensure cityId is a string
      const formData = {
        ...data,
        cityId: data.cityId || '',
      };

      await profileUpdateMutation.mutateAsync(formData);
      showMessage({
        message: 'Profile updated successfully',
        type: 'success',
      });
      utils.invalidate();
    } catch (error) {
      console.log(error);
      showMessage({
        message: 'Failed to update profile',
        type: 'danger',
      });
    }
  };

  // console.log('profile?.data?.filePublicUrl', profile?.data?.filePublicUrl);

  return (
    <>
      <View className="flex-1">
        <LinearGradient
          colors={['#F04D24', '#C33D12', '#962C00']}
          start={[0, 0]}
          end={[0.7, 0]}
        >
          <BlurView intensity={50} tint="light">
            <View className="h-[120px] px-5" style={{ paddingTop: insets.top }}>
              <View className="my-2.5 flex-row items-center justify-between">
                <Pressable
                  onPress={() => navigation.goBack()}
                  className="w-[85px]"
                >
                  <Entypo name="chevron-thin-left" size={18} color="#FFFBF9" />
                </Pressable>

                <Text className="font-airbnb_xbd text-xl font-extrabold text-white">
                  Edit Profile
                </Text>
                <Text className="w-[85px]"> </Text>
              </View>
            </View>
          </BlurView>
        </LinearGradient>

        <KeyboardAwareScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          className="flex-1"
        >
          <View className={'px-4 pt-6'}>
            <ControlledInput
              testID="name"
              control={control}
              name="name"
              label="Name"
            />
            <View className="relatable">
              <View className="absolute self-end z-10">
                <EditBottomsheet
                  phoneNumber={profile.data?.phoneNumber || ''}
                  email={profile.data?.email || ''}
                />
              </View>
              <ControlledInput
                testID="email-input"
                control={control}
                name="email"
                label="Email Id"
                autoCapitalize="none"
                editable={false}
              />
            </View>
            <ControlledInput
              testID="phone-number"
              control={control}
              keyboardType="numeric"
              name="phoneNumber"
              label="Phone Number"
              editable={false}
            />
            <ControlledInput
              testID="aadhaar-number"
              control={control}
              keyboardType="numeric"
              name="adharcardNumber"
              label="Aadhaar Card Number*"
              editable={false}
            />
            <ControlledInput
              testID="pancard-number"
              control={control}
              keyboardType="default"
              name="pancardNumber"
              label="PAN Card Number*"
              transformToUppercase={true}
              editable={false}
            />
            <ControlledInput
              testID="rera-number"
              control={control}
              keyboardType="default"
              name="reraNumber"
              label="RERA Number (optional)"
              transformToUppercase={true}
            />
            <ControlledInput
              testID="gst-number"
              control={control}
              keyboardType="default"
              name="gstNumber"
              label="GST Number"
            />
            <ControlledSelect
              name="cityId"
              label="City"
              control={control}
              placeholder="Select your city"
              disabled={true}
              options={cityData?.map((city) => ({
                label: city.name,
                value: city.id,
              }))}
            />
          </View>
        </KeyboardAwareScrollView>

        <View className="mb-5 px-5">
          <Button
            onPress={handleSubmit(onSubmit)}
            variant="default"
            size="default"
            label="Save Changes"
            loading={profileUpdateMutation.isPending}
          />
        </View>
      </View>
    </>
  );
};

export default EditProfile;
