import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import {
  Dimensions,
  Pressable,
  StyleSheet,
  Text,
  View,
  Share,
} from 'react-native';
import { showMessage } from 'react-native-flash-message';
import * as Clipboard from 'expo-clipboard';
import * as Linking from 'expo-linking';
import { api } from '@/utils/api';
import { getBaseUrl } from '@/utils/base-url';

const { height, width } = Dimensions.get('screen');

const Refer = () => {
  const { data: profile } = api.user.profile.useQuery();
  const inviteCode = profile?.inviteCode ?? 'N/A';
  const referralLink = `${getBaseUrl()}/sign-up?referralCode=${inviteCode}`;

  const handleCopyCode = async () => {
    await Clipboard.setStringAsync(inviteCode);
    console.log('copied');

    showMessage({
      message: 'Invite code copied to clipboard!',
      type: 'success',
    });
  };

  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: `Join me on Deer Connect - A great real estate platform. Use my referral link to sign up: ${referralLink}`,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('shared');
        }
      }
    } catch (error) {
      console.log(error);
      showMessage({
        message: 'Failed to share.',
        type: 'danger',
      });
    }
  };

  return (
    <LinearGradient
      colors={['#FFFBF9', '#FFF9F5', '#B39889']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradient}
    >
      <View className="mt-6 items-center px-5">
        <Text className="font-airbnb_bk text-sm font-normal text-text-550">
          Invite Agent
        </Text>
        <Text className="font-airbnb_blk text-xl font-black text-secondary-main700">
          Invite Agents
          {/* & Earn */}
        </Text>
        <Text className="mt-3 font-airbnb_bk text-sm font-normal text-text-500">
          Refer agents to join our platform
          {/* & earn

           rewards.
           */}
        </Text>

        {/* Image */}
        <View className="py-6">
          <NImage
            source={require('../../../assets/images/cuate.png')}
            style={{ height: height * 0.3464, width: width * 0.7523 }}
          />
        </View>

        {/* invite code */}
        <Pressable
          className="flex-row items-center rounded-xl bg-primary-100 px-5 py-2.5"
          onPress={handleCopyCode}
        >
          <Text className="font-airbnb_bk text-xs font-normal text-text-600">
            Invite code : {inviteCode}
          </Text>
          <NImage
            source={require('../../../assets/icons/copyicon.png')}
            className="ml-2 h-3 w-3"
          />
        </Pressable>

        {/* buttons */}
        <View className="w-full flex-row items-center justify-between pt-8 px-5">
          <Pressable
            className="flex-1 mr-3 flex-row items-center justify-center rounded-xl bg-secondary-main700 px-6 py-3.5"
            onPress={() =>
              Linking.openURL(
                `whatsapp://send?text=Hey! I'm inviting you to join MyDeer, a great real estate platform. Use my referral link to sign up: ${referralLink}`,
              )
            }
          >
            <NImage
              source={require('../../../assets/icons/whatsapp.png')}
              className="mr-2 h-5 w-5"
            />
            <Text
              className="font-airbnb_md text-sm font-medium text-white"
              numberOfLines={2}
            >
              WhatsApp
            </Text>
          </Pressable>

          <Pressable
            className="flex-1 ml-3 flex-row items-center justify-center rounded-xl bg-secondary-100 px-6 py-3.5"
            onPress={handleShare}
          >
            <NImage
              source={require('../../../assets/icons/share2.png')}
              className="mr-2 h-5 w-5"
            />
            <Text
              className="font-airbnb_md text-sm font-medium text-secondary-main700"
              numberOfLines={2}
            >
              More Options
            </Text>
          </Pressable>
        </View>
      </View>
    </LinearGradient>
  );
};

export default Refer;

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
    height: 100,
  },
  blurContainer: {
    flex: 1,
    justifyContent: 'center',
  },
});
