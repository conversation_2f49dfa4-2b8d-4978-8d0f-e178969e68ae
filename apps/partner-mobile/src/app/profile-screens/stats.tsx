import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Pressable,
  Animated,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { format } from 'date-fns';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  PopulationPyramid,
} from 'react-native-gifted-charts';
import CircularGraph from '@/components/circular-graph';
import { BlurView } from 'expo-blur';
import ColourfulCircularGraph from '@/components/colourful-circular-graph';
import Entypo from '@expo/vector-icons/Entypo';
import { router } from 'expo-router';
import { api } from '@/utils/api';

interface TimeItem {
  id: number;
  time: string;
}

interface ButtonMeasurement {
  x: number;
  width: number;
}
type ButtonMeasurements = {
  [key: number]: ButtonMeasurement;
};

const TabsList = [
  {
    name: 'Today',
    key: 'today',
  },
  {
    name: 'Yesterday',
    key: 'yesterday',
  },
  {
    name: 'This Week',
    key: 'this-week',
  },
  {
    name: 'Last Week',
    key: 'last-week',
  },
  {
    name: 'This Month',
    key: 'this-month',
  },
  {
    name: 'Last Month',
    key: 'last-month',
  },
  {
    name: 'This Year',
    key: 'this-year',
  },
  {
    name: 'Last Year',
    key: 'last-year',
  },
  // {
  //   name: "All Time",
  //   key: "all-time",
  // },
] as const;

const colors = [
  '#0062FF',
  '#12725B',
  '#FF974A',
  '#FFC542',
  '#45B7D1',
  '#F04D24',
];

const PropertiesWithMostVisitors = ({ filter }: { filter: string }) => {
  const {
    data: topFiveListedProperties,
    isPending: isPendingTopFiveListedProperties,
    error: errorTopFiveListedProperties,
  } = api.stats.getTopFiveListedProperties.useQuery({
    filter,
  });

  if (isPendingTopFiveListedProperties) {
    return null;
  }

  if (errorTopFiveListedProperties) {
    return null;
  }

  if (topFiveListedProperties.propertyDetails.length === 0) {
    return null;
  }

  return (
    <View className="w-full px-5 mb-5 flex-1 justify-center items-center">
      <View className="w-full flex-row items-center justify-between p-3">
        {/*<Entypo*/}
        {/*  name="chevron-small-left"*/}
        {/*  size={24}*/}
        {/*  color="#6B6B6B"*/}
        {/*  className="p-3 rounded-lg"*/}
        {/*/>*/}
        <Text className="text-text-600 text-base font-airbnb_bd font-bold m-auto">
          Properties With Most Visitors
        </Text>
        {/*<Entypo*/}
        {/*  name="chevron-small-right"*/}
        {/*  size={24}*/}
        {/*  color="#6B6B6B"*/}
        {/*  className="p-3 rounded-lg"*/}
        {/*/>*/}
      </View>

      <ColourfulCircularGraph
        segments={topFiveListedProperties?.propertyDetails.map(
          (item, index) => ({
            value: item._count ?? 0,
            color: colors[index] ?? '#FFFFFF',
          }),
        )}
        // CenterIcon={</>}
        headingText={
          <Text className="text-text-main700 font-black font-airbnb_blk text-xl">
            {topFiveListedProperties?.propertyDetails?.reduce(
              (acc, item) => acc + item._count,
              0,
            )}
          </Text>
        }
        descriptionText={
          <Text
            className="text-center text-text-550 text-[10px] font-normal font-airbnb_bk"
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            Visitors
          </Text>
        }
      />

      <View className="w-full">
        <Pressable
          className="self-end"
          onPress={() => router.push('/(app)/listings')}
        >
          <Text
            className="text-secondary-main700 font-airbnb_md text-base font-medium border-b border-secondary-main700"
            // style={{ borderBottomWidth: 1, borderBottomColor: '#F04D24' }}
          >
            View Full Listings
          </Text>
        </Pressable>

        <View className="mt-3 w-full flex-row items-center justify-center flex-wrap">
          {topFiveListedProperties?.propertyDetails.map((item, index) => (
            <View
              key={index}
              className="mt-1.5 mr-2 w-[48%] flex-row items-center gap-2"
            >
              <Image
                source={require('../../../assets/icons/reddot.png')}
                className="h-3 w-3"
                tintColor={colors[index] ?? '#FFFFFF'}
              />
              <Text
                className="flex-1 text-base font-normal font-airbnb_bk text-text-500"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {item.propertyTitle}
              </Text>
              <Text
                className="w-[25%] text-sm font-bold font-airbnb_bd text-text-550"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {item._count}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

const Stats = () => {
  const [selectedFilter, setSelectedFilter] = useState<string>('today');
  const {
    data: stats,
    isPending,
    refetch,
  } = api.stats.getStats.useQuery({
    filter: selectedFilter,
  });

  const [buttonMeasurements, setButtonMeasurements] =
    useState<ButtonMeasurements>({});
  const [isReady, setIsReady] = useState(false);

  const sliderPosition = useRef(new Animated.Value(0)).current;
  const sliderWidth = useRef(new Animated.Value(0)).current;

  // useEffect(() => {
  //   if (
  //     Object.keys(buttonMeasurements).length === TimeData.length &&
  //     !isReady
  //   ) {
  //     const firstButton = buttonMeasurements[1];
  //     if (firstButton) {
  //       sliderPosition.setValue(firstButton.x);
  //       sliderWidth.setValue(firstButton.width);
  //       setIsReady(true);
  //     }
  //   }
  // }, [buttonMeasurements, isReady]);

  const handleTimeSelect = (filter: string) => {
    setSelectedFilter(filter);
    // const button = buttonMeasurements[filter];
    // if (!button) return;
    //
    // Animated.parallel([
    //   Animated.spring(sliderPosition, {
    //     toValue: button.x,
    //     useNativeDriver: false,
    //     tension: 50,
    //     friction: 7,
    //   }),
    //   Animated.spring(sliderWidth, {
    //     toValue: button.width,
    //     useNativeDriver: false,
    //     tension: 50,
    //     friction: 7,
    //   }),
    // ]).start();
  };

  return (
    <ScrollView
      className="flex-1"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={isPending} onRefresh={refetch} />
      }
    >
      <View className="mt-6">
        {/* Filter */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 0 }}
          className="w-full"
        >
          <View className="px-5 flex-row gap-2.5 items-center justify-between relative">
            {TabsList.map((item) => (
              <Pressable
                key={item.key}
                onPress={() => handleTimeSelect(item.key)}
                onLayout={({
                  nativeEvent: {
                    layout: { x, width },
                  },
                }) => {
                  setButtonMeasurements((prev) => ({
                    ...prev,
                    [item.key]: { x, width },
                  }));
                }}
                className={`px-4 py-2 rounded-lg justify-center items-center z-10 ${
                  selectedFilter === item.key
                    ? 'bg-primary-100'
                    : 'border border-text-100'
                }`}
              >
                <Text
                  className={`font-normal text-xs font-airbnb_bk ${
                    selectedFilter === item.key
                      ? 'text-secondary-850'
                      : 'text-text-600'
                  }`}
                >
                  {item.name}
                </Text>
              </Pressable>
            ))}

            <Animated.View
              style={[
                styles.slider,
                {
                  transform: [{ translateX: sliderPosition }],
                  width: sliderWidth,
                  opacity: isReady ? 1 : 0,
                },
              ]}
            />
          </View>
        </ScrollView>

        <PropertiesWithMostVisitors filter={selectedFilter} />

        {/* Visitors graph */}
        <View className="px-5 mt-5 flex-row items-center justify-between">
          <View>
            <View className="flex-row items-center">
              <Text className="mr-2 text-text-main700 font-airbnb_blk font-black text-xl">
                {stats?.totalVisitors}
              </Text>
              {/* <Image
                source={require('../../../assets/icons/down.png')}
                className="mr-1 h-3 w-3"
              /> */}
              {/* <Text className="text-[#D50000] font-airbnb_bd font-bold text-xs">
                1.5%
              </Text> */}
            </View>
            <Text className="mt-1 text-text-600 font-airbnb_bk font-normal text-xs">
              Visitors
            </Text>
          </View>

          {/* <View>
            <LineChart data={data} areaChart />
          </View> */}
        </View>
        {isPending && <ActivityIndicator />}

        {/* charts*/}
        {stats && (
          <View className="px-5 mt-4 gap-4 flex-row flex-wrap justify-around">
            <Card
              color={colorGradients[0]}
              value={stats.totalVisitors}
              previousValue={stats.previousTotalVisitors}
              label="Property Visitors"
            />

            <Card
              color={colorGradients[1]}
              value={stats.propertiesConnections}
              previousValue={stats.previousPropertiesConnections}
              label="Total Responses"
            />

            <Card
              color={colorGradients[2]}
              value={stats.connectionsB2B + stats.connectionsB2C}
              previousValue={
                stats.previousConnectionsB2B + stats.previousConnectionsB2C
              }
              label="Network Growth"
            />

            <Card
              color={colorGradients[3]}
              value={stats.averageRating ?? 0}
              previousValue={stats.previousAverageRating ?? 0}
              label="/5 Client Satisfaction"
            />

            {/* Circle stats show */}
            {/*<View*/}
            {/*  className="w-full"*/}
            {/*  style={{*/}
            {/*    flexDirection: 'row',*/}
            {/*    flexWrap: 'wrap',*/}
            {/*  }}*/}
            {/*>*/}
            {/*  {statsData.map((data, index) => (*/}
            {/*    <View key={index} className="w-1/3">*/}
            {/*      <CircularGraph*/}
            {/*        total={data.total}*/}
            {/*        magnitude={data.magnitude}*/}
            {/*        progressCircleColor={data.progressCircleColor}*/}
            {/*        bgCircleColor={data.bgCircleColor}*/}
            {/*        CenterIcon={data.centerIcon}*/}
            {/*        headingText={data.headingText}*/}
            {/*        descriptionText={data.descriptionText}*/}
            {/*      />*/}
            {/*    </View>*/}
            {/*  ))}*/}
            {/*</View>*/}
          </View>
        )}

        <View className="mt-5 mb-6">
          <NotificationCard />
        </View>
      </View>
    </ScrollView>
  );
};

export default Stats;

const styles = StyleSheet.create({
  slider: {
    position: 'absolute',
    height: 36,
    backgroundColor: '#fef9ea',
    borderRadius: 8,
    zIndex: 1,
  },
  gradientBox: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    height: 124,
    // width: '44%',
    width: '46%',
    // width: 'auto',
    // flex: 1,
  },
});

interface CardsProps {
  color: readonly [string, string, ...string[]];
  label: string;
  value: number;
  previousValue: number;
  isLoading?: boolean;
}
export const colorGradients: readonly [string, string, ...string[]][] = [
  ['rgba(143, 201, 187, 0.20)', 'rgba(18, 114, 91, 0.20)'],
  ['rgba(82, 131, 222, 0.25)', 'rgba(39, 85, 171, 0.25)'],
  ['rgba(255, 196, 164, 0.25)', 'rgba(255, 135, 69, 0.25)'],
  ['rgba(255, 214, 103, 0.25)', 'rgba(255, 204, 0, 0.25)'],
];

const DiffValue = ({
  currentValue,
  previousValue,
}: {
  currentValue: number;
  previousValue: number;
}) => {
  const value = currentValue - previousValue;

  const positive = value >= 0;

  return (
    <View className="flex-row items-center">
      <Image
        source={
          positive
            ? require('../../../assets/icons/Up.png')
            : require('../../../assets/icons/down.png')
        }
        className="mr-1 h-3 w-3"
      />
      <Text
        className={`${positive ? 'text-[#2A6A2A]' : 'text-[#D50000]'} font-airbnb_bd font-bold text-xs`}
      >
        {value}
      </Text>
    </View>
  );
};

export const Card: React.FC<CardsProps> = ({
  color,
  label,
  value,
  previousValue,
  isLoading,
}) => {
  return (
    <LinearGradient
      colors={color}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      // locations={[0.1346, 1.0812]} // Using approximate percentage stops
      style={styles.gradientBox}
    >
      <View className="w-full p-3">
        <View className="flex-row items-center justify-between w-full">
          <Image
            source={require('../../../assets/icons/viewsicon.png')}
            className="h-6 w-6"
          />
          <DiffValue currentValue={value} previousValue={previousValue} />
        </View>

        {/* Texts */}
        <View className="mt-3">
          <View className="flex-row items-center">
            <Text className="text-main700 font-airbnb_bd text-lg font-bold">
              {value}{' '}
            </Text>
            {/*<Text className="text-500 font-airbnb_bk text-[10px] font-normal">*/}
            {/*  total*/}
            {/*</Text>*/}
          </View>
          <Text className="mt-0.5 text-text-600 text-xs font-normal font-airbnb_bk">
            {label}
          </Text>
          <Text className="mt-1.5 text-text-500 text-[10px] font-normal font-airbnb_bk">
            Compared to ({previousValue})
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const NotificationCard = () => {
  const { data, isPending } = api.notification.getAllNotifications.useQuery();

  return (
    <View className="px-5">
      <View className="p-4 border-[1.25px] rounded-2xl border-[#DFE7EF]">
        <View className="flex-row items-center">
          <Text className="mr-1.5 text-base font-airbnb_md text-text-600 font-medium">
            Notifications
          </Text>

          {/*<Pressable>*/}
          {/*  <Image*/}
          {/*    source={require('../../../assets/icons/info.png')}*/}
          {/*    className="h-5 w-5"*/}
          {/*  />*/}
          {/*</Pressable>*/}
        </View>
        {data?.notifications?.map((item) => (
          <View key={item.id}>
            <View className="flex-row items-center mt-5 justify-between">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/icons/reddot.png')}
                  className="mr-1 h-2 w-2"
                />
                <Text className="mr-1.5 text-base font-airbnb_md text-primary-750 font-medium">
                  {item.title}
                </Text>
              </View>

              <Text className="font-airbnb_md text-xs font-normal text-secondary-main700">
                {format(item.createdAt, 'dd/MM/yyyy hh:mm a')}
              </Text>
            </View>

            <Text
              className="mt-1 border-b-[1.5px] border-[#000] font-airbnb_bk text-[#4D4D4D] text-sm font-normal"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.description}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};
