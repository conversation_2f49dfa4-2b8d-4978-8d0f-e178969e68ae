import { FlashList } from '@shopify/flash-list';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import FavouritesComponent from '@/components/favourites-component';
import { Data, type Item } from '@/ui/listings-favorites-data';
import { api } from '@/utils/api';
import { router } from 'expo-router';

// Define the type expected by FavouritesComponent
type FavouritesComponentItemType = {
  id: string;
  registeryFileKey: string | null;
  propertyTitle: string | null;
  propertyFor: string | null;
  areaUnit: {
    id: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    shortForm: string;
    conversionMultiplyer: number;
  } | null;
  area: number | null;
  aboutProperty: string | null;
  propertyAddress: string | null;
  propertyGooglePlaceId: string | null;
  propertyAddressComponents: any | null;
  propertyLocation: string | null;
  societyOrLocalityName: string | null;
  buildYear: number | null;
  possessionState: string | null;
  furnishing: string | null;
  totalFloors: number | null;
  floorNumber: number | null;
  carParking: number | null;
  facing: string | null;
  propertyState: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  propertyTypeId: string | null;
  userId: string;
  propertyMarkersLatLng: { lat: string; lng: string }[] | null;
  propertyStatus: string | null;
  bedrooms: number | null;
  bathrooms: number | null;
  rating: string | null;
  review: string | null;
  popularProperty: boolean | null;
  soldAt: string | null;
  propertyPrice: string | null;
  securityDeposit: number | null;
  areaInSqMeters: number | null;
  totalViews: number | null;
  propertyLatitude: number | null;
  propertyLongitude: number | null;
  propertyCategoryId: string | null;
  utilities: {
    id: string;
    utility: string;
    distanceInKm: number;
  }[];
  amenities: {
    id: string;
    name: string;
    fileKey: string;
    filePublicUrl: string;
    cloudinaryUrl: string;
  }[];
  user: {
    company: {};
    companyDetails: {};
  };
  comments: {
    user: {};
  };
  mediaSections: {
    id: string;
    title: string;
    media: {
      id: string;
      fileKey: string;
      filePublicUrl: string;
      cloudinaryUrl: string;
    }[];
  }[];
};

const RecentlyViewed = () => {
  const { data: recentlyViewedProperties, isLoading } =
    api.postProperty.getAllProperties.useQuery();

  function handlePress(id: string): void {
    router.navigate(`/property/${id}`);
  }

  const convertToFavouritesItem = (item: any): FavouritesComponentItemType => {
    return {
      ...item,
      propertyMarkersLatLng: null, // Set to null to avoid type issues
    } as FavouritesComponentItemType;
  };

  return (
    <LinearGradient
      colors={['#FFFBF9', '#FFF9F5', '#B39889']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradient}
    >
      <SafeAreaView style={{ flex: 1 }} edges={['bottom']}>
        <View className="mx-5 flex-1">
          {isLoading ? (
            <ActivityIndicator />
          ) : (
            <FlashList
              showsVerticalScrollIndicator={false}
              data={recentlyViewedProperties}
              estimatedItemSize={210}
              keyExtractor={(item) => item.id.toString()}
              className="flex-1"
              renderItem={({ item }) => (
                <FavouritesComponent
                  item={convertToFavouritesItem(item)}
                  handlePress={() => handlePress(item.id)}
                />
              )}
            />
          )}
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default RecentlyViewed;

const styles = StyleSheet.create({
  gradient: {
    // borderRadius: 32,
    flex: 1,
  },
});
