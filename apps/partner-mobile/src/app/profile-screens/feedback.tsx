import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import {
  Alert,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';

import { Button } from '@/ui/button';

type TextInputFieldProps = {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
};

// text input field function/component
export const TextInputField: React.FC<TextInputFieldProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
}) => {
  return (
    <View className="pb-8">
      <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
        {label}
      </Text>
      <TextInput
        placeholder={placeholder}
        multiline={true}
        className="rounded-xl border border-[#ECE9E8] px-5 py-3.5"
        style={{ maxHeight: 62 }}
        value={value}
        onChangeText={onChangeText}
      />
    </View>
  );
};

type StarRatingProps = {
  onRatingSelected: (rating: number) => void;
};

// star rating function/component
export const StarRating: React.FC<StarRatingProps> = ({ onRatingSelected }) => {
  const [rating, setRating] = useState(0);

  const handlePress = (index: number) => {
    setRating(index + 1); // Update the rating
    if (onRatingSelected) {
      onRatingSelected(index + 1); // Call the callback with the new rating
    }
  };

  return (
    <View className="flex-row items-center">
      {Array.from({ length: 5 }, (_, index) => (
        <Pressable key={index} onPress={() => handlePress(index)}>
          <NImage
            source={require('../../../assets/icons/yostar.png')}
            className={`mr-6 h-10 w-10 ${index < rating ? 'opacity-100' : 'opacity-20'}`} // Adjust opacity based on rating
          />
        </Pressable>
      ))}
    </View>
  );
};

// agent feedback options select function/component
export const FeedbackOptions = () => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const options = [
    'Easy to communicate',
    'Helpful',
    'Understood my needs',
    'Good Behavior',
  ];

  return (
    <View className="flex-row flex-wrap items-center">
      {options.map((option) => (
        <Pressable
          key={option}
          onPress={() => setSelectedOption(option)}
          className={`mb-2.5 mr-4 self-start rounded-xl px-5 py-2.5 ${
            selectedOption === option ? 'bg-primary-100' : 'bg-white'
          }`}
        >
          <Text
            className={`font-airbnb_bk text-sm font-normal ${
              selectedOption === option ? 'text-primary-850' : 'text-text-600'
            }`}
          >
            {option}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

// eslint-disable-next-line max-lines-per-function
const Feedback: React.FC = () => {
  const handleRatingSelected = (rating: any) => {
    Alert.alert(`You rated: ${rating} star(s)`); // Handle the rating as needed
  };

  return (
    <LinearGradient
      colors={['#FFFBF9', '#FFF9F5', '#B39889']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradient}
    >
      <View className="flex-1 px-5 pt-6">
        <TextInputField
          label={'Agent Name'}
          placeholder={'enter your name here'}
        />
        <TextInputField
          label={'Agent’s Company Name'}
          placeholder={'enter your company name here'}
        />

        <View className="pb-8">
          <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
            Rate the agent
          </Text>
          <View className="flex-row items-center">
            <StarRating onRatingSelected={handleRatingSelected} />
          </View>
        </View>

        <View className="pb-8">
          <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
            What you like about the agent?
          </Text>
          <FeedbackOptions />
        </View>

        <TextInputField
          label={'Any comment or review regarding agent?'}
          placeholder={'enter your comments here'}
        />
        {/* Bottom Button */}
        <View className="mb-6 flex-1">
          {Platform.OS === 'ios' ? (
            <BlurView
              intensity={15}
              tint="light"
              blurReductionFactor={4}
              experimentalBlurMethod="dimezisBlurView"
              className="absolute bottom-0 w-full flex-1 flex-col justify-end bg-primary-50/30 px-5 py-3"
            >
              <Button label={'Send Feedback'} />
            </BlurView>
          ) : Platform.OS === 'android' ? (
            <View className="absolute bottom-0 w-full flex-1 flex-col justify-end bg-primary-50 px-5 py-3">
              <Button label={'Send Feedback'} />
            </View>
          ) : null}
        </View>
      </View>
    </LinearGradient>
  );
};

export default Feedback;

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
    height: 100,
  },
  blurContainer: {
    flex: 1,
    justifyContent: 'center',
  },
});
