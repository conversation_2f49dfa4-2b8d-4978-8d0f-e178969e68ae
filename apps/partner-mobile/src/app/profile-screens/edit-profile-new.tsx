import {
  View,
  ScrollView,
  Pressable,
  Text,
  Alert,
  Platform,
  Dimensions,
  RefreshControl,
  ImageSourcePropType,
  StyleSheet,
  ActivityIndicator,
  findNodeHandle,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { Button, ControlledInput } from '@/ui';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Href, router } from 'expo-router';
import Entypo from '@expo/vector-icons/Entypo';
import { Image as NImage, ImageBackground } from 'expo-image';
import { BlurView } from 'expo-blur';
import { Video, ResizeMode } from 'expo-av';
import { formatDistanceToNow } from 'date-fns';
import { Post } from '../home-top-tabs/feed';
import AgentInfoCard from '@/components/agent-info-card';
import LikeAgentButton from '@/components/agent-like-share/agent-like-btn';
import PropertiesComponent from '@/components/properties-component/properties-component';
import UserPic from '@/components/user-pic';
import { api } from '@/utils/api';
import { Card, colorGradients } from './stats';
import * as ImagePicker from 'expo-image-picker';
import PhotoOptionsModal from './photo-options-modal';
import { showMessage } from 'react-native-flash-message';
import { useVideoPlayer, VideoView } from 'expo-video';
import { useForm } from 'react-hook-form';
import Language from '@/components/edit-profile/language';
import OperationalAreas from '@/components/edit-profile/operational-areas';
import Bio from '@/components/edit-profile/bio';
import { signedUploadToCloudinary } from '@/api/cloudinary';

const { height, width } = Dimensions.get('screen');

interface CloudinaryImageData {
  url: string;
  publicId: string;
}

const Stats = ({
  value,
  label,
  img,
}: {
  value: string | number;
  label: string;
  img: ImageSourcePropType;
}) => {
  return (
    <View className="items-center">
      <Text className="text-lg font-semi-bold font-airbnb_bd text-primary-700">
        {value}
      </Text>
      <View className="mt-1.5 flex-row items-center">
        <NImage source={img} className="h-5 w-5" />
        <Text className="ml-1 text-sm font-airbnb_bk font-normal text-text-500">
          {label}
        </Text>
      </View>
    </View>
  );
};

const VideoPlayer = ({ url }: { url: string }) => {
  const player = useVideoPlayer(url, (player) => {
    player.loop = false;
    player.play();
  });

  return (
    <VideoView
      style={{ height: '100%', width: '100%' }}
      player={player}
      allowsFullscreen
      allowsPictureInPicture
    />
  );
};

const EditProfileNew = () => {
  const insets = useSafeAreaInsets();
  const [selected, setSelected] = useState<'post' | 'property'>('post');
  const scrollViewRef = useRef<ScrollView | null>(null);
  const scrollToRef = useRef<View | null>(null);
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [modalEditProfileVisible, setModalEditProfileVisible] = useState(false);
  const [modalBgImageVisible, setModalBgImageVisible] = useState(false);

  // Track upload states separately for profile and background images
  const [isProfileImageUploading, setIsProfileImageUploading] = useState(false);
  const [isBgImageUploading, setIsBgImageUploading] = useState(false);

  // API mutations for image handling
  const utils = api.useUtils();
  const awsPreSignedUrlMutation = api.aws.getPresignedUrl.useMutation();
  const awsGetPublicUrlMutation = api.aws.getPublicFileUrl.useMutation();
  const userImageMutation = api.user.createUserMedia.useMutation();
  const deleteUserImageMutation = api.user.deleteUserMedia.useMutation();
  const [isUploading, setIsUploading] = useState(false);
  // profile data
  const profile = api.user.profile.useQuery();
  const profileData = profile?.data;
  const agentId = profileData?.id;

  //new apis

  // Add updateUserProfileMedia mutation
  const updateUserProfileMediaMutation =
    api.user.updateUserProfileImage.useMutation({
      onSuccess: ({ message }) => {
        showMessage({
          message,
          type: 'success',
        });
        void utils.invalidate();
      },
      onError: ({ message }) => {
        showMessage({
          message: message,
          type: 'danger',
        });
      },
    });
  const updateUserBgMediaMutation = api.user.updateUserBgImage.useMutation({
    onSuccess: ({ message }) => {
      showMessage({
        message,
        type: 'success',
      });
    },
    onError: ({ message }) => {
      showMessage({
        message: message,
        type: 'danger',
      });
    },
  });

  const timestampRef = useRef(Math.round(Date.now() / 1000));
  const { data: signatureData } = api.cloudinary.generateSignature.useQuery({
    paramsToSign: {
      timestamp: timestampRef.current,
      folderFor: 'users',
      forlderPurpose: 'profile',
    },
  });
  const [cloudinaryImage, setCloudinaryImage] = useState<CloudinaryImageData>({
    // Prioritize Cloudinary URL if available
    url: '',
    publicId: '',
  });

  // Update cloudinaryImage when profile data is loaded
  useEffect(() => {
    if (profile.data) {
      console.log('[DEBUG] Profile loaded, initializing image data');

      // Access these properties safely with type assertions
      const profileImagePublicUrl = profile.data.cloudinaryProfileImageUrl;
      const cloudinaryPublicId = profile.data.cloudinaryProfileImagePublicId;

      console.log('[DEBUG] profileImagePublicUrl:', profileImagePublicUrl);

      // Prioritize Cloudinary URL if available
      const imageUrl = profileImagePublicUrl || '';
      const publicId = cloudinaryPublicId || '';

      setCloudinaryImage({
        url: imageUrl,
        publicId: publicId,
      });
    }
  }, [profile.data]);

  // agent details
  const { data: agentDetails } = api.homePage.getAgentDetails.useQuery(
    {
      id: agentId ?? '',
    },
    {
      enabled: !!agentId,
    },
  );

  // check if agent profile or not
  const isAgentProfile = agentId === agentDetails?.id;

  // testimonial data n video player ref
  const videoRef = useRef<Video>(null);
  const { data: agentReviews } = api.agentReviews.getAgentReviews.useQuery({
    agentId: agentId ?? '',
  });

  const propertyDetails = [
    {
      title: 'Website:',
      icon: require('../../../assets/icons/web.png'),
      value: profileData?.company?.companyWebsiteLink ?? 'N/A',
    },
    {
      title: 'Location:',
      icon: require('../../../assets/icons/location.png'),
      value: profileData?.company?.companyLocation ?? 'N/A',
    },
    {
      title: 'Mobile:',
      value: profileData?.company?.phoneNumber ?? 'N/A',
    },
    {
      title: 'Email:',
      value: profileData?.email ?? 'N/A',
    },
    {
      title: 'Fax:',
      value: profileData?.company?.fax ?? 'N/A',
    },
  ];

  const handlePropertyPress = () => {
    setSelected('property');
    if (scrollToRef.current && scrollViewRef.current) {
      const scrollHandle = findNodeHandle(scrollViewRef.current);
      if (scrollHandle) {
        scrollToRef.current.measureLayout(scrollHandle, (x, y) => {
          scrollViewRef.current?.scrollTo({ y, animated: true });
        });
      }
    }
  };

  const handlePostPress = () => {
    setSelected('post');
    if (scrollToRef.current && scrollViewRef.current) {
      const scrollHandle = findNodeHandle(scrollViewRef.current);
      if (scrollHandle) {
        scrollToRef.current.measureLayout(scrollHandle, (x, y) => {
          scrollViewRef.current?.scrollTo({ y, animated: true });
        });
      }
    }
  };

  const requestPermissions = async () => {
    const { status: cameraStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: libraryStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
      Alert.alert(
        'Permission denied',
        'Sorry, we need camera and photo permissions to make this work!',
      );
      return false;
    }
    return true;
  };

  const pickImage = async () => {
    try {
      // Request permissions first
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        showMessage({
          message:
            'Sorry, we need camera roll permissions to change the background image.',
          type: 'warning',
        });
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [16, 9],
        quality: 1,
      });

      if (!result.canceled) {
        const file = result.assets[0];
        if (!file) {
          return;
        }

        // Determine which upload function to use based on which modal is active
        if (modalEditProfileVisible) {
          await uploadUserProfileImage(file.uri);
        } else if (modalBgImageVisible) {
          await uploadUserBgImage(file.uri);
        }
      }
    } catch (error) {
      showMessage({
        message: 'Error picking image',
        type: 'danger',
      });
    }
  };

  const takePhoto = async () => {
    if (!(await requestPermissions())) return;

    let result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled) {
      const file = result.assets[0];
      if (!file) {
        return;
      }

      // Determine which upload function to use based on which modal is active
      if (modalEditProfileVisible) {
        await uploadUserProfileImage(file.uri);
      } else if (modalBgImageVisible) {
        await uploadUserBgImage(file.uri);
      }
    }

    // Close both modals after taking photo
    setModalEditProfileVisible(false);
    setModalBgImageVisible(false);
  };

  const uploadUserProfileImage = async (uri: string) => {
    try {
      setIsProfileImageUploading(true);
      setModalEditProfileVisible(false);

      if (!signatureData || !signatureData.signature) {
        throw new Error('Signature not available');
      }
      // Upload the new image
      const result = await signedUploadToCloudinary({
        imageUri: uri,
        folder: signatureData.uploadFolderUrl,
        signature: signatureData.signature,
        timestamp: timestampRef.current.toString(),
        preset: signatureData.cloudPreset,
        apiKey: signatureData.apiKey,
        cloudName: signatureData.cloudName,
      });

      // Update state with new image data
      const newImageData = {
        url: result.secure_url,
        publicId: result.public_id,
      };
      setCloudinaryImage(newImageData);
      // Call the updateUserProfileMedia mutation immediately after upload
      await updateUserProfileMediaMutation.mutateAsync({
        cloudinaryUrl: result.secure_url,
        cloudinaryId: result.public_id,
      });
    } catch (e) {
      showMessage({
        message: 'Error uploading user image',
        type: 'danger',
      });
    } finally {
      setIsProfileImageUploading(false);
    }
  };

  const uploadUserBgImage = async (uri: string) => {
    try {
      setIsBgImageUploading(true);
      setModalBgImageVisible(false);

      if (!signatureData || !signatureData.signature) {
        throw new Error('Signature not available');
      }
      // Upload the new image
      const result = await signedUploadToCloudinary({
        imageUri: uri,
        folder: signatureData.uploadFolderUrl,
        signature: signatureData.signature,
        timestamp: timestampRef.current.toString(),
        preset: signatureData.cloudPreset,
        apiKey: signatureData.apiKey,
        cloudName: signatureData.cloudName,
      });

      // Update state with new image data
      const newImageData = {
        url: result.secure_url,
        publicId: result.public_id,
      };
      setCloudinaryImage(newImageData);
      // Call the updateUserProfileMedia mutation immediately after upload
      await updateUserBgMediaMutation.mutateAsync({
        purpose: 'update',
        cloudinaryId: result.public_id,
        cloudinaryUrl: result.secure_url,
      });
    } catch (e) {
      showMessage({
        message: 'Error uploading background image',
        type: 'danger',
      });
    } finally {
      setIsBgImageUploading(false);
    }
  };

  const deleteUserBgImage = async () => {
    try {
      await updateUserBgMediaMutation.mutateAsync({
        purpose: 'delete',
      });
      showMessage({
        message: 'Background Image Deleted Successfully',
        type: 'success',
      });
      setModalBgImageVisible(false);
    } catch (e) {
      showMessage({
        message: 'Error deleting background image',
        type: 'danger',
      });
    }
  };

  // states for changes yo yo yo
  const [bioModalVisible, setBioModalVisible] = useState(false);
  const [operationAreasModalVisible, setOperationAreasModalVisible] =
    useState(false);

  // edit bio
  const { mutate: editBio } = api.user.bioUpdate.useMutation({
    onSuccess: (data) => {
      showMessage({
        message: 'Bio updated successfully',
      });
      utils.user.getProfileWithoutSensitiveData.invalidate();
    },
    onError: (error: { message: any }) => {
      showMessage({
        message: error.message || 'Error updating bio',
        type: 'danger',
      });
    },
  });

  const { control, handleSubmit } = useForm({
    defaultValues: {
      bio: profileData?.bio || '',
    },
  });

  const onSubmitBio = (data: { bio: string }) => {
    editBio({ bio: data.bio });
    setBioModalVisible(false);
  };

  return (
    <View className="flex-1 bg-[#FFFAF9]" style={{ paddingTop: insets.top }}>
      {/* back button */}
      <BlurView
        intensity={10}
        tint="light"
        className="bg-white/30 w-full px-5 pt-2.5 pb-3"
      >
        <Pressable
          className="self-start bg-primary-100 rounded-full p-2.5"
          onPress={() => router.back()}
        >
          <Entypo name="chevron-thin-left" size={18} color="#252525" />
        </Pressable>
      </BlurView>

      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        className="mb-5"
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        <View className="mt-6 px-5 relative">
          {/* Agent Profile */}
          <View
            className="relative rounded-xl overflow-hidden bg-white"
            style={[
              { borderBottomLeftRadius: 12, borderBottomRightRadius: 12 },
            ]}
          >
            <View className="relative">
              {isBgImageUploading ? (
                <View
                  style={{
                    height: height * 0.1931,
                    width: width * 0.907,
                    padding: 20,
                    borderTopLeftRadius: 12,
                    borderTopRightRadius: 12,
                    backgroundColor: profileData?.bgFilePublicUrl
                      ? undefined
                      : '#FEF9EA',
                  }}
                  className=" inset-0 bg-gray-700 d items-center justify-center  animate-pulse"
                ></View>
              ) : (
                <ImageBackground
                  className="items-center"
                  source={
                    profileData?.cloudinaryBgImageUrl
                      ? { uri: profileData.cloudinaryBgImageUrl }
                      : profileData?.bgFilePublicUrl
                        ? { uri: profileData.bgFilePublicUrl }
                        : null
                  }
                  style={{
                    height: height * 0.1931,
                    width: width * 0.907,
                    padding: 20,
                    backgroundColor: profileData?.bgFilePublicUrl
                      ? undefined
                      : '#FEF9EA',
                  }}
                  imageStyle={{
                    borderTopLeftRadius: 12,
                    borderTopRightRadius: 12,
                  }}
                  contentFit="cover"
                />
              )}
              <View className="absolute top-0 right-0">
                <Pressable
                  className="self-end bg-secondary-100 rounded-full p-2.5"
                  onPress={() => setModalBgImageVisible(true)}
                >
                  <NImage
                    source={require('../../../assets/icons/cam.png')}
                    className="h-5 w-5"
                  />
                </Pressable>
              </View>

              <View className="absolute bottom-[-24px] left-6">
                <View className="bg-white rounded-full p-1 relative">
                  {isProfileImageUploading ? (
                    <View className=" h-24 w-24 rounded-full bg-gray-500 items-center justify-center rounded-full animate-pulse"></View>
                  ) : (
                    <UserPic
                      picUrl={
                        profileData?.cloudinaryProfileImageUrl ??
                        profileData?.filePublicUrl
                      }
                      size={96}
                      color="#784100"
                      className="h-24 w-24 rounded-full"
                    />
                  )}
                </View>
                <Pressable
                  className="absolute bottom-0 right-0 left-0 z-50 flex-row items-center justify-center rounded-xl bg-primary-0 p-1.5"
                  onPress={() => setModalEditProfileVisible(true)}
                >
                  <NImage
                    source={require('../../../assets/icons/camera.png')}
                    className="mr-1 h-3 w-3"
                  />
                  <Text className="font-airbnb_md text-[10px] font-medium text-primary-750">
                    Add Photo
                  </Text>
                </Pressable>
              </View>
            </View>

            <View
              className="px-4 pb-4 bg-white border-b-[1.5px] border-secondary-100"
              style={{
                borderBottomLeftRadius: 12,
                borderBottomRightRadius: 12,
                marginTop: 24,
              }}
            >
              {/* details */}
              <View>
                <Pressable
                  className="self-end my-3"
                  onPress={() => router.push('/profile-screens/edit-profile')}
                  hitSlop={10}
                >
                  <NImage
                    source={require('../../../assets/icons/edit5.png')}
                    className="h-4 w-4"
                  />
                </Pressable>
                <View className="flex-row items-center justify-between">
                  <View style={{ maxWidth: '60%' }}>
                    <View>
                      <Text
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        className="font-airbnb_xbd text-xl font-extrabold text-secondary-800"
                      >
                        {profileData?.name}
                      </Text>
                      <Text
                        numberOfLines={2}
                        ellipsizeMode="tail"
                        className="mt-1 font-airbnb_md text-sm font-medium text-text-600"
                      >
                        {profileData?.company?.companyName}
                      </Text>
                    </View>
                  </View>

                  {/* rating & review part */}
                  <View style={{ maxWidth: '40%' }} className="ml-auto">
                    <View className="mb-2 flex-row items-center self-end">
                      <NImage
                        source={require('../../../assets/icons/star.png')}
                        className="h-4 w-4"
                      />
                      <Text
                        className="ml-0.5 font-airbnb_md text-sm font-medium text-text-600"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {profileData?.rating ?? 0}
                      </Text>
                    </View>

                    <View className="mt-2">
                      <Text
                        className="font-airbnb_md text-xs font-medium text-text-600"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {profileData?.reviews ?? 0} Reviews
                      </Text>
                    </View>
                  </View>
                </View>

                <View className="mt-2">
                  <Text className="text-sm font-normal font-airbnb_bk text-text-500">
                    {profileData?.city?.name}
                  </Text>
                </View>
              </View>

              {/* Stats */}
              <View className="mt-5 space-y-5">
                <View className="mb-2.5 flex-row items-center justify-between px-4">
                  <Stats
                    value={profileData?.propertiesSold ?? 0}
                    label="Total sales closed"
                    img={require('../../../assets/icons/homeicon.png')}
                  />
                  <Stats
                    value={`${profileData?.experience} yrs`}
                    label="Experience"
                    img={require('../../../assets/icons/experience3.png')}
                  />
                </View>
                <View className="mt-2.5 flex-row items-center justify-between px-4">
                  <Stats
                    value={
                      profileData?.createdAt
                        ? formatDistanceToNow(new Date(profileData.createdAt)) +
                          ' ago'
                        : 'N/A'
                    }
                    label="Active"
                    img={require('../../../assets/icons/clock.png')}
                  />
                  <Stats
                    value={
                      (profileData?.receivedConnectionRequests?.length ?? 0) +
                      (profileData?.sentConnectionRequests?.length ?? 0) +
                      (profileData?.coustomerConnections?.length ?? 0)
                    }
                    label="Connections"
                    img={require('../../../assets/icons/handshake.png')}
                  />
                </View>
              </View>
            </View>
          </View>

          {/* charts*/}
          <View className="mt-3 py-3 bg-white rounded-xl border-[1.5px] border-secondary-100">
            <View className="flex-row flex-wrap items-center justify-center gap-3">
              <Card
                color={colorGradients[0]}
                label="Property Visitors"
                value={0}
                previousValue={0}
              />

              <Card
                color={colorGradients[1]}
                label="Total Responses"
                value={0}
                previousValue={0}
              />

              <Card
                color={colorGradients[2]}
                label="Network Growth"
                value={0}
                previousValue={0}
              />

              <Card
                color={colorGradients[3]}
                label="/5 Client Satisfaction"
                value={0}
                previousValue={0}
              />
            </View>

            <View className="my-3 h-[0.1px] bg-secondary-100 rounded-full" />

            <Pressable
              className="items-center justify-center"
              onPress={() => router.push('/profile-screens/stats')}
            >
              <Text className="font-airbnb_md text-lg font-medium text-primary-750">
                Show all analystics
              </Text>
            </Pressable>
          </View>

          <View className="mt-3 py-4 border-[1.5px] rounded-xl border-secondary-100 bg-white">
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="px-4 font-airbnb_bd text-lg font-bold text-primary-750"
            >
              Activity
            </Text>
            <Text className="my-3 h-[0.1px] bg-[#E0DFDC] rounded-full" />

            {/* switchable button */}
            <View className="my-5 w-2/3 px-4 flex-row items-center justify-center">
              <View className="flex-row items-center rounded-lg bg-[#f1f1f1] p-1">
                <Pressable
                  className={`flex-1 items-center rounded-lg px-6 py-2.5 ${selected === 'post' ? 'bg-primary-500' : 'bg-transparent'}`}
                  onPress={handlePostPress}
                >
                  <Text
                    className={`font-airbnb_md text-sm font-medium ${selected === 'post' ? 'text-primary-800' : 'text-text-500'}`}
                  >
                    Post
                  </Text>
                </Pressable>

                <Pressable
                  className={`flex-1 items-center rounded-lg px-6 py-2.5 ${selected === 'property' ? 'bg-primary-500' : 'bg-transparent'}`}
                  onPress={handlePropertyPress}
                >
                  <Text
                    className={`font-airbnb_md text-sm font-medium ${selected === 'property' ? 'text-primary-800' : 'text-text-500'}`}
                  >
                    Property
                  </Text>
                </Pressable>
              </View>
            </View>

            {/* post content */}
            {selected === 'post' && (
              <View ref={scrollToRef}>
                <Text className="mb-4 pl-5 font-airbnb_md text-sm font-medium text-text-600">
                  Posts ({agentDetails?.posts?.length ?? 0})
                </Text>

                {agentDetails?.posts?.length === 0 ? (
                  <Text className="pl-5 font-airbnb_bk text-sm font-normal text-text-600">
                    No posts listed
                  </Text>
                ) : (
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ paddingHorizontal: 4 }}
                    bounces={false}
                  >
                    <View className="flex-row">
                      {agentDetails?.posts?.map((item: any) => (
                        <View
                          key={item.id}
                          style={{
                            width: width * 0.8,
                            marginRight: 10,
                          }}
                          className="flex-1"
                        >
                          <Post post={item} />
                        </View>
                      ))}
                    </View>
                  </ScrollView>
                )}
              </View>
            )}

            {/* property content */}
            {selected === 'property' && (
              <View ref={scrollToRef}>
                <Text className="pl-5 font-airbnb_md text-sm font-medium text-text-600">
                  Properties ({agentDetails?.properties?.length ?? 0})
                </Text>

                {agentDetails?.properties?.length === 0 ? (
                  <Text className="pl-5 font-airbnb_bk text-sm font-normal text-text-600">
                    No properties listed
                  </Text>
                ) : (
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ paddingHorizontal: 4 }}
                    bounces={false}
                  >
                    <View className="flex-row">
                      {agentDetails?.properties?.map((item: any) => (
                        <View
                          key={item.id}
                          style={{
                            width: width * 0.8,
                            marginRight: 10,
                          }}
                          className="flex-1"
                        >
                          <PropertiesComponent
                            item={item}
                            handlePress={() =>
                              router.push({
                                pathname: '/property/[propertyId]',
                                params: {
                                  propertyId: item.id,
                                },
                              })
                            }
                            isLiked={!!item.customerFavourites.length}
                            isAgentProfile={isAgentProfile}
                          />
                        </View>
                      ))}
                    </View>
                  </ScrollView>
                )}
              </View>
            )}
          </View>

          {/* bio */}
          <Bio bio={profileData?.bio ?? ''} />

          {/* about company */}
          {profileData?.company?.about ? (
            <View className="mt-5">
              <AgentInfoCard<true>
                heading={'About Company'}
                description={profileData?.company?.about ?? 'N/A'}
                companyDetails={propertyDetails}
                onEdit={() => router.push('/profile-screens/about-company')}
              />
            </View>
          ) : (
            <View className="mt-5">
              <AgentInfoCard<true>
                heading={'About Company'}
                description={'No company information available'}
                companyDetails={propertyDetails}
                onAdd={() => router.push('/profile-screens/about-company')}
              />
            </View>
          )}

          {/* operational areas */}
          <OperationalAreas />

          {/* languages */}
          <Language />

          {/* Testimonials */}
          {agentReviews && agentReviews.length > 0 && (
            <View className="mt-5 py-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
              <Text className="mb-3 px-4 text-lg font-airbnb_bd font-bold text-primary-750">
                Users Testimonials
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingHorizontal: 16 }}
              >
                {agentReviews.map((review, index: number) => (
                  <Pressable
                    key={index}
                    className="mr-4 rounded-xl overflow-hidden aspect-[9/16] object-cover"
                    style={{ height: 460, width: 316 }}
                  >
                    <View className="h-full w-full bg-black/50 rounded-xl shadow-md relative object-cover">
                      {/* Video Player yo*/}
                      <View className="justify-center items-center relative">
                        {review.cloudinaryUrl ? (
                          <>
                            <VideoPlayer url={review.cloudinaryUrl} />
                            {isLoading[review.id] && (
                              <View className="absolute top-0 left-0 right-0 bottom-0 items-center justify-center bg-black/30">
                                <ActivityIndicator
                                  size="large"
                                  color="#F04D24"
                                />
                              </View>
                            )}
                          </>
                        ) : review.filePublicUrl ? (
                          <>
                            <VideoPlayer url={review.filePublicUrl} />
                            {isLoading[review.id] && (
                              <View className="absolute top-0 left-0 right-0 bottom-0 items-center justify-center bg-black/30">
                                <ActivityIndicator
                                  size="large"
                                  color="#F04D24"
                                />
                              </View>
                            )}
                          </>
                        ) : (
                          <Text className="text-center text-text-500 font-airbnb_md text-base font-medium">
                            Not Available now
                          </Text>
                        )}
                      </View>

                      {/* user info yo*/}
                      <View className="p-4 bg-transparent absolute bottom-0 left-0 right-0">
                        <View className="flex-row items-center">
                          <NImage
                            key={review?.ratedBy?.profileImageKey}
                            source={
                              review?.ratedBy?.cloudinaryImagePublicUrl
                                ? {
                                    uri: review.ratedBy
                                      .cloudinaryImagePublicUrl,
                                  }
                                : review?.ratedBy?.profileImagePublicUrl
                                  ? {
                                      uri: review.ratedBy.profileImagePublicUrl,
                                    }
                                  : require('@assets/icons/default-user.png')
                            }
                            className="h-10 w-10 rounded-full"
                          />
                          <View className="ml-2">
                            <Text className="font-airbnb_bd text-base font-bold text-white">
                              {review?.ratedBy?.name || 'N/A Customer Name'}
                            </Text>
                            <View className="flex-row items-center">
                              <NImage
                                source={require('@assets/icons/star.png')}
                                className="h-3 w-3 mr-1"
                              />
                              <Text className="font-airbnb_md text-xs text-text-100">
                                {review?.userStarsCount || 'N/A'}
                              </Text>
                              <Text
                                className="max-w-xs ml-2 font-airbnb_md font-medium text-sm text-text-100"
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                {review?.connection?.customer?.city?.name ||
                                  'N/A city'}
                              </Text>
                            </View>
                          </View>
                        </View>
                        <Text
                          className="mt-2 self-start font-airbnb_md text-center text-text-100"
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        >
                          {review?.userRatingMessage || ''}
                        </Text>
                      </View>
                    </View>
                  </Pressable>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
      </ScrollView>
      <PhotoOptionsModal
        visible={modalEditProfileVisible}
        onClose={() => setModalEditProfileVisible(false)}
        onChoosePhoto={pickImage}
        onTakePhoto={takePhoto}
        // onRemovePhoto={}
      />

      {/* bg image modal */}
      <PhotoOptionsModal
        visible={modalBgImageVisible}
        onClose={() => setModalBgImageVisible(false)}
        onChoosePhoto={pickImage}
        onTakePhoto={takePhoto}
        onRemovePhoto={deleteUserBgImage}
      />
    </View>
  );
};

export default EditProfileNew;

const styles = StyleSheet.create({
  Shadow: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
});
