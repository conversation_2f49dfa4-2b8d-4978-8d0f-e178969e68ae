import {
  Alert,
  Pressable,
  StyleSheet,
  Text,
  View,
  RefreshControl,
} from 'react-native';
import React, { useRef, useState, useEffect } from 'react';
import { ScrollView, TextInput } from 'react-native-gesture-handler';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker'; // Add this import
import * as DocumentPicker from 'expo-document-picker';
import { Button } from '@/ui';
import { FlashList } from '@shopify/flash-list';
import { router, useRouter } from 'expo-router';
import { api } from '@/utils/api';
import UserPic from '@/components/user-pic';
import { formatTime } from '@/utils/format-terms';
import VideoComponent from '@/ui/video-component';
import z from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { showMessage } from 'react-native-flash-message';
import { FeedType } from '@/types';
import {
  signedUploadToCloudinary,
  signedVideoUploadToCloudinary,
} from '@/api/cloudinary/upload';
import PostOptionsMenu from '@/components/post-options-menu';
import ReportPostModal from '@/components/report-post-modal';
import { useModal } from '@/ui/modal';

interface CloudinaryImageData {
  url: string;
  publicId: string;
}

const NewPostSchema = z.object({
  content: z.string().min(5, { message: 'Atleast 5 characters required' }),
  mediaFileKey: z.string().optional(),
  mediaPublicUrl: z.string().optional(),
  mediaType: z.enum(['VIDEO', 'IMAGE']).optional(),
});

export const CommentBottomStrip = React.memo(
  ({
    postId,
    likedId,
    totalComments,
    totalLikes,
    refetch,
  }: {
    postId: string;
    likedId: string;
    totalComments: number;
    totalLikes: number;
    refetch?: () => void;
  }) => {
    const [comment, setComment] = useState('');
    const { mutate: likePost } = api.social.likePost.useMutation();
    const { mutate: newComment } = api.social.newComment.useMutation();
    const [liked, setLiked] = useState(!!likedId);
    const utils = api.useUtils();

    const handleLikeClick = () => {
      likePost(
        { postId: postId },
        {
          onSuccess: (resp) => {
            utils.social.invalidate();
            showMessage({
              message: resp.message,
              type: 'success',
            });
            setLiked(resp.isLiked);
          },
          onError: (opts) => {
            showMessage({
              message: opts.message,
              type: 'danger',
            });
          },
        },
      );
    };

    const handleComment = () => {
      if (comment.trim().length === 0) {
        showMessage({
          message: 'Please enter something.',
          type: 'danger',
        });
        return;
      }
      newComment(
        { comment: comment, postId: postId },
        {
          onSuccess: () => {
            utils.social.invalidate();

            setComment('');
            refetch && refetch();
            // void trcpUtils.social.invalidate();
          },
          onError: (opts) => {
            showMessage({
              message: opts.message,
              type: 'danger',
            });
          },
        },
      );
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleComment();
      }
    };

    return (
      <>
        <View className="w-full flex-row items-center justify-between">
          <Pressable
            className="flex-row items-center"
            onPress={handleLikeClick}
          >
            <Image
              source={
                liked
                  ? require('../../../assets/icons/like1.png')
                  : require('../../../assets/icons/like2.png')
              }
              className="mr-2 h-5 w-5"
            />
            <Text
              className={`font-airbnb_bk font-normal text-sm ${
                // item.type === 'display'
                //   ? 'text-text-500'
                liked ? 'text-primary-main700' : 'text-text-500'
              }`}
            >
              Like
            </Text>
          </Pressable>
          <View
            className="flex-row items-center"
            // onPress={item.type === 'action' ? item.onPress : undefined}
          >
            <Text className="font-airbnb_bk font-normal text-sm text-primary-main700">
              {totalLikes} likes
            </Text>
          </View>
          <View
            className="flex-row items-center"
            // onPress={item.type === 'action' ? item.onPress : undefined}
          >
            <Text className="font-airbnb_bk font-normal text-sm text-text-500">
              {totalComments} comments
            </Text>
          </View>
        </View>
        <View
          className={`mt-2.5 px-3 flex-row items-center justify-between border border-text-100 rounded-lg`}
        >
          <TextInput
            className="p-3 flex-1"
            placeholder="Add a comment"
            value={comment}
            onChangeText={setComment}
          />
          <Pressable onPress={handleComment}>
            <Image
              source={require('../../../assets/icons/send.png')}
              className="h-4 w-4"
              tintColor={'#5F2800'}
            />
          </Pressable>
        </View>
      </>
    );
  },
);

export const Post = React.memo(({ post }: { post: FeedType }) => {
  const { data: profile } = api.user.profile.useQuery();
  const reportModal = useModal();
  const isOwnPost = profile?.id === post.user?.id;

  const handleReportPress = () => {
    reportModal.present();
  };

  return (
    <>
      <Pressable
        className={`mb-3.5 p-3 border border-text-100 rounded-xl`}
        onPress={() => router.push(`/home-top-tabs/${post.id}`)}
      >
        {/* Post heading */}
        <View className="mb-2 flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            <View className="mr-3 rounded-full self-start">
              <UserPic
                picUrl={
                  post.user?.cloudinaryProfileImageUrl ?? post.user?.filePublicUrl
                }
                color="#F04D24"
                size={54}
                className="mr-2.5 aspect-square h-16 rounded-full"
              />
            </View>
            <View className="flex-1">
              <Text className="mb-[0.25px] font-bold font-airbnb_bd text-sm text-text-600">
                {post.user?.name}
              </Text>
              <Text className="mb-0.5 font-normal font-airbnb_bk text-[10px] text-primary-800">
                {post.user?.company?.companyName}
              </Text>
              <Text className="font-normal font-airbnb_bk text-[10px] text-text-500">
                {formatTime(post.createdAt)}
              </Text>
            </View>
          </View>

          {/* Post Options Menu */}
          <View
            onTouchEnd={(e) => e.stopPropagation()}
          >
            <PostOptionsMenu
              postId={post.id}
              isOwnPost={isOwnPost}
              onReportPress={handleReportPress}
            />
          </View>
        </View>

      {/* post */}
      <View className="items-center justify-center">
        <Text
          className="mb-3 font-airbnb_bk font-normal text-sm text-text-500 w-full"
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {post.content}
        </Text>
        {post.media[0] &&
        post.media[0].mediaType === 'IMAGE' &&
        post.media[0].cloudinaryUrl ? (
          <Image
            source={{ uri: post.media[0].cloudinaryUrl }}
            className="mb-4 h-[206px] w-full"
          />
        ) : post.media[0] &&
          post.media[0].mediaType === 'IMAGE' &&
          post.media[0].filePublicUrl ? (
          <Image
            source={{ uri: post.media[0].filePublicUrl }}
            className="mb-4 h-[206px] w-full"
          />
        ) : null}

        {post.media[0] &&
        post.media[0].mediaType === 'VIDEO' &&
        post.media[0].cloudinaryUrl ? (
          <VideoComponent
            videoSource={post.media[0].cloudinaryUrl}
            // className="mb-4 h-[206px] w-full"
          />
        ) : post.media[0] &&
          post.media[0].mediaType === 'VIDEO' &&
          post.media[0].filePublicUrl ? (
          <VideoComponent
            videoSource={post.media[0].filePublicUrl}
            // className="mb-4 h-[206px] w-full"
          />
        ) : null}
        <CommentBottomStrip
          postId={post.id}
          likedId={post.likes[0]?.id}
          totalComments={post.totalComments}
          totalLikes={post.totalLikes}
        />
      </View>
    </Pressable>

    {/* Report Post Modal */}
    <ReportPostModal ref={reportModal.ref} postId={post.id} />
  </>
  );
});

const Feed = React.memo(() => {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const {
    data,
    isLoading,
    isSuccess,
    refetch,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = api.social.getFeed.useInfiniteQuery(
    { limit: 10 },
    {
      getNextPageParam: (lastpage) => lastpage.nextCursor,
    },
  );
  const { data: profile } = api.user.profile.useQuery();
  const { mutate: createNewPost, isPending } =
    api.social.createNewPost.useMutation();
  const { mutate: deleteTemporaryMedia } =
    api.social.deleteTemporaryMedia.useMutation();
  const trpcUtils = api.useUtils();

  // Create a stable timestamp for the signature query
  const timestampRef = useRef(Math.round(Date.now() / 1000));

  // Fetch signature for Cloudinary upload
  const { data: signatureData } = api.cloudinary.generateSignature.useQuery({
    paramsToSign: {
      timestamp: timestampRef.current,
      folderFor: 'users',
      forlderPurpose: 'posts',
    },
  });

  const [cloudinaryMedia, setCloudinaryMedia] = useState<CloudinaryImageData>({
    url: '',
    publicId: '',
  });

  // Fetch next page when intersection observer is in view
  const loadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // Image/Video Picker
  const pickMedia = async (mediaType: 'IMAGE' | 'VIDEO') => {
    // Check if there's existing media and it's a different type
    const currentMediaType = form.getValues('mediaType');
    if (cloudinaryMedia.publicId && currentMediaType !== mediaType) {
      // Delete the existing media first
      try {
        await deleteTemporaryMedia(
          { cloudinaryId: cloudinaryMedia.publicId },
          {
            onSuccess: () => {
              showMessage({
                message: 'Previous media removed',
                type: 'success',
              });
            },
          },
        );
        // Reset media state
        setCloudinaryMedia({ url: '', publicId: '' });
        form.setValue('mediaFileKey', undefined);
        form.setValue('mediaPublicUrl', undefined);
      } catch (error) {
        showMessage({
          message: 'Failed to remove previous media',
          type: 'danger',
        });
      }
    }

    form.setValue('mediaType', mediaType);
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission denied',
        'Sorry, we need camera permissions to make this work!',
      );
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: mediaType === 'IMAGE' ? ['images'] : ['videos'],
      allowsEditing: true,
      quality: 1,
      selectionLimit: 1,
    });

    if (!result.canceled && result.assets.length > 0) {
      const selectedUri = result.assets[0].uri;
      const file = result.assets[0];
      uploadMediaToCloudinary(file.uri, mediaType);
    }
  };

  const uploadMediaToCloudinary = async (
    fileUri: string,
    mediaType: 'IMAGE' | 'VIDEO',
  ) => {
    try {
      setIsUploading(true);

      if (!signatureData || !signatureData.signature) {
        throw new Error('Signature not available');
      }
      let result;
      // Upload media to Cloudinary
      if (mediaType === 'IMAGE') {
        result = await signedUploadToCloudinary({
          imageUri: fileUri,
          folder: signatureData.uploadFolderUrl,
          signature: signatureData.signature,
          timestamp: timestampRef.current.toString(),
          preset: signatureData.cloudPreset,
          apiKey: signatureData.apiKey,
          cloudName: signatureData.cloudName,
        });
      } else if (mediaType === 'VIDEO') {
        result = await signedVideoUploadToCloudinary({
          videoUri: fileUri,
          folder: signatureData.uploadFolderUrl,
          signature: signatureData.signature,
          timestamp: timestampRef.current.toString(),
          preset: signatureData.cloudPreset,
          apiKey: signatureData.apiKey,
          cloudName: signatureData.cloudName,
        });
      }

      // Update form values with cloudinary data
      if (result && result.secure_url && result.public_id) {
        form.setValue('mediaFileKey', result.public_id);
        form.setValue('mediaPublicUrl', result.secure_url);
      }

      // Store cloudinary media info
      setCloudinaryMedia({
        url: result?.secure_url ?? '',
        publicId: result?.public_id ?? '',
      });
    } catch (error) {
      console.error('Error uploading to Cloudinary:', error);
      showMessage({
        message: 'Failed to upload media',
        type: 'danger',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const form = useForm<z.infer<typeof NewPostSchema>>({
    resolver: zodResolver(NewPostSchema),
    defaultValues: {
      content: '',
      mediaType: 'IMAGE',
    },
  });

  const onSubmit = async (data: z.infer<typeof NewPostSchema>) => {
    createNewPost(data, {
      onSuccess: (opts) => {
        showMessage({
          message: opts.message,
          type: 'success',
        });
        form.reset({
          content: '',
          mediaType: 'IMAGE',
        });
        setCloudinaryMedia({ url: '', publicId: '' });
        trpcUtils.invalidate();
      },
      onError: (opts) => {
        showMessage({
          message: opts.message,
          type: 'danger',
        });
      },
    });
  };

  const mediaPublicUrl = form.watch('mediaPublicUrl');
  const mediaType = form.watch('mediaType');

  const feedItems = data?.pages.flatMap((page) => page.feed) ?? [];

  const handleRemoveMedia = () => {
    // If we have a cloudinary public ID, delete it from cloudinary
    if (cloudinaryMedia.publicId) {
      deleteTemporaryMedia(
        { cloudinaryId: cloudinaryMedia.publicId },
        {
          onSuccess: () => {
            showMessage({
              message: 'Media removed',
              type: 'success',
            });
          },
          onError: (error) => {
            console.error('Error removing media:', error);
            showMessage({
              message: 'Failed to remove media',
              type: 'danger',
            });
          },
        },
      );
    }

    // Reset form values
    form.setValue('mediaFileKey', undefined);
    form.setValue('mediaPublicUrl', undefined);
    form.setValue('mediaType', undefined);
    setCloudinaryMedia({ url: '', publicId: '' });
  };

  return (
    <ScrollView
      className="flex-1"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={isLoading} onRefresh={refetch} />
      }
    >
      <View className="pt-3 flex-1 px-5">
        {/* Post Composer */}
        <View className="mb-3.5 p-4 flex-row items-center bg-[#FFF9F7] border border-secondary-200 rounded-lg">
          <View className="mr-3 border p-1 border-secondary-main700 rounded-full self-start">
            <UserPic
              picUrl={
                profile?.cloudinaryProfileImageUrl ?? profile?.filePublicUrl
              }
              size={52}
              color={'#FFF9F7'}
              className="h-[52px] w-[52px] rounded-full"
            />
          </View>

          <View className="flex-1">
            <Controller
              control={form.control}
              name="content"
              render={({ field }) => (
                <TextInput
                  className="mb-3 px-3 py-4 flex-1 border border-text-100 rounded-lg bg-white"
                  placeholder="Enter your text"
                  placeholderTextColor={'#6B6B6B'}
                  value={field.value}
                  onChangeText={(text) => {
                    console.log('text', text);
                    field.onChange(text);
                  }}
                />
              )}
            />

            {/* selected media */}
            {mediaPublicUrl && (
              <View className="relative">
                <View className="relative">
                  {mediaType === 'IMAGE' ? (
                    <>
                      <Pressable
                        onPress={handleRemoveMedia}
                        className="self-end pb-2 "
                      >
                        <Image
                          source={require('../../../assets/icons/delete.png')}
                          className="h-5 w-5"
                        />
                      </Pressable>
                      <Image
                        source={{ uri: mediaPublicUrl }}
                        className="w-full aspect-square rounded-lg"
                      />
                    </>
                  ) : (
                    <>
                      <Pressable
                        onPress={handleRemoveMedia}
                        className="self-end pb-2 "
                      >
                        <Image
                          source={require('../../../assets/icons/delete.png')}
                          className="h-5 w-5"
                        />
                      </Pressable>
                      <VideoComponent videoSource={mediaPublicUrl} />
                    </>
                  )}
                </View>
              </View>
            )}

            {/* Loading animation */}
            {isUploading && (
              <View className="w-full aspect-square rounded-lg bg-white items-center justify-center mb-3">
                <View className="h-16 w-16 rounded-full bg-gray-200 animate-pulse" />
              </View>
            )}

            <View className="mt-3 flex-row items-center justify-between">
              <View className="flex-row items-center gap-5">
                <Pressable
                  className="flex-row items-center"
                  onPress={() => pickMedia('IMAGE')}
                >
                  <Image
                    source={require('../../../assets/icons/pictures.png')}
                    className="mr-1.5 h-4 w-4"
                  />
                  <Text className="font-airbnb_bk font-normal text-xs text-text-400">
                    Photo
                  </Text>
                </Pressable>
                <Pressable
                  className="flex-row items-center"
                  onPress={() => pickMedia('VIDEO')}
                >
                  <Image
                    source={require('../../../assets/icons/videos.png')}
                    className="mr-1.5 h-5 w-5"
                  />
                  <Text className="font-airbnb_bk font-normal text-xs text-text-400">
                    Video
                  </Text>
                </Pressable>
              </View>

              <Button
                className="w-20 m-0 h-9 rounded-lg"
                label="Post"
                loading={isUploading || isPending}
                onPress={form.handleSubmit(onSubmit, (err) => {
                  console.log(err);
                  showMessage({
                    message: 'Need to have minimum 5 characters',
                    type: 'danger',
                  });
                })}
              />
            </View>
          </View>
        </View>

        {/* Posts */}
        <FlashList
          data={feedItems}
          estimatedItemSize={432}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item) => item.id.toString()}
          renderItem={(item) => {
            const post = item.item;
            return <Post post={post} />;
          }}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
        />
      </View>
    </ScrollView>
  );
});

export default Feed;

const styles = StyleSheet.create({});
