import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  View,
  Animated,
  Platform,
  LayoutChangeEvent,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { BlurView } from 'expo-blur';
import MapView, { Marker } from 'react-native-maps';
import { Image } from 'expo-image';
import { Button, Pressable, Switch } from '@/ui';
import MultiSliderComponent from '@/components/multi-slider';
import { ScrollView } from 'react-native-gesture-handler';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useLocalSearchParams, router } from 'expo-router';
import { androidMapApiKey, iosMapApiKey } from '@/utils/google-api-keys';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { CustomerPropertiesFilterSchema } from '@/utils/form-validators';
import { z } from 'zod';
import BottomSheet, {
  BottomSheetView,
  BottomSheetScrollView,
  BottomSheetFlashList,
} from '@gorhom/bottom-sheet';
import { FlashList } from '@shopify/flash-list';
import { api } from '@/utils/api';
import Entypo from '@expo/vector-icons/Entypo';
import FavouritesComponent from '@/components/favourites-component';
import { formatPriceAddLabels } from '@/utils/format-terms';
import PropertyFilters from '@/components/property-filters';
import { showMessage } from 'react-native-flash-message';
import * as Location from 'expo-location';
import {
  usePropertyFilter,
  usePropertyFiltersStore,
} from '@/store/property-filters.store';
// import { Env } from 'env';

const { height, width } = Dimensions.get('screen');

export default function Map() {
  const { lat, long, setMultipleFilters } = usePropertyFiltersStore();
  const [errorMsg, setErrorMsg] = useState<string | null>(null);

  const getCurrentLocation = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      showMessage({
        message: 'Permission to access location was denied',
        type: 'danger',
      });
      setErrorMsg('Permission to access location was denied');
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    console.log('status', location.coords);
    setMultipleFilters({
      lat: location.coords.latitude.toString(),
      long: location.coords.longitude.toString(),
    });
    // router.setParams({
    //   lat: location.coords.latitude.toString(),
    //   lng: location.coords.longitude.toString(),
    // });
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  console.log('lat, long', lat, long);

  if (errorMsg) {
    return (
      <View className="flex-1 w-full justify-center items-center">
        <Text>{errorMsg}</Text>
      </View>
    );
  }

  if (!lat || !long) {
    return (
      <View className="flex-1 w-full justify-center items-center">
        <ActivityIndicator size={40} color="#f04d24" />
        <Text>Detecting Location</Text>
        <Button
          className="absolute bottom-10 right-6 z-10"
          onPress={getCurrentLocation}
        >
          <FontAwesome6 name="location-crosshairs" size={24} color="white" />
        </Button>
      </View>
    );
  }

  return (
    <View className="flex-1 w-full">
      {/* Map View */}
      <MapChildComponent />
    </View>
  );
}

const MapChildComponent = React.memo(() => {
  // const insets = useSafeAreaInsets();

  const bottomSheetModalRef = useRef<BottomSheet>(null);
  const mapRef = useRef<MapView>(null);
  const [properties, setProperties] = useState<any[]>([]);
  const [selectedObjectId, setSelectedObjectId] = useState<string | null>(null);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const { lat, long, propertyFor, propertyCategory } =
    usePropertyFiltersStore();
  // const { lat, long, propertyFor, propertyCategory } =
  //   useLocalSearchParams<IFilterForm>();

  const handlePresentModal = () => {
    setIsBottomSheetVisible(true);
    bottomSheetModalRef.current?.expand();
    // setIsVisible(true);
  };

  const handleDismissModal = () => {
    bottomSheetModalRef.current?.close();
    // setIsVisible(false);
  };

  const [isVisible, setIsVisible] = useState(false);
  const { data: profile } = api.user.profile.useQuery();

  useEffect(() => {
    if (profile) {
      mapRef.current?.animateToRegion({
        latitude: Number(lat ?? 28.4430837),
        longitude: Number(long ?? 77.0317603),
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      });
    }
  }, [profile]);

  const propertiesAccordingToFiltersQuery =
    api.listing.getPropertiesAccordingToFilters.useInfiniteQuery(
      {
        take: 100,
        propertyFor: propertyFor,
        propertyCategory: propertyCategory,
        // page: 1,
      },
      {
        getNextPageParam: (lastPage) => lastPage.nextCursor,
        // initialCursor: 1, // <-- optional you can pass an initialCursor
      },
      // {
      //   enabled: !!profile && isPropertySelected,
      // },
    );

  // const { data: resultAgents, isLoading: isLoadingAgents } =
  //   api.listing.getAgentsAccordingTofilters.useQuery(
  //     {
  //       take: 10,
  //       page: 1,
  //     },
  //     {
  //       enabled: !!profile && !isPropertySelected,
  //     },
  //   );

  // useEffect(() => {
  //   bottomSheetModalRef.current?.present();
  //
  //   return () => {
  //     bottomSheetModalRef.current?.dismiss();
  //   };
  // }, []);
  // console.log('resultAgents', resultAgents?.agents);

  const totalResults =
    propertiesAccordingToFiltersQuery?.data?.pages?.flatMap(
      (p) => p.totalResults,
    )?.[0] ?? 0;

  useEffect(() => {
    if (propertiesAccordingToFiltersQuery.isLoading) {
      return;
    }

    const tempProperties =
      propertiesAccordingToFiltersQuery.data?.pages?.flatMap(
        (page) => page.properties,
      ) ?? [];

    setProperties(tempProperties);

    if (
      tempProperties.filter((p) => {
        p.propertyLatitude && p.propertyLongitude;
      }).length === 0
    ) {
      return;
    }
    mapRef.current?.fitToCoordinates(
      tempProperties
        .filter((p) => {
          p.propertyLatitude && p.propertyLongitude;
        })
        .map((p) => ({
          latitude: p.propertyLatitude!,
          longitude: p.propertyLongitude!,
        })),
      {
        edgePadding: { top: 20, right: 20, bottom: 20, left: 20 },
        animated: true,
      },
    );
  }, [propertiesAccordingToFiltersQuery.data]);

  const handleModalVisible = () => {
    setIsVisible((prevState) => !prevState);
  };

  const handleBottomSheetChange = () => {
    setIsBottomSheetVisible(false);
  };

  const selectedProperty = properties?.find((p) => p.id === selectedObjectId);

  console.log('search params', lat, long, propertyFor, propertyCategory);

  return (
    <View className="flex-1 relative">
      {!isBottomSheetVisible && (
        <Button
          className="absolute bottom-10 right-6 z-10"
          onPress={handlePresentModal}
        >
          <Entypo name="list" size={24} color="white" />
        </Button>
      )}

      <MapView
        ref={mapRef}
        style={{ width: width, flex: 1 }}
        // provider={PROVIDER_GOOGLE}
        googleMapsApiKey={
          Platform.OS === 'ios' ? iosMapApiKey : androidMapApiKey
        }
        userInterfaceStyle="light"
        initialRegion={{
          latitude: Number(profile?.latitude ?? 28.4430837),
          longitude: Number(profile?.longitude ?? 77.0317603),
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
        onPress={() => setSelectedObjectId(null)}
      >
        {properties?.map((property, index) => {
          if (
            !property.propertyLatitude ||
            !property.propertyLongitude ||
            !property.propertyPrice
          ) {
            return;
          }
          return (
            <Marker
              key={property.id}
              coordinate={{
                latitude: property.propertyLatitude,
                longitude: property.propertyLongitude,
              }}
              onPress={(e) => {
                e.stopPropagation();
                setSelectedObjectId(property.id);
                handleDismissModal();
              }}
            >
              <View className="bg-white p-2 rounded-full">
                <Text className="" style={styles.shadow}>
                  {formatPriceAddLabels(String(property.propertyPrice))}
                </Text>
              </View>
            </Marker>
          );
        })}
      </MapView>
      {selectedProperty && (
        <View className="absolute bottom-0 z-50 left-0 w-full bg-white">
          <Button
            className="absolute top-1 right-2 z-10"
            onPress={() => setSelectedObjectId(null)}
          >
            <Entypo name="circle-with-cross" size={24} color="white" />
          </Button>
          <FavouritesComponent
            item={selectedProperty}
            handlePress={() =>
              router.navigate(`/property/${selectedProperty.id}`)
            }
          />
        </View>
      )}

      <BottomSheet
        ref={bottomSheetModalRef}
        // index={0} // Start index when modal is presented
        snapPoints={['100%']} // Height of modal
        onClose={handleBottomSheetChange} // Callback when modal is dismissed
        // onDismiss={handleDismissModal} // Callback when modal is dismissed
        backgroundStyle={{ backgroundColor: 'white' }} // Set background color ()
        handleIndicatorStyle={{ backgroundColor: 'gray' }} // Customize the handle ()
        enablePanDownToClose={true}
      >
        <BottomSheetScrollView className="px-5 flex-col flex-1">
          {/* Search n filter */}
          <BottomSheetView className="flex flex-row items-center">
            {/*<BottomSheetView className="flex flex-row items-center gap-2 flex-1">*/}
            {/*  <Text>Properties</Text>*/}
            {/*  <Switch.Root*/}
            {/*    accessibilityLabel="Property Selected"*/}
            {/*    checked={!isPropertySelected}*/}
            {/*    onPressIn={() => setIsPropertySelected(!isPropertySelected)}*/}
            {/*    onChange={(value) => setIsPropertySelected(value)}*/}
            {/*  >*/}
            {/*    <Switch.Icon checked={!isPropertySelected} />*/}
            {/*  </Switch.Root>*/}
            {/*  <Text>Agents</Text>*/}
            {/*</BottomSheetView>*/}
            <View className="flex-1">
              <Text>{totalResults} Results</Text>
            </View>
            <Pressable
              className="rounded-lg bg-white px-3 py-2"
              style={styles.filtershadow}
              onPress={() => {
                // handleDismissModal();
                setIsVisible(true); //modal ko open
              }}
            >
              <Image
                source={require('../../../assets/icons/filter.png')}
                className="h-6 w-6"
                tintColor={'#C58E00'}
              />
            </Pressable>
          </BottomSheetView>

          <BottomSheetView className="mt-4 flex flex-col">
            {/*{isLoadingProperties ||*/}
            {/*  (isLoadingAgents && (*/}
            {/*    <BottomSheetView className="flex-grow items-center justify-center">*/}
            {/*      <ActivityIndicator size={40} color="#000" />*/}
            {/*    </BottomSheetView>*/}
            {/*  ))}*/}
            {/*{properties.length > 0 && (*/}
            <BottomSheetFlashList
              showsVerticalScrollIndicator={false}
              data={properties}
              estimatedItemSize={210}
              keyExtractor={(item) => item.id.toString()}
              className="flex-1"
              renderItem={({ item }) => (
                <FavouritesComponent
                  item={item}
                  handlePress={() => router.navigate(`/property/${item.id}`)}
                />
              )}
            />
            {/*)}*/}
            {/*{!isPropertySelected && (*/}
            {/*  <BottomSheetFlashList*/}
            {/*    showsVerticalScrollIndicator={false}*/}
            {/*    data={resultAgents?.agents}*/}
            {/*    estimatedItemSize={210}*/}
            {/*    keyExtractor={(item) => item.id.toString()}*/}
            {/*    className="flex-1"*/}
            {/*    renderItem={({ item }) => (*/}
            {/*      <AgentHorizontalCard*/}
            {/*        agent={item}*/}
            {/*        handlePress={() =>*/}
            {/*          router.push(`/network-screens/agent-details`)*/}
            {/*        }*/}
            {/*      />*/}
            {/*    )}*/}
            {/*  />*/}
            {/*)}*/}
          </BottomSheetView>
        </BottomSheetScrollView>
      </BottomSheet>
      <PropertyFilters
        isVisible={isVisible}
        totalResults={totalResults}
        handleModalVisible={handleModalVisible}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  shadow: {
    shadowColor: 'rgba(15, 81, 65, 0.30)',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 1,
    shadowRadius: 24,
    elevation: 5,
  },
  blurView: {
    position: 'absolute',
    height: '55%',
    // flex: 1,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    alignItems: 'center',
    zIndex: 100,
    backgroundColor: 'rgba(255, 248, 244, 0.50)',
    // borderRadius: 16,
    // borderWidth: 1,
    // borderColor: 'rgba(255, 248, 244, 0.50)',
    // padding: 16,
  },
  ioslikeshadow: {
    shadowColor: '#848484',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.7,
    shadowRadius: 4,
    elevation: 3,
  },
  tabBackground: {
    position: 'absolute',
    height: '100%',
    backgroundColor: '#C58E00',
    zIndex: -1,
    top: 0,
    bottom: 0,
    borderRadius: 8,
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  shadowBgColor: {
    shadowColor: 'rgba(15, 81, 65, 0.30)',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 1,
    shadowRadius: 24,
    elevation: 5,
    backgroundColor: '#005842',
    borderColor: '#003F28',
  },
  filtershadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
});
