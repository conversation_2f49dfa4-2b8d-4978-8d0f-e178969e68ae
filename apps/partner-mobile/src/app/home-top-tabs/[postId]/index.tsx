import { View, Text, Pressable, Alert } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';
import Post from '@/app/home-top-tabs/[postId]/post';
import { ScrollView } from 'react-native-gesture-handler';
import { api } from '@/utils/api';
import { useLocalSearchParams } from 'expo-router';
import UserPic from '@/components/user-pic';
import { formatTime } from '@/utils/format-terms';
import VideoComponent from '@/ui/video-component';
import { CommentBottomStrip } from '@/app/home-top-tabs/feed';
import PostOptionsMenu from '@/components/post-options-menu';
import ReportPostModal from '@/components/report-post-modal';
import { useModal } from '@/ui/modal';

const PostComponent = () => {
  const params = useLocalSearchParams<{ postId: string }>();
  const { data: post, refetch } = api.social.getPostById.useQuery(
    { postId: params.postId },
    {
      enabled: !!params.postId,
    },
  );
  const { data: profile } = api.user.profile.useQuery();
  const reportModal = useModal();

  const isOwnPost = profile?.id === post?.user?.id;

  const handleReportPress = () => {
    reportModal.present();
  };

  if (!post) {
    return;
  }

  return (
    <>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="mb-3.5 p-3">
          {/* Post heading */}
          <View className="mb-2 flex-row items-center justify-between">
            <View className="flex-row items-center flex-1">
              <View className="mr-3 rounded-full self-start">
                <UserPic
                  picUrl={
                    post.user?.cloudinaryProfileImageUrl ?? post.user.filePublicUrl
                  }
                  color="#F04D24"
                  size={54}
                  className="mr-2.5 aspect-square h-16 rounded-full"
                />
              </View>
              <View className="flex-1">
                <Text className="mb-[0.25px] font-bold font-airbnb_bd text-sm text-text-600">
                  {post.user?.name}
                </Text>
                <Text className="mb-0.5 font-normal font-airbnb_bk text-[10px] text-primary-800">
                  {post.user?.companyDetails?.companyName}
                </Text>
                <Text className="font-normal font-airbnb_bk text-[10px] text-text-500">
                  {formatTime(post.createdAt)}
                </Text>
              </View>
            </View>

            {/* Post Options Menu */}
            <PostOptionsMenu
              postId={post.id}
              isOwnPost={isOwnPost}
              onReportPress={handleReportPress}
            />
          </View>

        {/* post */}
        <View className="items-center justify-center mb-5">
          <Text className="mb-3 font-airbnb_bk font-normal text-sm text-text-500 w-full">
            {post.content}
          </Text>
          {post.media[0] &&
          post.media[0].mediaType === 'IMAGE' &&
          post.media[0].cloudinaryUrl ? (
            <Image
              source={{ uri: post.media[0].cloudinaryUrl }}
              className="mb-4 h-[206px] w-full"
            />
          ) : (
            post.media[0] &&
            post.media[0].mediaType === 'IMAGE' &&
            post.media[0].filePublicUrl && (
              <Image
                source={{ uri: post.media[0].filePublicUrl }}
                className="mb-4 h-[206px] w-full"
              />
            )
          )}
          {post.media[0] &&
          post.media[0].mediaType === 'VIDEO' &&
          post.media[0].cloudinaryUrl ? (
            <VideoComponent
              videoSource={post.media[0].cloudinaryUrl}
              // className="mb-4 h-[206px] w-full"
            />
          ) : (
            post.media[0] &&
            post.media[0].mediaType === 'VIDEO' &&
            post.media[0].filePublicUrl && (
              <VideoComponent videoSource={post.media[0].filePublicUrl} />
            )
          )}
          <CommentBottomStrip
            postId={post.id}
            likedId={post.likes[0]?.id}
            totalComments={post.totalComments}
            totalLikes={post.totalLikes}
            refetch={refetch}
          />
        </View>

        {post.comments.map((item, index) => (
          <EachComment item={item} key={index} />
        ))}
      </View>
      {/*<View className="items-center w-full px-5">*/}
      {/*  <Image*/}
      {/*    source={require('../../../../assets/images/besthouselol.png')}*/}
      {/*    className="mb-5 mt-3 h-[297px] w-full rounded-lg"*/}
      {/*  />*/}
      {/*  <Post brdr={false} />*/}

      {/*</View>*/}
    </ScrollView>

    {/* Report Post Modal */}
    <ReportPostModal ref={reportModal.ref} postId={post.id} />
  </>
  );
};

export default PostComponent;

const EachComment: React.FC<{
  item: {
    user: {
      companyDetails: { companyName: string } | null;
      name: string;
      id: string;
      filePublicUrl: string | null;
    } | null;
    comment: string;
    createdAt: Date;
    isPinned: boolean;
  };
}> = ({ item }) => {
  // Skip rendering if user is null
  if (!item.user) {
    return null;
  }

  return (
    <View className="mb-4 flex-row items-center w-full">
      <View className="mr-3 border p-1 border-secondary-main700 rounded-full self-start">
        <UserPic
          picUrl={item.user.filePublicUrl}
          color="#F04D24"
          size={54}
          className="h-8 w-8 rounded-full"
        />
      </View>

      <View className="flex-1">
        <View className="flex-1 flex-row items-center justify-between">
          <Text className="font-airbnb_bd text-base font-bold text-text-600">
            {item.user.name}
          </Text>
          <Text className="font-airbnb_bk text-xs font-normal text-text-400">
            {formatTime(item.createdAt)}
          </Text>
        </View>
        <Text className="font-airbnb_bk text-xs font-normal text-primary-750">
          {item.user.companyDetails?.companyName}
        </Text>
        <Text className="my-2 flex-1 font-airbnb_bk text-sm font-normal text-text-500">
          {item.comment}
        </Text>
        {/* likes & button */}
        {/*<View className="flex-row items-center">*/}
        {/*  <Pressable*/}
        {/*    onPress={() =>*/}
        {/*      Alert.alert(*/}
        {/*        'Like button pressed!  \n Ask your Friendly neighborhood dev to implement a function to it.',*/}
        {/*      )*/}
        {/*    }*/}
        {/*    className="flex-row items-center"*/}
        {/*  >*/}
        {/*    <Image*/}
        {/*      source={require('../../../../assets/icons/like2.png')}*/}
        {/*      className="mr-2 h-3 w-3"*/}
        {/*    />*/}
        {/*    <Text className="font-airbnb_bk text-xs font-normal text-text-300">*/}
        {/*      Like*/}
        {/*    </Text>*/}
        {/*  </Pressable>*/}
        {/*  <Text className="font-airbnb_bk text-xs font-normal text-text-300">*/}
        {/*    {' '}*/}
        {/*    • 101{' '}*/}
        {/*  </Text>*/}
        {/*</View>*/}
      </View>
    </View>
  );
};
