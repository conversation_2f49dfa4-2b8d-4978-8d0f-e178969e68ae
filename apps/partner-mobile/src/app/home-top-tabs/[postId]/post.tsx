import { Alert, Pressable, Text, TextInput, View } from 'react-native';
import React, { useState } from 'react';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import { boolean } from 'zod';

interface ActionsProps {
  id: number;
  source?: string | number;
  text: string;
  onPress?: () => void;
  type?: 'action' | 'display';
}

const Actions: React.FC = () => {
  const [liked, setLiked] = useState(false);

  const handleLike = () => {
    setLiked(!liked);
    Alert.alert('Like button pressed!');
  };

  const handleComment = () => {
    Alert.alert('Comment button pressed!');
  };

  //Action buttons
  const Data = postActions.map((item) => {
    if (item.id === 1) {
      return {
        ...item,
        text: liked ? 'Liked' : 'Like',
        onPress: handleLike,
      };
    }
    if (item.id === 2) {
      return {
        ...item,
        onPress: handleComment,
      };
    }
    return item;
  });

  return (
    <View className="w-full flex-row items-center justify-between">
      {Data.map((item) => (
        <Pressable
          key={item.id}
          className="flex-row items-center"
          onPress={item.type === 'action' ? item.onPress : undefined}
        >
          {item.source && (
            <Image source={item.source} className={`mr-2 h-5 w-5`} />
          )}
          <Text
            className={`font-airbnb_bk font-normal text-sm ${
              item.type === 'display'
                ? 'text-text-500'
                : liked
                  ? 'text-primary-main700'
                  : 'text-text-500'
            }`}
          >
            {item.text}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

//Textinput
const Comment = () => {
  return (
    <View
      className={`mt-2.5 px-3 flex-row items-center justify-between border border-text-100 rounded-lg`}
    >
      <TextInput className="p-3 flex-1" placeholder="Add a comment" />
      <Pressable
        onPress={() =>
          Alert.alert(
            'Send Button pressed! \n Ask your Friendly neighborhood dev to implement a function to it',
          )
        }
      >
        <Image
          source={require('../../../../assets/icons/send.png')}
          className="h-4 w-4"
          tintColor={'#5F2800'}
        />
      </Pressable>
    </View>
  );
};

interface PostProps {
  brdr: boolean;
  onPress?: () => void;
}

const Post: React.FC<PostProps> = ({ brdr, onPress }) => {
  return (
    <Pressable
      className={`mb-3.5 ${brdr ? 'p-3 border border-text-100' : null} rounded-xl`}
      onPress={onPress}
    >
      {/* Post heading */}
      <View className="mb-2 flex-row items-center">
        <View className="mr-3 border p-1 border-secondary-main700 rounded-full self-start">
          <Image
            source={require('../../../../assets/images/avatar.png')}
            className="h-10 w-10"
          />
        </View>
        <View>
          <Text className="mb-[0.25px] font-bold font-airbnb_bd text-sm text-text-600">
            Akshay mehra
          </Text>
          <Text className="mb-0.5 font-normal font-airbnb_bk text-[10px] text-primary-800">
            Omkiron Properties
          </Text>
          <Text className="font-normal font-airbnb_bk text-[10px] text-text-500">
            6hrs
          </Text>
        </View>
      </View>

      {/* post */}
      <View className="items-center justify-center">
        <Text className="mb-3 font-airbnb_bk font-normal text-sm text-text-500">
          🔑 Start your homeownership journey today! we offers great value and
          potential for growth. With a spacious yard and cozy interiors, it's
          the perfect place to call your own. 🏡 Contact us now to learn more!
          #FirstHome #StarterHome #AffordableLiving"
        </Text>
        {brdr && (
          <Image
            source={require('../../../../assets/images/besthouselol.png')}
            className="mb-4 h-[206px] w-[366px]"
          />
        )}
        {/* actions, comment, & info yo */}
        <Actions />
        <Comment />
      </View>
    </Pressable>
  );
};

export default Post;

export const postActions: ActionsProps[] = [
  {
    id: 1,
    source: require('../../../../assets/icons/like1.png'),
    text: 'Like',
    type: 'action',
  },
  {
    id: 2,
    source: require('../../../../assets/icons/comment.png'),
    text: 'Comment',
    type: 'action',
  },
  {
    id: 3,
    text: '5.2K likes',
    type: 'display',
  },
  {
    id: 4,
    text: '20 comments',
    type: 'display',
  },
];
