import { FlashList } from '@shopify/flash-list';
import { Image as NImage } from 'expo-image';
import React, { useEffect, useMemo } from 'react';
import { SafeAreaView, Text, View } from 'react-native';
import { api } from '@/utils/api';
import { format } from 'date-fns';
import NotificationEmpty from '@/components/notification-empty-screen';

const getGroupTitle = (date: Date): string => {
  const now = new Date();
  const diffInDays = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),
  );

  if (diffInDays === 0) return 'Today';
  if (diffInDays === 1) return 'Yesterday';
  if (diffInDays <= 7) return 'This Week';
  if (diffInDays <= 30) return 'This Month';
  return 'Older';
};

type Notification = {
  id: string;
  title: string;
  description: string;
  type: string;
  metaData: any;
  receiverId: string | null;
  createdAt: Date;
};

export default function Notification() {
  const trpcUtils = api.useUtils();
  const { data, isLoading } = api.notification.getAllNotifications.useQuery();
  const { isSuccess } =
    api.notification.updateLastCheckedNotification.useQuery(undefined, {
      enabled: false, // Disable automatic query execution
    });

  const notifications: Notification[] = data?.notifications ?? [];

  const groupedNotifications = useMemo(
    () =>
      notifications.reduce(
        (groups: Record<string, Notification[]>, notification) => {
          const groupTitle = getGroupTitle(notification.createdAt);
          if (!groups[groupTitle]) {
            groups[groupTitle] = [];
          }
          groups[groupTitle].push(notification);
          return groups;
        },
        {},
      ),
    [notifications],
  );

  useEffect(() => {
    if (isSuccess) {
      void trpcUtils.invalidate();
    }
  }, [isSuccess, trpcUtils]);

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1">
        {notifications.length === 0 ? (
          <NotificationEmpty />
        ) : (
          <FlashList
            showsVerticalScrollIndicator={false}
            data={notifications}
            keyExtractor={(item) => item.id.toString()}
            estimatedItemSize={190}
            className="py-6"
            renderItem={({ item, index }) => (
              <View>
                {/*{item.id === 3 ? (*/}
                {/*  <Text className="px-5 pb-3 pt-5 font-airbnb_md text-sm font-medium text-secondary-main700">*/}
                {/*    Yesterday*/}
                {/*  </Text>*/}
                {/*) : item.id === 7 ? (*/}
                {/*  <Text className="px-5 pb-3 pt-5 font-airbnb_md text-sm font-medium text-secondary-main700">*/}
                {/*    This week*/}
                {/*  </Text>*/}
                {/*) : null}*/}
                <View
                  className={`flex-row ${index % 2 === 0 ? 'bg-[#F1F1F1]' : 'bg-white'} p-5`}
                >
                  {/*<NImage*/}
                  {/*  source={item.source}*/}
                  {/*  className="mr-3 aspect-square h-10"*/}
                  {/*/>*/}

                  <View className="flex-1 gap-0.5">
                    <View className="flex flex-row items-center justify-between">
                      <Text className="font-airbnb_md text-sm font-medium text-[#5F3924]">
                        {item.title}
                      </Text>
                      <Text className="font-airbnb_md text-[10px] font-medium text-text-500">
                        {format(item.createdAt, 'dd/MM/yyyy hh:mm a')}
                      </Text>
                    </View>

                    <Text
                      className="font-airbnb_bk text-xs font-normal text-text-400"
                      style={{ flexWrap: 'wrap' }}
                    >
                      {item.description}
                    </Text>
                  </View>
                </View>
              </View>
            )}
          />
        )}
      </View>
    </SafeAreaView>
  );
}
