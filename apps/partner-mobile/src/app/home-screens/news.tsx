import { FlashList } from '@shopify/flash-list';
import { Image as NImage } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { format } from 'date-fns';
import * as WebBrowser from 'expo-web-browser';

import { Data } from '@/ui/news-data';
import { api } from '@/utils/api';

export default function NewsDetails() {
  const { data: news } = api.news.getAllNews.useQuery();

  if (!news) {
    return <></>;
  }

  return (
    <View className="h-full w-full px-5">
      {news.length === 0 ? (
        <View className="items-center justify-center">
          <Text className="my-2 font-airbnb_md text-base text-text-600">
            No news available.
          </Text>
        </View>
      ) : (
        <View className="flex-1">
          <FlashList
            showsVerticalScrollIndicator={false}
            data={news}
            keyExtractor={(item) => item.id.toString()}
            estimatedItemSize={190}
            className="pt-5"
            renderItem={({ item }) => {
              return (
                <Pressable
                  onPress={() => WebBrowser.openBrowserAsync(item.redirectUrl)}
                >
                  <View className="my-2 flex-row items-center justify-center">
                    <NImage
                      source={item.filePublicUrl}
                      className="aspect-video w-2/5 rounded-md "
                    />
                    <View
                      className="h-full flex-col justify-between pl-4"
                      style={{ flex: 1 }}
                    >
                      {/* Headline */}
                      <Text
                        className="self-start font-airbnb_bd text-sm font-bold text-primary-750"
                        numberOfLines={1}
                      >
                        {item.title}
                      </Text>
                      <Text
                        className="self-start font-airbnb_lt text-sm text-text-500"
                        numberOfLines={2}
                      >
                        {item.description}
                      </Text>
                      {/* <Text className="font-airbnb_bd text-sm font-bold text-text-500">
                        Read more
                      </Text> */}

                      {/* Author */}
                      <View className="Items-center flex-row">
                        {/*<Text className="items-center font-airbnb_bk text-sm font-normal text-text-400">*/}
                        {/*  by{' '}*/}
                        {/*</Text>*/}
                        {/*<Text className="font-airbnb_bd text-sm font-bold text-text-500">*/}
                        {/*  {item.author}*/}
                        {/*</Text>*/}
                        <Text className="items-center font-airbnb_bk text-sm font-normal text-text-400 ">
                          {' '}
                          {format(item.createdAt, 'dd/MM/yyyy hh:mm a')}
                        </Text>
                      </View>
                    </View>
                  </View>
                </Pressable>
              );
            }}
          />
        </View>
      )}
    </View>
  );
}
