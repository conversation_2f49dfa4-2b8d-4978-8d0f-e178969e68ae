import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import z from 'zod';
import { Button } from '@/ui';
import { api } from '@/utils/api';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { showMessage } from 'react-native-flash-message';

const formSchema = z.object({
  onboardingPreference: z.enum(['BUY', 'RENT'], {
    required_error: 'You need to select one choice.',
  }),
});

const Step1 = () => {
  const step1Mutation = api.user.onboardingStep1.useMutation();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON>esolver(formSchema),
  });
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    const { onboardingPreference } = data;
    step1Mutation
      .mutateAsync({ onboardingPreference })
      .then((resp) => {
        showMessage({
          message: resp.messageDescription as string,
          type: 'success',
        });
        router.push('/home-screens/step-2');
      })
      .catch(() => {
        showMessage({
          message: 'Something went wrong',
          type: 'danger',
        });
      });
  };

  const selectedOption = form.watch('onboardingPreference');

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView>
        <View className="h-[0.5%] w-1/3 rounded-[4px] bg-secondary-main700" />

        <View className="h-full w-full px-5">
          <View className="mb-10 mt-5 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-xl font-semibold text-secondary-850">
                1/
              </Text>
              <Text className="text-xl font-semibold text-text-600">3</Text>
            </View>
            <Pressable onPress={() => router.push('/')}>
              <NImage
                source={require('../../../assets/icons/cancel.png')}
                contentFit="contain"
                className="h-8 w-8"
                tintColor={'#252525'}
              />
            </Pressable>
          </View>

          <Text className="mb-5 font-airbnb_xbd text-3xl font-extrabold text-secondary-main700">
            Your Interest
          </Text>

          {/* Agent Option */}
          <Pressable
            className={`my-3 p-5 flex-row items-center rounded-2xl border-2 border-[#F04D24] ${selectedOption === 'BUY' ? 'bg-secondary-100' : 'bg-[#FFFEFD]'} py-5`}
            onPress={() => form.setValue('onboardingPreference', 'BUY')}
          >
            <View className="mr-5 p-3 bg-white rounded-xl">
              <NImage
                source={require('../../../assets/icons/property.png')}
                className="w-20 h-20"
              />
            </View>
            <View>
              <Text
                className={`font-airbnb_xbd text-xl font-extrabold ${selectedOption === 'BUY' ? 'text-secondary-main700' : 'text-text-600'}`}
              >
                Buy a Property
              </Text>
              <Text className="mt-2 text-text-550 flex-wrap font-normal text-sm font-airbnb_bk">
                Let us help you find the perfect place {'\n'}to call your own
              </Text>
            </View>
          </Pressable>

          {/* Dealer Option */}
          <Pressable
            className={`my-3 p-5 flex-row items-center rounded-2xl border-2 border-[#F04D24] ${selectedOption === 'RENT' ? 'bg-secondary-100' : 'bg-[#FFFEFD]'} py-5`}
            onPress={() => form.setValue('onboardingPreference', 'RENT')}
          >
            <View className="mr-5 p-3 bg-white rounded-xl">
              <NImage
                source={require('../../../assets/icons/property.png')}
                className="w-20 h-20"
              />
            </View>
            <View>
              <Text
                className={`font-airbnb_xbd text-xl font-extrabold ${selectedOption === 'RENT' ? 'text-secondary-main700' : 'text-text-600'}`}
              >
                Rent a Property
              </Text>
              <Text className="mt-2 text-text-550  font-normal text-sm font-airbnb_bk">
                Looking to sell your property faster,
              </Text>
              <Text className="text-text-550  font-medium text-sm font-airbnb_md">
                We are here to assist you
              </Text>
            </View>
          </Pressable>

          {errorMessage && (
            <Text className="text-red-500 text-base font-semibold mt-4">
              {errorMessage}
            </Text>
          )}

          <View className="mb-20 flex-1 flex-col items-end justify-end">
            <View className="w-full flex-row items-center justify-between">
              <Pressable
                onPress={() => router.push('/home')}
                className="rounded-xl bg-[#F1F1F1] px-6 py-3.5 border border-[#6B6B6B]"
              >
                <Text className="text-text-600  font-medium text-base font-airbnb_md">
                  Skip
                </Text>
              </Pressable>
              <Button
                variant="step"
                size="step"
                label="Continue"
                loading={step1Mutation.isPending}
                // disabled={!selected}
                onPress={form.handleSubmit(onSubmit, (err) => {
                  showMessage({
                    message: 'Please select an option',
                    type: 'danger',
                  });
                })}
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default Step1;
