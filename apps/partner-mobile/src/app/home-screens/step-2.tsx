import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useNavigation } from 'expo-router';
import React, { useState } from 'react';
import { Pressable, Text, View, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { showMessage } from 'react-native-flash-message';
import TextInputGoogleAutocomplete from '@/components/text-input-google-autocomplete';
import TextInputComponent from '@/components/text-input-component';
import AntDesign from '@expo/vector-icons/AntDesign';
import { Button, cn } from '@/ui';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { api } from '@/utils/api';

const formSchema = z.object({
  location: z.string().min(2),
  latitude: z.string(),
  longitude: z.string(),
  operationArea: z.string().optional(),
});

// eslint-disable-next-line max-lines-per-function
const Step2 = () => {
  const navigation = useNavigation();
  const [detectingLocation, setDetectingLocation] = useState<boolean>(false);
  const { control, setValue, reset, handleSubmit } = useForm<
    z.infer<typeof formSchema>
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      location: '',
      operationArea: '',
    },
  });

  const step2Mutation = api.user.onboardingStep2.useMutation();
  const onboardingOperationAreaMutation =
    api.operationArea.onboardingOperationArea.useMutation();
  const { data: operationAreas, refetch: refetchOperationAreas } =
    api.operationArea.getOperationArea.useQuery();
  const deleteOperationArea =
    api.operationArea.deleteOperationArea.useMutation();

  async function getCurrentLocation() {
    setDetectingLocation(true);
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      setDetectingLocation(false);
      showMessage({
        message: 'Permission to access location was denied',
        type: 'danger',
      });
      return;
    }

    try {
      let location = await Location.getCurrentPositionAsync({});
      console.log(location);
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      console.log('reverseGeocode', reverseGeocode[0]);
      const address = reverseGeocode[0];
      if (!address) {
        showMessage({
          message: 'Unable to get address',
          type: 'danger',
        });
        return;
      }
      setValue(
        'location',
        address.formattedAddress
          ? address.formattedAddress
          : `${address.streetNumber}, ${address.street}, ${address.district}, ${address.city}, ${address.region}, ${address.isoCountryCode}`,
      );
      setValue('latitude', location.coords.latitude.toString());
      setValue('longitude', location.coords.longitude.toString());
    } catch (error) {
      console.log('error', error);
    }
    setDetectingLocation(false);
  }

  const onAddressComponentsChange = (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => {
    console.log('onAddressComponentsChange', data);
    setValue('location', data.address);
    setValue('latitude', data.lat);
    setValue('longitude', data.lng);
  };

  const onOperationalAreaAddressComponentsChange = (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => {
    onboardingOperationAreaMutation.mutate(
      {
        operationArea: data.address,
        operationAreaLatitude: data.lat,
        operationAreaGooglePlaceId: data.place_id,
        operationAreaLongitude: data.lng,
        operationAreaAddressComponent: data.address_components,
      },
      {
        onSuccess: () => {
          showMessage({
            message: 'Operational Area added successfully.',
            type: 'success',
          });
          reset({
            operationArea: '',
          });
        },
        onError: (err) => {
          showMessage({
            message: err?.message ?? 'Operational Area failed.',
            type: 'danger',
          });
        },
      },
    );
  };

  const openConfirmModalDeleteOperationalArea = (operationAreaId: string) => {
    Alert.alert(
      'Are you absolutely sure?',
      'Are you sure you want to delete this operation area?',
      [
        {
          text: 'Cancel',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: () => {
            deleteOperationArea.mutate(
              { operationAreaId },
              {
                onSuccess: () => {
                  refetchOperationAreas();
                  showMessage({
                    message: 'Operational area deleted successfully.',
                    type: 'success',
                  });
                },
                onError: (err) => {
                  showMessage({
                    message: err?.message ?? 'Operational Area failed.',
                    type: 'danger',
                  });
                },
              },
            );
          },
        },
      ],
    );
  };

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView>
        <View className="h-[0.5%] w-2/3 rounded-[4px] bg-secondary-main700" />

        <View className="h-full w-full px-5">
          <View className="mb-20 mt-5 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-xl font-semibold text-secondary-850">
                2/
              </Text>
              <Text className="text-xl font-semibold text-text-600">3</Text>
            </View>
            <Pressable onPress={() => router.push('/')}>
              <NImage
                source={require('../../../assets/icons/cancel.png')}
                contentFit="contain"
                className="h-8 w-8"
                tintColor={'#252525'}
              />
            </Pressable>
          </View>

          <View>
            <Text className="font-airbnb_xbd text-3xl font-extrabold text-secondary-main700">
              Choose Your Location
            </Text>
            <Text className="mt-1 font-normal text-sm font-airbnb_bk text-text-550">
              Choose your location to get customized suggestions
            </Text>
          </View>

          <View className="mt-8">
            {/*<Text className="mb-1 font-airbnb_md text-base font-medium text-primary-800">*/}
            {/*  Your Location*/}
            {/*</Text>*/}
            <Controller
              control={control}
              name="location"
              render={({ field, fieldState: { error } }) => {
                return (
                  <TextInputGoogleAutocomplete
                    onAddressComponentsChange={onAddressComponentsChange}
                  >
                    <View className="mb-1">
                      <Text
                        className={cn(
                          'mb-2 font-airbnb_md text-base font-medium text-primary-800',
                          error && 'text-danger-600',
                        )}
                      >
                        Your Location
                      </Text>
                      <View
                        className={cn(
                          'mt-0 flex-row items-center rounded-xl border border-secondary-main700 bg-primary-50',
                          error && 'border-danger-600',
                        )}
                      >
                        <Text className="flex-1 px-5 py-3.5 font-airbnb_bk text-sm font-normal leading-5 text-text-600">
                          {field.value || ' '}
                        </Text>
                      </View>
                      {error?.message && (
                        <Text className="text-sm text-danger-400">
                          {error?.message}
                        </Text>
                      )}
                    </View>
                  </TextInputGoogleAutocomplete>
                );
              }}
            />
            {/* Auto location detection button */}
            <Pressable
              onPress={getCurrentLocation}
              className="mt-3 flex flex-row items-center gap-2"
            >
              {detectingLocation ? (
                <ActivityIndicator color="#5f2800" size={18} />
              ) : (
                <NImage
                  source={require('../../../assets/icons/autolocate.png')}
                  className="h-5 w-5"
                  contentFit="contain"
                />
              )}
              <Text className="font-airbnb_bk text-sm font-normal text-primary-800">
                Autodetect my location
              </Text>
            </Pressable>
          </View>

          <View>
            <Controller
              control={control}
              name="operationArea"
              render={({ field, fieldState: { error } }) => {
                return (
                  <TextInputGoogleAutocomplete
                    onAddressComponentsChange={
                      onOperationalAreaAddressComponentsChange
                    }
                  >
                    <View className="mb-4 mt-6">
                      <Text
                        className={cn(
                          'mb-2 font-airbnb_md text-base font-medium text-primary-800 ',
                          error && 'text-danger-600 ',
                        )}
                      >
                        Your Operational Areas
                      </Text>
                      <View
                        className={cn(
                          'mt-0 flex-row items-center rounded-xl border border-secondary-main700 bg-primary-50',
                          error && 'border-danger-600',
                        )}
                      >
                        <Text className="flex-1 px-5 py-3.5 font-airbnb_bk text-sm font-normal leading-5 text-text-600">
                          {field.value || ' '}
                        </Text>
                      </View>
                      {error?.message && (
                        <Text className="text-sm text-danger-400">
                          {error?.message}
                        </Text>
                      )}
                    </View>
                  </TextInputGoogleAutocomplete>
                );
              }}
            />
            <View className="flex flex-row flex-wrap items-center gap-3">
              {
                operationAreas &&
                  operationAreas.map((area) => (
                    <View
                      key={area.id}
                      className="flex flex-row items-center gap-2 rounded-[6px] bg-primary-100 px-3 py-2 font-airbnb_w_bk text-sm text-primary-2-800 xl:text-base"
                    >
                      <Text className="font-montserratRegular text-sm text-black-500">
                        {area.name}
                      </Text>
                      <Pressable
                        onPress={() =>
                          openConfirmModalDeleteOperationalArea(area.id)
                        }
                      >
                        <AntDesign name="closecircle" size={16} color="black" />
                      </Pressable>
                    </View>
                  ))
                // <View className="mt-3 flex-row items-center">
                //   <Text className="font-montserratRegular text-sm text-black-500">
                //     {operationAreas.length} Selected
                //   </Text>
                // </View>
              }
            </View>
            {/*<TextInputComponent*/}
            {/*  background={''}*/}
            {/*  placeholdercolor={''}*/}
            {/*  searchiconimagecolor={''}*/}
            {/*  textinputcolor={''}*/}
            {/*  bordercolor={'border-secondary-main700'}*/}
            {/*  placeholdertext={''}*/}
            {/*/>*/}
            {/* Selected locations */}
            {/* <Pressable className="mt-3 flex-row items-center">
            <NImage
              source={require('../../../assets/icons/autolocate.png')}
              className="mr-2 h-5 w-5"
              contentFit="contain"
            />
            <Text className="font-airbnb_bk text-sm font-normal text-primary-800">
              Autodetect my location
            </Text>
          </Pressable> */}
          </View>

          {/* Bottom navigation buttons */}
          <View className="mb-20 flex-1 flex-col items-end justify-end">
            <View className="w-full flex-row items-center justify-between">
              <Pressable
                onPress={() => router.push('/')}
                className="rounded-xl bg-[#F1F1F1] px-6 py-3.5 border border-[#6B6B6B]"
              >
                <Text className="text-text-600  font-medium text-base font-airbnb_md">
                  Skip
                </Text>
              </Pressable>
              <Button
                variant="step"
                size="step"
                label="Continue"
                loading={
                  step2Mutation.isPending ||
                  onboardingOperationAreaMutation.isPending
                }
                onPress={() => router.push('/home-screens/step-3')}
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default Step2;
