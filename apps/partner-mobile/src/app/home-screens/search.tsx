import {
  View,
  Text,
  Pressable,
  Modal,
  Animated,
  PanResponder,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { router } from 'expo-router';
import meiliClient from '@/utils/meilisearch-client';
import Entypo from '@expo/vector-icons/Entypo';
import { BlurView } from 'expo-blur';
import { Button, Input } from '@/ui';
import { Image as NImage } from 'expo-image';
import { ScrollView } from 'moti';
import { Env } from '@env';

type IPartner = {
  filePublicUrl: string;
  cloudinaryProfileImageUrl: string;
  id: string;
  name: string;
  userLocation: string;
};

type IProperty = {
  id: string;
  mediaSections: {
    media: { filePublicUrl: string; cloudinaryUrl: string }[];
  }[];
  propertyAddress: string;
  propertyLocation: string;
  propertyTitle: string;
  user: { name: string };
};

const AgentCard = ({ agent }: { agent: IPartner }) => {
  return (
    <Pressable onPress={() => router.push(`/network-screens/${agent.id}`)}>
      <View className="mb-4 flex-row items-center justify-between rounded-xl border border-[#E9E2DD] px-2.5 py-3">
        <View className="flex-row items-center">
          <NImage
            source={
              agent.cloudinaryProfileImageUrl ||
              agent.filePublicUrl ||
              require('../../../assets/icons/user-defaultimg.png')
            }
            className="aspect-square w-[48px] rounded-xl"
          />
          <View className="ml-3 flex flex-col flex-1">
            <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-750">
              {agent.name}
            </Text>
            <Text className="font-airbnb_bk text-sm font-normal text-text-600">
              {agent.userLocation}
            </Text>
          </View>
        </View>

        <View className="flex flex-col"></View>
      </View>
    </Pressable>
  );
};

const Search = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalContent, setModalContent] = useState<
    'agents' | 'properties' | null
  >(null);
  const [searchText, setSearchText] = useState('');
  const [partners, setPartners] = useState<IPartner[]>([]);
  const [properties, setProperties] = useState<IProperty[]>([]);

  const SNAP_POINTS = {
    top: 0,
    middle: 300,
    bottom: 600,
  };
  const pan = useRef(new Animated.Value(0)).current;
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          pan.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.vy > 0.5) {
          closeModal();
        } else if (gestureState.dy < SNAP_POINTS.middle) {
          snapTo('top');
        } else if (gestureState.dy < SNAP_POINTS.bottom) {
          snapTo('middle');
        } else {
          closeModal();
        }
      },
    }),
  ).current;

  const snapTo = (point: 'top' | 'middle' | 'bottom') => {
    Animated.spring(pan, {
      toValue: SNAP_POINTS[point],
      useNativeDriver: true,
    }).start();
  };

  const closeModal = () => {
    Animated.timing(pan, {
      toValue: SNAP_POINTS.bottom,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setModalVisible(false);
      setModalContent(null);
      pan.setValue(0);
    });
  };

  const handleOpenModal = (content: 'agents' | 'properties') => {
    setModalContent(content);
    setModalVisible(true);
    snapTo('middle');
  };

  useEffect(() => {
    meiliClient
      .index(Env.MEILI_SEARCH_PARTNER_KEY)
      .search(searchText)
      .then((resp) => {
        console.log('MEILI_SEARCH_PARTNER', resp);
        setPartners(resp.hits as IPartner[]);
      });
  }, [searchText]);

  return (
    <>
      <View className="h-full w-full">
        <View className="px-5 mt-5 mb-8">
          <View>
            <Input
              icon={1}
              imagesource={require('../../../assets/icons/search2.png')}
              placeholder="Search for agent or properties"
              value={searchText}
              onChangeText={(text) => setSearchText(text)}
            />
          </View>

          <ScrollView
            contentContainerStyle={{ paddingBottom: 100 }}
            showsVerticalScrollIndicator={false}
          >
            <View className="my-2">
              <Text className="text-lg font-medium text-secondary-900 font-airbnb_md">
                Agents
              </Text>
              <View className="my-3">
                {partners.map((agent) => (
                  <AgentCard agent={agent} key={agent.id} />
                ))}
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
    </>
  );
};

export default Search;
