/* eslint-disable max-lines-per-function */
import Entypo from '@expo/vector-icons/Entypo';
import { Stack, useNavigation } from 'expo-router';
import React from 'react';
import { Pressable, Text } from 'react-native';

export default function Layout() {
  const navigation = useNavigation();
  return (
    <Stack>
      <Stack.Screen
        name="search"
        options={{
          title: 'Search',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="news"
        options={{
          title: 'News',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="news-details"
        options={{
          title: '',
          headerTintColor: '#F04D24',
          headerTitleAlign: 'center',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="notification"
        options={{
          title: 'Notification',
          headerTintColor: '#F04D24',
          headerTitleAlign: 'center',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
          // headerRight: () => (
          //   <Pressable>
          //     <Text className="font-airbnb_md text-sm font-medium text-primary-700">
          //       Mark all as read
          //     </Text>
          //   </Pressable>
          // ),
        }}
      />
      <Stack.Screen name="auto-complete" options={{ headerShown: false }} />
      <Stack.Screen name="[propertyId]" options={{ headerShown: false }} />
      <Stack.Screen name="step-1" options={{ headerShown: false }} />
      <Stack.Screen name="step-2" options={{ headerShown: false }} />
      <Stack.Screen name="step-3" options={{ headerShown: false }} />
      <Stack.Screen name="gallery" options={{ headerShown: false }} />
    </Stack>
  );
}
