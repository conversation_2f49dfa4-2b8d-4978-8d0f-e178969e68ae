import { Image, Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useNavigation } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  Pressable,
  Text,
  View,
  StyleSheet,
  ScrollView,
  Animated,
  Easing,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const { height, width } = Dimensions.get('screen');
import { Button } from '@/ui';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import AgentCard from '@/components/agent-card';

// eslint-disable-next-line max-lines-per-function
const Step3 = () => {
  const {
    data: nearbyAgents,
    isLoading,
    isSuccess,
  } = api.user.nearbyAgents.useQuery();
  const navigation = useNavigation();
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const sendConnectionReqMutation =
    api.connection.sendConnectionRequest.useMutation();
  const updateOnBoardingStatusMutation =
    api.user.updateOnBoardingStatus.useMutation();
  const utils = api.useUtils();

  useEffect(() => {
    const bounceAnimation = () => {
      if (!isUserScrolling && currentIndex === 0) {
        Animated.sequence([
          Animated.timing(bounceAnim, {
            toValue: -20,
            duration: 300,
            easing: Easing.elastic(1.2),
            useNativeDriver: true,
          }),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 300,
            easing: Easing.elastic(1.2),
            useNativeDriver: true,
          }),
        ]).start();
      }
    };

    const animationInterval = setInterval(bounceAnimation, 5000);

    return () => clearInterval(animationInterval);
  }, [bounceAnim, isUserScrolling, currentIndex]);

  const handleScroll = (event: {
    nativeEvent: { contentOffset: { x: any } };
  }) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / (width * 0.8));
    setCurrentIndex(index);
    setIsUserScrolling(true);
  };

  const handleScrollEnd = () => {
    setIsUserScrolling(false);
  };

  const handleClick = (agentId: string) => {
    sendConnectionReqMutation.mutate(
      {
        receiverId: agentId,
      },
      {
        onSuccess: (opts) => {
          utils.invalidate();
          if (opts.warning) {
            showMessage({
              message: opts.message,
              type: 'danger',
            });
            return;
          }
          showMessage({
            message: opts.message,
            type: 'success',
          });
        },
        onError: (opts) => {
          showMessage({
            message: opts.message,
            type: 'danger',
          });
        },
      },
    );
  };

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView>
        <View className="h-[0.5%] w-3/3 rounded-[4px] bg-secondary-main700" />

        <View className="h-full w-full">
          <View className="mb-20 mt-5 px-5 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-xl font-semibold text-secondary-850">
                3/
              </Text>
              <Text className="text-xl font-semibold text-text-600">3</Text>
            </View>
            <Pressable onPress={() => router.push('/')}>
              <NImage
                source={require('../../../assets/icons/cancel.png')}
                contentFit="contain"
                className="h-8 w-8"
                tintColor={'#252525'}
              />
            </Pressable>
          </View>

          <View className="mb-10 px-5">
            <Text className="font-airbnb_xbd text-3xl font-extrabold text-secondary-main700">
              Connect with nearby agent
            </Text>
            <Text className="mt-1 font-normal text-sm font-airbnb_bk text-text-550">
              Get expert insights and advice on the best properties {'\n'}
              available right now.
            </Text>
          </View>

          <Animated.View
            style={{
              transform: [{ translateX: bounceAnim }],
              width: '100%',
            }}
          >
            <ScrollView
              horizontal
              pagingEnabled
              decelerationRate="fast"
              snapToInterval={width * 0.8}
              snapToAlignment="start"
              showsHorizontalScrollIndicator={false}
              onScroll={handleScroll}
              onScrollEndDrag={handleScrollEnd}
              onMomentumScrollEnd={handleScrollEnd}
              scrollEventThrottle={16}
              contentContainerStyle={{
                paddingHorizontal: 20,
              }}
            >
              {nearbyAgents &&
                nearbyAgents.map((item) => <AgentCard agent={item} />)}
            </ScrollView>
          </Animated.View>

          {/* Bottom navigation buttons */}
          <View className="mb-20 px-5 flex-1 flex-col items-end justify-end">
            <View className="w-full flex-row items-center justify-between">
              {/*<Pressable*/}
              {/*  onPress={() => router.push('/')}*/}
              {/*  className="rounded-xl bg-[#F1F1F1] px-6 py-3.5 border border-[#6B6B6B]"*/}
              {/*>*/}
              {/*<Text className="text-text-600  font-medium text-base font-airbnb_md">*/}
              {/*  Skip*/}
              {/*</Text>*/}
              {/*</Pressable>*/}
              <Button
                variant="step"
                // size="step"
                label="Finish"
                className="w-full"
                loading={updateOnBoardingStatusMutation.isPending}
                onPress={() =>
                  updateOnBoardingStatusMutation.mutate(
                    {},
                    {
                      onSuccess: () => {
                        router.push('/');
                      },
                      onError: () => {
                        showMessage({
                          message: 'Something went wrong',
                          type: 'danger',
                        });
                        // router.push('/')
                      },
                    },
                  )
                }
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default Step3;

const styles = StyleSheet.create({
  shadowBox: {
    shadowColor: '#250000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
});
