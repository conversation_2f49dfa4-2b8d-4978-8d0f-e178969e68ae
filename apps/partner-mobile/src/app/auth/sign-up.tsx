import { LinearGradient } from 'expo-linear-gradient';
import Entypo from '@expo/vector-icons/Entypo';
import { useRouter, useNavigation } from 'expo-router';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import type { SignUpFormProps } from '@/components/sign-up-form';
import { SignUpForm } from '@/components/sign-up-form';
import { useAuth } from '@/core';
import { FocusAwareStatusBar } from '@/ui';
import { Pressable, View } from 'react-native';
import { api } from '@/utils/api';

export default function SignUp() {
  const router = useRouter();
  const navigation = useNavigation();

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        <FocusAwareStatusBar />
        <View className="flex-row items-center justify-between px-5">
          <Pressable
            onPress={() =>
              navigation.canGoBack()
                ? navigation.goBack()
                : router.replace('/auth/onboarding')
            }
            className="mt-3"
          >
            <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
          </Pressable>
        </View>
        <SignUpForm label={true} />
      </SafeAreaView>
    </LinearGradient>
  );
}
