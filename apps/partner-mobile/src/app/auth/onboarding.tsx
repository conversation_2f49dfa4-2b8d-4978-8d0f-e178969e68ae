import { Image as NImage } from 'expo-image';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import OnboardingComponent from '@/components/onboarding-component';
import { useIsFirstTime } from '@/core/hooks';
import { Button, FocusAwareStatusBar, Pressable, Text, View } from '@/ui';
const { height, width } = Dimensions.get('screen');

// eslint-disable-next-line max-lines-per-function
export default function Onboarding() {
  const [currentStep, setCurrentStep] = useState(0);
  const [_, setIsFirstTime] = useIsFirstTime();
  const router = useRouter();

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % onboardingData.length);
  };

  return (
    <SafeAreaView className="flex-1 px-5">
      <View className="h-full w-full px-5">
        <FocusAwareStatusBar />
        <View className="flex-col justify-between flex-1">
          <View className="">
            <View className="mb-6 w-full flex-row items-center justify-between">
              <NImage
                source={require('../../../assets/images/mydeer1.png')}
                style={{ width: width * 0.31, height: height * 0.053 }}
                contentFit="contain"
              />
              <Pressable
                className="self-end rounded-xl bg-[#FFF9F5] px-6 py-3.5"
                onPress={nextStep}
              >
                <NImage
                  source={require('../../../assets/icons/arrow.png')}
                  contentFit="contain"
                  className="h-7 w-7"
                  tintColor={'#1E1E1E'}
                />
              </Pressable>
            </View>

            <OnboardingComponent
              label={onboardingData[currentStep].label}
              text={onboardingData[currentStep].text}
              image={onboardingData[currentStep].image}
            />
          </View>
          <View className="flex-1 flex-col justify-start">
            <Button
              variant="onboarding"
              size="onboarding"
              fullWidth={true}
              label="Get Started"
              onPress={() => {
                setIsFirstTime(false);
                router.replace('/auth/sign-up');
              }}
            />
            <View className="mt-2 flex-row items-center justify-center gap-4">
              <Text className="font-airbnb_bk text-base font-normal text-text-main700">
                Already have a account?
              </Text>
              <Pressable
                className="flex-row items-center"
                onPress={() => router.push('/auth/sign-in')}
              >
                <Text className="font-airbnb_md text-base font-medium text-secondary-650">
                  Login
                </Text>
                <NImage
                  source={require('../../../assets/icons/arrow.png')}
                  className="aspect-square w-4"
                  tintColor={'#F15F3A'}
                />
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const onboardingData = [
  {
    label: 'Sell, Buy, Rent',
    text: 'Connect, Collaborate & Conquer',
    image: require('../../../assets/images/illustration.png'),
  },
  {
    label: 'Trusted  Partners',
    text: 'Authentic, Secure and Fair Build your profile and connect with Verified Brokers',
    image: require('../../../assets/images/business.png'),
  },
  {
    label: 'Unlimited Free Listings & \nNationwide Reach',
    text: 'Connect with a click & List properties at no Cost',
    image: require('../../../assets/images/pricecuate.png'),
  },
];
