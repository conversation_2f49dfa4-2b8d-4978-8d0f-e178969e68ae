import { LinearGradient } from 'expo-linear-gradient';
import Entypo from '@expo/vector-icons/Entypo';
import { useRouter, useNavigation } from 'expo-router';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Pressable, StyleSheet, View } from 'react-native';

import type { FormType, SignInFormProps } from '@/components/sign-in';
import { SignInForm } from '@/components/sign-in';
import { useAuth } from '@/core';
import { FocusAwareStatusBar } from '@/ui';

export default function Login() {
  const router = useRouter();
  const navigation = useNavigation();
  const signIn = useAuth.use.signIn();

  // Update the onSubmit handler to match the correct type
  const onSubmit: SignInFormProps['onSubmit'] = (data) => {
    console.log(data);
    signIn({ access: 'access-token', refresh: 'refresh-token' });
    router.push('/auth/otp');
  };

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradient}
    >
      <SafeAreaView style={styles.safeArea}>
        <FocusAwareStatusBar />
        <View className="flex-row items-center justify-between px-5">
          <Pressable
            onPress={() => router.navigate('/auth/onboarding')}
            className="mt-3"
          >
            <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
          </Pressable>
        </View>
        <SignInForm onSubmit={onSubmit} />
      </SafeAreaView>
    </LinearGradient>
  );
}

// StyleSheet for better performance
const styles = StyleSheet.create({
  gradient: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
});
