import { Redirect } from 'expo-router';
import React, { useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { api } from '@/utils/api';
import { useAuth } from '@/core';

const index = () => {
  const { status } = useAuth();
  const { data: user, isLoading, error } = api.user.profile.useQuery();

  // Show loading state while checking auth and loading user data
  if (isLoading || status === 'idle') {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size={40} color="#d6330a" />
      </View>
    );
  }

  // If not authenticated or error loading user, go to onboarding
  if (status === 'signOut' || error || !user) {
    return <Redirect href="/auth/onboarding" />;
  }

  // If authenticated but onboarding not complete
  if (user && !user.onboardingStatus) {
    return <Redirect href="/home-screens/step-1" />;
  }

  // If authenticated and onboarding complete
  if (user) {
    return <Redirect href="/home" />;
  }

  // Fallback to onboarding if something unexpected happens
  return <Redirect href="/auth/onboarding" />;
};

export default index;
