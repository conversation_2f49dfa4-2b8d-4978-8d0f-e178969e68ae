/* eslint-disable max-lines-per-function */
import Entypo from '@expo/vector-icons/Entypo';
import { useLocalSearchParams, router } from 'expo-router';
import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import { useRouter } from 'expo-router';
import { format } from 'date-fns';
// import { WishlistButton } from ""
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';
import MapView, { Polygon, Marker } from 'react-native-maps';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from '@/ui';
import { api } from '@/utils/api';
import PropertyWishlistBtn from '@/components/property/wishlist-btn';
import PropertyShareBtn from '@/components/property/share-btn';
import { ZoomIn } from 'react-native-reanimated';
import { skipToken } from '@tanstack/react-query';
import { showMessage } from 'react-native-flash-message';
const { height, width } = Dimensions.get('screen');

interface FacilitiesProps {
  onPress: () => void;
  amenities: {
    id: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    fileKey: string | null;
    filePublicUrl: string | null;
    cloudinaryUrl: string | null;
    cloudinaryId: string | null;
  }[];
}

//Facilities Component
const Facilities: React.FC<FacilitiesProps> = ({ onPress, amenities }) => {
  if (amenities.length === 0) {
    return;
  }

  return (
    <View className="mt-2.5">
      <Heading title="Facilities" />
      <View>
        <View className="flex-row flex-wrap justify gap-2.5">
          {amenities.map(({ id, name, filePublicUrl, cloudinaryUrl }) => (
            <View key={id} className="mb-4 items-center w-20">
              <Pressable
                key={id}
                onPress={onPress}
                className={` self-center rounded-full bg-[#DED3CD4D] p-4`}
              >
                <NImage
                  source={cloudinaryUrl ?? filePublicUrl}
                  className="h-6 w-6"
                />
              </Pressable>
              <Text
                className={`font-airbnb_bk text-xs font-normal text-center`}
              >
                {name}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

interface OptionsProps {
  options: string[];
}

//Options Component
const Options: React.FC<OptionsProps> = ({ options }) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  return (
    <View className="flex-row flex-wrap items-center">
      {options.map((option) => (
        <Pressable
          key={option}
          onPress={() => setSelectedOption(option)}
          className={`mb-2 mr-3 self-start rounded-xl border border-primary-0 px-2 py-2.5 ${
            selectedOption === option ? 'bg-secondary-main700' : 'bg-white'
          }`}
        >
          <Text
            className={`font-airbnb_bk text-xs font-normal ${
              selectedOption === option ? 'text-primary-50' : 'text-text-600'
            }`}
          >
            {option}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

// heading and location component
interface PropsType {
  title: string;
  className?: string;
}

const Heading: React.FC<PropsType> = ({ title, className }) => (
  <Text
    className={`mb-2 font-airbnb_bd text-base font-bold text-text-600 ${className || ''}`}
  >
    {title}
  </Text>
);

const Location: React.FC<PropsType> = ({ title, className }) => (
  <View className={`flex-row items-center ${className || ''}`}>
    <NImage
      source={require('../../../../assets/icons/location2.png')}
      className="mr-1 h-4 w-4"
    />
    <Text
      className="font-airbnb_bk text-sm font-normal text-text-600"
      numberOfLines={1}
      ellipsizeMode="tail"
    >
      {title}
    </Text>
  </View>
);

// Add a new component for image with loading state
const GalleryImage = ({ source }: { source: string | null }) => {
  const [isLoading, setIsLoading] = useState(true);

  // Skip rendering if source is null
  if (!source) {
    return null;
  }

  return (
    <View className="mr-1.5 h-32 w-32 rounded-xl overflow-hidden">
      {isLoading && (
        <View className="absolute bottom-0 top-0 left-0 right-0 flex items-center justify-center bg-gray-100">
          <ActivityIndicator size="small" color="#F04D24" />
        </View>
      )}
      <NImage
        source={source}
        className="h-32 w-32 rounded-xl"
        onLoadStart={() => setIsLoading(true)}
        onLoadEnd={() => setIsLoading(false)}
      />
    </View>
  );
};

const Index = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  const { propertyId } = useLocalSearchParams<{ propertyId: string }>();
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    data: property,
    isLoading,
    error,
  } = api.homePage.getPropertyDetails.useQuery(
    propertyId ? { id: propertyId } : skipToken,
  );

  // to check if the current user is the property owner
  const propertyOwnerId = property?.user.id;
  const currentUserProfile = api.user.getProfileWithoutSensitiveData.useQuery();
  const currentUserId = currentUserProfile?.data?.id;
  const isPropertyOwner = propertyOwnerId === currentUserId;

  // Move the mutation hook outside of the render function
  const sendConnectionReqMutation =
    api.connection.sendConnectionRequest.useMutation();

  useEffect(() => {
    if (error) {
      router.back();
    }
  }, [error]);

  // Define handleClick outside of the render function
  const handleClick = () => {
    if (!property?.user.id) return;

    sendConnectionReqMutation.mutate(
      {
        receiverId: property.user.id,
      },
      {
        onSuccess: (opts) => {
          if (opts.warning) {
            showMessage({
              message: opts.message,
              type: 'info',
            });
            return;
          }

          showMessage({
            message: opts.message,
            type: 'success',
          });
        },
        onError: (opts) => {
          showMessage({
            message: opts.message,
            type: 'danger',
          });
        },
      },
    );
  };

  if (isLoading || !property) {
    return (
      <View className="flex flex-1">
        <ActivityIndicator size={24} color="#F04D24" />
      </View>
    );
  }

  const url =
    property.mediaSections?.length > 0 &&
    property.mediaSections[0]?.media[0]?.filePublicUrl;

  const images =
    property.mediaSections
      ?.flatMap((section) =>
        section.media.map(
          (media) => media.cloudinaryUrl ?? media.filePublicUrl,
        ),
      )
      .filter((url) => url) || [];

  const coordinates =
    (property.propertyMarkersLatLng as {
      lat: number;
      lng: number;
    }[]) || [];

  return (
    <View
      style={{
        marginBottom: insets.bottom,
        marginTop: 10,
        position: 'relative',
      }}
    >
      <View className="h-full w-full px-5">
        <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
          {/* Top header */}
          {/*<View className="mt-2.5 flex-row items-center justify-between px-5">*/}
          {/*  <Pressable onPress={() => navigation.goBack()}>*/}
          {/*    <Entypo name="chevron-thin-left" size={16} color="#451F0A" />*/}
          {/*  </Pressable>*/}
          {/*  <Pressable>*/}
          {/*    <NImage*/}
          {/*      source={require('../../../../assets/icons/dot3.png')}*/}
          {/*      className="h-8 w-8"*/}
          {/*    />*/}
          {/*  </Pressable>*/}
          {/*</View>*/}
          <Pressable
            onPress={() =>
              router.push({
                pathname: '/home-screens/gallery',
                params: {
                  images: JSON.stringify(
                    images.map((image, index) => ({
                      id: index,
                      source: image,
                    })),
                  ),
                },
              })
            }
            className="mt-2 py-[18px]"
          >
            {images && images.length > 0 ? (
              <NImage
                source={images[0]}
                contentFit="cover"
                className="h-[200px] w-full rounded-2xl"
              />
            ) : (
              <Text className="text-text-500">No images available</Text>
            )}
          </Pressable>
          {/* Details blur view */}
          <View className="rounded-2xl">
            {/* Top Part */}
            <View className="flex flex-row items-center justify-between gap-2 ">
              <View className="flex flex-1 max-w-[50%] gap-3">
                <Text className="font-airbnb_bd text-xl font-bold text-text-main700">
                  {property.propertyTitle}
                </Text>
                {property.propertyAddress && (
                  <Location title={property.propertyAddress} />
                )}
              </View>

              {isPropertyOwner ? (
                ''
              ) : (
                <View className="flex-row items-center">
                  <PropertyWishlistBtn propertyId={property.id} />
                  <PropertyShareBtn
                    propertyId={property.id}
                    propertyTitle={property.propertyTitle ?? ''}
                    aboutProperty={property.aboutProperty ?? ''}
                    bg={true}
                  />
                </View>
              )}
            </View>

            {/* Middle part */}
            {property.rating && (
              <View className="my-3 flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <NImage
                    source={require('../../../../assets/icons/yostar.png')}
                    className="mr-2 h-4 w-4"
                  />
                  <View className="flex-row items-center">
                    <Text className="mr-1 font-airbnb_bd text-sm font-bold text-text-600">
                      {property.rating}
                    </Text>
                    {/*<Text className="font-airbnb_bk text-sm font-normal text-text-400">*/}
                    {/*  (335)*/}
                    {/*</Text>*/}
                  </View>
                </View>
                {/*<View>*/}
                {/*  <Text className="font-airbnb_md text-sm font-medium text-text-500">*/}
                {/*    {property.} reviews*/}
                {/*  </Text>*/}
                {/*</View>*/}
              </View>
            )}

            {/* Bottom text part */}
            {/*<View className="mb-4">*/}
            {/*  <Text className="font-airbnb_bk text-sm font-normal text-text-600">*/}
            {/*    {property.aboutProperty}*/}
            {/*  </Text>*/}
            {/*  /!*<Pressable>*!/*/}
            {/*  /!*  <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700 ">*!/*/}
            {/*  /!*    Read more*!/*/}
            {/*  /!*  </Text>*!/*/}
            {/*  /!*</Pressable>*!/*/}
            {/*</View>*/}

            <Heading title="Gallery" className="mt-4" />

            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <Pressable
                className="flex-row"
                onPress={() =>
                  router.push({
                    pathname: '/home-screens/gallery',
                    params: {
                      images: JSON.stringify(
                        images.map((image, index) => ({
                          id: index,
                          source: image,
                        })),
                      ),
                    },
                  })
                }
              >
                {images && images.length > 0 ? (
                  images.map((image, index) => (
                    <View key={index} className="flex-row flex-1 gap-10">
                      <GalleryImage key={index} source={image} />
                    </View>
                  ))
                ) : (
                  <Text className="text-text-500">No images available</Text>
                )}
              </Pressable>
            </ScrollView>
          </View>

          {/* Map */}
          <View className="mt-8 flex-row items-center justify-between gap-3">
            <Heading title="Location" />
            <Location title={property.propertyAddress ?? ''} className="" />
          </View>
          {/* Map View */}
          <View className="mt-3 items-center justify-center">
            {coordinates && coordinates.length > 0 && (
              <MapView
                ref={mapRef}
                style={{
                  height: height * 0.1857,
                  width: width * 0.9,
                  borderRadius: 16,
                }}
                userInterfaceStyle="light"
                initialRegion={{
                  latitude: coordinates[0].lat,
                  longitude: coordinates[0].lng,
                  latitudeDelta: 0.0922,
                  longitudeDelta: 0.0421,
                }}
                // zoomEnabled={false}
                rotateEnabled={false}
                // scrollEnabled={false}
              >
                <Polygon
                  coordinates={coordinates.map(({ lat, lng }) => ({
                    latitude: lat,
                    longitude: lng,
                  }))}
                  fillColor="#F04D24"
                  strokeColor="#F04D24"
                />
                {coordinates.map(({ lat, lng }) => (
                  <Marker
                    key={lat + lng}
                    coordinate={{ latitude: lat, longitude: lng }}
                  />
                ))}
              </MapView>
            )}
          </View>
          <View className="mb-6 mt-3 flex-row items-center gap-2 flex-wrap">
            {property.utilities.map((utility) => {
              return (
                <Pressable
                  key={utility.id}
                  className={
                    'flex-row items-center self-start rounded-xl border border-primary-0 bg-white px-2 py-2.5 mb-2'
                  }
                >
                  <Text
                    className={
                      'font-airbnb_md text-xs font-medium text-text-600'
                    }
                  >
                    {utility.utility}{' '}
                  </Text>
                  {utility.distanceInKm && (
                    <Text
                      className={
                        'font-airbnb_bk text-xs font-normal text-text-600'
                      }
                    >
                      {utility.distanceInKm.toString()} km away
                    </Text>
                  )}
                </Pressable>
              );
            })}
          </View>

          {/* Property section */}
          {property.aboutProperty && (
            <View className="gap-3">
              <Heading title="About the property" />

              <View>
                <Text
                  className={`font-airbnb_bk text-sm font-normal text-text-main700 ${!isExpanded ? 'line-clamp-5' : ''}`}
                >
                  {property.aboutProperty}
                </Text>

                <Pressable
                  onPress={() => setIsExpanded(!isExpanded)}
                  className="mt-1"
                >
                  <Text className="font-airbnb_md text-sm text-secondary-main700">
                    {isExpanded ? 'Read less' : 'Read more'}
                  </Text>
                </Pressable>
              </View>
            </View>
          )}

          {/* Facilities */}

          <Facilities
            amenities={property.amenities}
            onPress={() => Alert.alert('Functionality not implemented!')}
          />

          {/* agent details */}
          <View className="mt-2.5">
            <Heading title={'Listed by '} />
            <View className="mb-4 mt-3 flex-row items-center justify-between rounded-xl border border-[#E9E2DD] px-4 py-3">
              {/* image and name part */}
              <View className="flex-row items-center">
                <NImage
                  source={
                    property.user.cloudinaryProfileImageUrl ??
                    property.user.filePublicUrl
                  }
                  className="aspect-square w-[86px] rounded-full"
                />

                <View className="ml-3">
                  <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-750">
                    {property.user.name}
                  </Text>
                  <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                    {property.user?.company?.companyName}
                  </Text>
                </View>
              </View>

              {/* rating & review part */}
              {property.user.rating && (
                <View>
                  <View className="mb-3 flex-row items-center self-end">
                    <NImage
                      source={require('../../../../assets/icons/star.png')}
                      className="h-4 w-4 rounded-2xl"
                    />
                    <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-text-600">
                      {property.user.rating}
                    </Text>
                  </View>

                  {/*<View>*/}
                  {/*  <Text className="font-airbnb_md text-xs font-medium text-text-600">*/}
                  {/*    59 Reviews*/}
                  {/*  </Text>*/}
                  {/*</View>*/}
                </View>
              )}
            </View>
            {/*<Pressable className="mb-6">*/}
            {/*  <Text className="font-airbnb_md text-base font-medium text-secondary-main700 ">*/}
            {/*    See All*/}
            {/*  </Text>*/}
            {/*</Pressable>*/}
          </View>

          {/* Comments */}
          {property.comments.length > 0 && (
            <View>
              {/* comment filter heading */}
              <View className="mb-6 flex-row items-center justify-between">
                <Heading title={`${property.comments.length} Comments`} />
                {/* Sir asked to comment the filter comments option for now ↓ */}
                {/* <View className="flex-row items-center gap-2">
                <Text className="font-airbnb_md text-xs font-medium text-[#838383]">
                  Sort By:
                </Text>
                <Pressable className="flex-row items-center rounded-[4px] border border-[#E9E2DD] py-1 pl-3 pr-1">
                  <Text className="font-airbnb_md text-sm font-medium text-text-550">
                    Newest
                  </Text>
                  <Entypo name="chevron-small-down" size={18} color="#838383" />
                </Pressable>
              </View> */}
              </View>

              {/* all comments */}
              <View>
                {property.comments.map((comment, index) => (
                  <View
                    key={index}
                    className="mb-6 flex-row items-center justify-between"
                  >
                    <View className="flex-row items-center">
                      <NImage
                        source={
                          comment.user.cloudinaryProfileImageUrl ??
                          comment.user.filePublicUrl
                        }
                        className="mr-3 h-14 w-14 rounded-full"
                        contentFit="contain"
                      />
                      <View>
                        <Text className="mb-1 font-airbnb_bd text-sm font-bold text-primary-750">
                          {comment.description}
                        </Text>
                        <Text className="font-airbnb_bk text-sm font-normal text-text-500">
                          {format(comment.createdAt, 'dd MMM yyy hh:mm a')}
                        </Text>
                      </View>
                    </View>
                    <Pressable>
                      <NImage
                        source={require('../../../../assets/icons/horizontaldot.png')}
                        className="h-5 w-5"
                      />
                    </Pressable>
                  </View>
                ))}
              </View>
            </View>
          )}
          <View className="mb-28" />
        </ScrollView>
      </View>

      {/* Bottom Button */}
      {!isPropertyOwner && (
        <BlurView
          intensity={30}
          tint="light"
          blurReductionFactor={4}
          style={{
            position: 'absolute',
            bottom:
              Platform.OS === 'ios' ? insets.bottom + 25 : insets.bottom - 25,
          }}
          experimentalBlurMethod="dimezisBlurView"
          className="w-full flex-1 flex-col justify-end bg-primary-50/30 px-5 py-3"
        >
          <Button label={'Contact Agent'} onPress={handleClick} />
        </BlurView>
      )}
    </View>
  );
};

export default Index;
