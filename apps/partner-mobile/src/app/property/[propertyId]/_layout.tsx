import { Stack } from 'expo-router';
import React from 'react';
import { Pressable } from 'react-native';
import { Image } from 'expo-image';
import { router } from 'expo-router';

export default function Layout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: '',
          headerStyle: {
            backgroundColor: '#FFFAF9',
          },
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Image
                source={require('../../../../assets/icons/bcak.png')}
                className="h-4 w-2.5"
              />
            </Pressable>
          ),
        }}
      />
    </Stack>
  );
}
