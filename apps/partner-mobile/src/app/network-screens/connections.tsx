import { FlashList } from '@shopify/flash-list';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Pressable,
  RefreshControl,
  ScrollView,
  Text,
  View,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { api } from '@/utils/api';
import NetworkRequestConnectionComponent from '@/components/network-request-connection-component';
import { NetworkCustomerChat } from '@/components/network-customer-chat';

const TabsList = [
  { key: 'all', name: 'All' },
  { key: 'partners', name: 'Partners' },
  { key: 'customers', name: 'Customers' },
];

const Connections = () => {
  const [selected, setSelected] = useState<'all' | 'partners' | 'customers'>(
    'all',
  );
  const router = useRouter();
  const { query } = useLocalSearchParams<{ query?: string }>();
  const { data: profile } = api.user.profile.useQuery();
  const {
    data: acceptedConnectionRequests,
    isLoading: isLoadingAcceptedConnections,
    refetch: onRefresh,
  } = api.connection.getAcceptedConnectionRequests.useQuery({
    q: query,
  });

  if (isLoadingAcceptedConnections) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size={40} color="#fff" />
      </View>
    );
  }

  if (
    (acceptedConnectionRequests?.acceptedConnectionRequestsFromPartner.length ||
      acceptedConnectionRequests?.acceptedConnectionRequestsFromCustomer
        .length) === 0
  ) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-center mt-5 text-orange">No Connections</Text>
      </View>
    );
  }

  return (
    <ScrollView
      className="h-full px-5 mt-5"
      refreshControl={
        <RefreshControl
          refreshing={isLoadingAcceptedConnections}
          onRefresh={onRefresh}
        />
      }
    >
      {/* switchable button */}
      <View className="pb-3">
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 0 }}
          className="w-full "
        >
          <View className="flex-row gap-5 items-center justify-between">
            {TabsList.map((item) => (
              <Pressable
                key={item.key}
                onPress={() =>
                  setSelected(item.key as 'all' | 'partners' | 'customers')
                }
                className={`px-5 py-3 rounded-lg justify-center items-center ${
                  selected === item.key
                    ? 'bg-secondary-main700'
                    : 'bg-secondary-100 '
                }`}
              >
                <Text
                  className={`font-normal text-sm font-airbnb_md ${
                    selected === item.key
                      ? 'text-white'
                      : 'text-secondary-main700'
                  }`}
                >
                  {item.name}
                </Text>
              </Pressable>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* for agents */}
      {(selected === 'all' || selected === 'partners') &&
        acceptedConnectionRequests?.acceptedConnectionRequestsFromPartner && (
          <FlashList
            showsVerticalScrollIndicator={false}
            data={
              acceptedConnectionRequests?.acceptedConnectionRequestsFromPartner
            }
            estimatedItemSize={210}
            keyExtractor={(item) => item.id.toString()}
            className="flex-1"
            renderItem={({ item }) => {
              return (
                <NetworkRequestConnectionComponent
                  item={item}
                  onChatPress={() =>
                    router.push({
                      pathname: '/chat-screens/[id]',
                      params: {
                        id: item.id,
                        chatType:
                          profile?.id === item.sender.id
                            ? 'agent'
                            : profile?.id === item.receiver.id
                              ? 'agent'
                              : 'customer',
                        name:
                          profile?.id === item.sender.id
                            ? item?.receiver.name
                            : item?.sender.name,
                        profileImage:
                          profile?.id === item.sender.id
                            ? (item?.receiver.cloudinaryProfileImageUrl ??
                              item?.receiver.filePublicUrl)
                            : (item?.sender.cloudinaryProfileImageUrl ??
                              item?.sender.filePublicUrl),
                      },
                    })
                  }
                  onViewProfilePress={() =>
                    router.push(
                      `/network-screens/${profile?.id === item.sender.id ? item.receiver.id : item.sender.id}`,
                    )
                  }
                />
              );
            }}
          />
        )}

      {/* for customers */}
      {(selected === 'all' || selected === 'customers') &&
        acceptedConnectionRequests?.acceptedConnectionRequestsFromCustomer && (
          <FlashList
            showsVerticalScrollIndicator={false}
            data={
              acceptedConnectionRequests?.acceptedConnectionRequestsFromCustomer
            }
            estimatedItemSize={210}
            keyExtractor={(item) => item.id.toString()}
            className="flex-1"
            renderItem={({ item }) => {
              return (
                <NetworkCustomerChat
                  item={item}
                  Chat={() =>
                    router.push({
                      pathname: '/chat-screens/[id]',
                      params: {
                        id: item.id,
                        chatType: item.state,
                        name: item.customer.name,
                        profileImage: item.customer.profileImagePublicUrl,
                      },
                    })
                  }
                />
              );
            }}
          />
        )}
    </ScrollView>
  );
};

export default Connections;
