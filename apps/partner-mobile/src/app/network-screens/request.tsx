import { FlashList } from '@shopify/flash-list';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import {
  ScrollView,
  RefreshControl,
  View,
  Text,
  ActivityIndicator,
  Pressable,
} from 'react-native';
import NetworkRequestConnectionComponent from '@/components/network-request-connection-component';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';

const TabsList = [
  { key: 'all', name: 'All' },
  { key: 'sent', name: 'Sent' },
  { key: 'received', name: 'Received' },
];

// eslint-disable-next-line max-lines-per-function
const Request = () => {
  const [selected, setSelected] = useState<'all' | 'sent' | 'received'>('all');
  const { query } = useLocalSearchParams<{ query?: string }>();

  // get pending connection requests
  const {
    data: pendingConnectionRequests,
    isLoading: isLoadingPendingConnections,
    refetch: refetchPendingConnectionRequests,
  } = api.connection.getAllPendingConnectionRequests.useQuery({
    q: query,
  });

  // get sent connection requests
  const {
    data: sentConnectionRequests,
    isLoading: isLoadingSentConnections,
    refetch: refetchSentConnectionRequests,
  } = api.connection.getAllSentConnectionRequests.useQuery({
    q: query,
  });

  // cancel sent connection request
  const { mutate: cancelConnectionRequest } =
    api.connection.cancelConnectionRequest.useMutation();

  const handleCancelConnectionRequest = ({
    connectionReqId,
  }: {
    connectionReqId: string;
  }) => {
    cancelConnectionRequest(
      { reqId: connectionReqId },
      {
        onSuccess: (opts) => {
          showMessage({
            message: opts.message,
            type: 'success',
          });
          trpcUtils.invalidate().catch((e) => {
            console.log('invalidated');
            showMessage({
              message: 'Failed to refresh data',
              type: 'danger',
            });
          });
        },
        onError: (opts) => {
          showMessage({
            message: opts.message,
            type: 'danger',
          });
        },
      },
    );
  };

  // accept connection request
  const { mutate: acceptConnectionReq } =
    api.connection.acceptConnectionRequest.useMutation();
  const { mutate: rejectConnectionReq } =
    api.connection.rejectConnectionRequest.useMutation();

  const trpcUtils = api.useUtils();

  const onRefresh = async () => {
    await refetchPendingConnectionRequests();
    await refetchSentConnectionRequests();
  };

  const handleAcceptConnection = ({
    connectionReqId,
  }: {
    connectionReqId: string;
  }) => {
    acceptConnectionReq(
      { reqId: connectionReqId },
      {
        onSuccess: (opts) => {
          showMessage({
            message: opts.message,
            type: 'success',
          });
          trpcUtils.invalidate().catch((e) => {
            console.log('invalidated');
          });
        },
        onError: (opts) => {
          showMessage({
            message: opts.message,
            type: 'danger',
          });
        },
      },
    );
  };

  const handleRejectConnection = ({
    connectionReqId,
  }: {
    connectionReqId: string;
  }) => {
    rejectConnectionReq(
      { reqId: connectionReqId },
      {
        onSuccess: (opts) => {
          showMessage({
            message: opts.message,
            type: 'success',
          });
          trpcUtils.invalidate().catch((e) => {
            console.log('invalidated');
          });
        },
        onError: (opts) => {
          showMessage({
            message: opts.message,
            type: 'danger',
          });
        },
      },
    );
  };

  if (isLoadingPendingConnections || isLoadingSentConnections) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size={40} color="#fff" />
      </View>
    );
  }

  if (
    pendingConnectionRequests?.length === 0 &&
    sentConnectionRequests?.length === 0
  ) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-center mt-5 text-orange">No Connections</Text>
      </View>
    );
  }

  return (
    <ScrollView
      className="h-full px-5 mt-5"
      refreshControl={
        <RefreshControl
          refreshing={isLoadingPendingConnections || isLoadingSentConnections}
          onRefresh={onRefresh}
        />
      }
    >
      {/* switchable button */}
      <View className="pb-3">
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 0 }}
          className="w-full "
        >
          <View className="flex-row gap-5 items-center justify-between">
            {TabsList.map((item) => (
              <Pressable
                key={item.key}
                onPress={() =>
                  setSelected(item.key as 'all' | 'sent' | 'received')
                }
                className={`px-5 py-3 rounded-lg justify-center items-center ${
                  selected === item.key
                    ? 'bg-secondary-main700'
                    : 'bg-secondary-100 '
                }`}
              >
                <Text
                  className={`font-normal text-sm font-airbnb_md ${
                    selected === item.key
                      ? 'text-white'
                      : 'text-secondary-main700'
                  }`}
                >
                  {item.name}
                </Text>
              </Pressable>
            ))}
          </View>
        </ScrollView>
      </View>

      {(selected === 'all' || selected === 'received') && (
        <FlashList
          showsVerticalScrollIndicator={false}
          data={pendingConnectionRequests}
          estimatedItemSize={210}
          keyExtractor={(item) => item.id.toString()}
          className="flex-1"
          renderItem={({ item }) => {
            return (
              <NetworkRequestConnectionComponent
                item={item}
                onCardPress={() => console.log('card pressed')}
                onConnectPress={() =>
                  handleAcceptConnection({ connectionReqId: item.id })
                }
                onDeclinePress={() =>
                  handleRejectConnection({ connectionReqId: item.id })
                }
              />
            );
          }}
        />
      )}
      {(selected === 'all' || selected === 'sent') && (
        <FlashList
          showsVerticalScrollIndicator={false}
          data={sentConnectionRequests}
          estimatedItemSize={210}
          keyExtractor={(item) => item.id.toString()}
          className="flex-1"
          renderItem={({ item }) => {
            return (
              <NetworkRequestConnectionComponent
                item={item}
                onCancelPress={() =>
                  handleCancelConnectionRequest({ connectionReqId: item.id })
                }
                onViewProfilePress={() =>
                  router.push(`/network-screens/${item.receiver.id}`)
                }
              />
            );
          }}
        />
      )}
    </ScrollView>
  );
};

export default Request;
