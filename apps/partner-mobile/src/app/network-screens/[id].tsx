import Entypo from '@expo/vector-icons/Entypo';
import { BlurView } from 'expo-blur';
import { Image as NImage, ImageBackground } from 'expo-image';
import { useNavigation } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
  StyleSheet,
  RefreshControl,
  findNodeHandle,
} from 'react-native';
import { useLocalSearchParams, useRouter, Href } from 'expo-router';
const { height, width } = Dimensions.get('screen');
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import MapView, { Marker } from 'react-native-maps';
import { Button } from '@/ui';
import { api } from '@/utils/api';
import UserPic from '@/components/user-pic';
import { showMessage } from 'react-native-flash-message';
import { formatDistanceToNow } from 'date-fns';
import { useEvent } from 'expo';
import { useVideoPlayer, VideoView } from 'expo-video';
import { Post } from '../home-top-tabs/feed';
import PropertiesComponent from '@/components/properties-component/properties-component';
import AgentInfoCard from '@/components/agent-info-card';
import LikeAgentButton from '@/components/agent-like-share/agent-like-btn';
import ShareAgentButton from '@/components/agent-like-share/agent-share-btn';

export type IAgentReviewCard = {
  connection: {
      customer: {
          city: {
              id: string;
              name: string;
              createdAt: Date;
              updatedAt: Date;
              cityMarkersLatLng: Prisma.JsonValue;
              northMaxLat: number | null;
              southMaxLat: number | null;
              eastMaxLng: number | null;
              westMaxLng: number | null;
          } | null;
          cityId: string | null;
      };
  } | null;
  ratedBy: {
    id: string;
    name: string;
    profileImagePublicUrl: string | null;
    cloudinaryImagePublicUrl: string | null;
  };
}
const Stats = ({
  value,
  label,
  img,
}: {
  value: string | number;
  label: string;
  img: any;
}) => {
  return (
    <View className="items-center">
      <Text className="text-lg font-semi-bold font-airbnb_bd text-primary-700">
        {value}
      </Text>
      <View className="mt-1.5 flex-row items-center">
        <NImage source={img} className="h-5 w-5" />
        <Text className="ml-1 text-sm font-airbnb_bk font-normal text-text-500">
          {label}
        </Text>
      </View>
    </View>
  );
};

const VideoPlayer = ({ url }: { url: string }) => {
  const player = useVideoPlayer(url, (player) => {
    player.loop = false;
    player.play();
  });

  return (
    <VideoView
      style={{ height: '100%', width: '100%' }}
      player={player}
      allowsFullscreen
      allowsPictureInPicture
    />
  );
};

const AgentDetails = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { id } = useLocalSearchParams<{ id: string }>();

  // Ensure we have a valid id
  if (!id) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-center mt-5">Invalid agent ID</Text>
      </View>
    );
  }

  const sendConnectionReqMutation =
    api.connection.sendConnectionRequest.useMutation();
  const {
    data: details,
    isPending: isPendingDetails,
    error: errorDetails,
    refetch,
  } = api.homePage.getAgentDetails.useQuery(
    {
      id: id,
    },
    {
      enabled: !!id,
    },
  );
  const {
    data: soldProperties,
    isPending: isPendingSoldProperties,
    error: errorSoldProperties,
  } = api.homePage.soldProperties.useQuery(
    {
      id: id,
    },
    {
      enabled: !!id,
    },
  );
  const [selected, setSelected] = useState<'post' | 'property'>('post');
  const navigation = useNavigation();
  const scrollViewRef = useRef<ScrollView | null>(null);
  const scrollToRef = useRef<View | null>(null);
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  // testimonial data n video player ref
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);
  const videoPlayer = currentVideoUrl
    ? useVideoPlayer(currentVideoUrl, (player) => {
        player.loop = false;
        player.play();
      })
    : null;
  const { data: agentReviews } = api.agentReviews.getAgentReviews.useQuery({
    agentId: id,
  });

  const propertyDetails = [
    {
      title: 'Website:',
      icon: require('../../../assets/icons/web.png'),
      value: details?.company?.companyWebsiteLink || '',
    },
    {
      title: 'Location:',
      icon: require('../../../assets/icons/location.png'),
      value: details?.company?.companyLocation || '',
    },
    {
      title: 'Mobile:',
      value: details?.company?.phoneNumber || '',
    },
    {
      title: 'Email:',
      value: details?.company?.email || '',
    },
    {
      title: 'Fax:',
      value: details?.company?.fax || '',
    },
  ].filter((item) => item.value.trim());

  const handlePropertyPress = () => {
    setSelected('property');
    if (scrollToRef.current && scrollViewRef.current) {
      const scrollHandle = findNodeHandle(scrollViewRef.current);
      if (scrollHandle) {
        scrollToRef.current.measureLayout(
          scrollHandle,
          (x, y) => {
            scrollViewRef.current?.scrollTo({ y, animated: true });
          },
          () => {
            console.log('measurement failed');
          },
        );
      }
    }
  };

  const handlePostPress = () => {
    setSelected('post');
    if (scrollToRef.current && scrollViewRef.current) {
      const scrollHandle = findNodeHandle(scrollViewRef.current);
      if (scrollHandle) {
        scrollToRef.current.measureLayout(
          scrollHandle,
          (x, y) => {
            scrollViewRef.current?.scrollTo({ y, animated: true });
          },
          () => {
            console.log('measurement failed');
          },
        );
      }
    }
  };

  const handleClick = () => {
    sendConnectionReqMutation.mutate(
      {
        receiverId: id,
      },
      {
        onSuccess: (opts) => {
          if (opts.warning) {
            showMessage({
              message: opts.message,
              type: 'info',
            });
            return;
          }

          showMessage({
            message: opts.message,
            type: 'success',
          });
        },
        onError: (opts) => {
          showMessage({
            message: opts.message,
            type: 'danger',
          });
        },
      },
    );
  };

  if (isPendingDetails || isPendingSoldProperties) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator
          className="text-center mt-5"
          color="#F04D24"
          size="large"
        />
      </View>
    );
  }

  if (errorDetails || errorSoldProperties) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-center mt-5">Error</Text>
      </View>
    );
  }

  if (!details || !soldProperties) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-center mt-5">No data</Text>
        <Pressable
          className="mt-4 px-4 py-2 bg-primary-700 rounded-md"
          onPress={() => navigation.goBack()}
        >
          <Text className="text-white">Go Back</Text>
        </Pressable>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-[#FFF8F7]">
      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        className="mb-5"
        contentContainerStyle={{ paddingBottom: 100 }}
        refreshControl={
          <RefreshControl refreshing={isPendingDetails} onRefresh={refetch} />
        }
      >
        <View style={{ paddingTop: insets.top }} className="px-5 relative">
          {/* Agent Profile */}
          <View
            className="relative rounded-xl overflow-hidden bg-white"
            style={[
              { borderBottomLeftRadius: 12, borderBottomRightRadius: 12 },
            ]}
          >
            <ImageBackground
              className="items-center"
              key={details?.bgFileKey}
              source={
                details?.cloudinaryBgImageUrl
                  ? { uri: details?.cloudinaryBgImageUrl }
                  : details?.bgFilePublicUrl
                    ? { uri: details?.bgFilePublicUrl }
                    : require('../../../assets/images/propertyyo.png')
              }
              style={{
                height: height * 0.1931,
                width: width * 0.907,
                padding: 20,
              }}
              imageStyle={{ borderTopLeftRadius: 12, borderTopRightRadius: 12 }}
              contentFit="cover"
              onLoadStart={() => {
                if (details?.bgFilePublicUrl) {
                  setIsLoading((prev) => ({ ...prev, bgImage: true }));
                }
              }}
              onLoadEnd={() =>
                setIsLoading((prev) => ({ ...prev, bgImage: false }))
              }
              onError={() => {
                setIsLoading((prev) => ({ ...prev, bgImage: false }));
              }}
            >
              <View className="flex-row items-center justify-between">
                <Pressable
                  className="bg-primary-100 rounded-full self-start p-2"
                  onPress={() => navigation.goBack()}
                >
                  <Entypo name="chevron-thin-left" size={16} color="#252525" />
                </Pressable>
                {/* <Pressable className="bg-primary-100 rounded-md self-start">
                  <NImage
                    source={require('../../../assets/icons/dot1.png')}
                    className="h-8 w-8"
                  />
                </Pressable> */}
              </View>

              <View className="absolute bottom-[-24px] left-6">
                <View className="bg-white rounded-full p-1">
                  <UserPic
                    picUrl={
                      details.cloudinaryProfileImageUrl
                        ? details.cloudinaryProfileImageUrl
                        : details.filePublicUrl
                    }
                    size={96}
                    color="#784100"
                    className="h-24 w-24 rounded-full"
                  />
                </View>
              </View>
              {(isLoading.bgImage || isLoading.profileImage) && (
                <View className="absolute top-0 left-0 right-0 bottom-0 items-center justify-center bg-black/30">
                  <ActivityIndicator size="large" color="#F04D24" />
                </View>
              )}
            </ImageBackground>

            <View
              className="px-4 pb-4 bg-white border-b-[1.5px] border-secondary-100"
              style={{
                borderBottomLeftRadius: 12,
                borderBottomRightRadius: 12,
                marginTop: 24,
              }}
            >
              {/* details */}
              <View className="mt-4">
                <View className="flex-row items-center justify-between">
                  <View style={{ maxWidth: '60%' }}>
                    <View>
                      <Text
                        numberOfLines={1}
                        ellipsizeMode="tail"
                        className="font-airbnb_xbd text-xl font-extrabold text-secondary-800"
                      >
                        {details.name}
                      </Text>
                      <Text
                        numberOfLines={2}
                        ellipsizeMode="tail"
                        className="mt-1 font-airbnb_md text-sm font-medium text-text-600"
                      >
                        {details.company?.companyName ?? ' '}
                      </Text>
                    </View>
                  </View>

                  {/* rating & review part */}
                  <View style={{ maxWidth: '40%' }} className="ml-auto">
                    <View className="mb-2 flex-row items-center self-end">
                      <NImage
                        source={require('../../../assets/icons/star.png')}
                        className="h-4 w-4"
                      />
                      <Text
                        className="ml-0.5 font-airbnb_md text-sm font-medium text-text-600"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {details.rating || 0}
                      </Text>
                    </View>

                    <View className="mt-2">
                      <Text
                        className="font-airbnb_md text-xs font-medium text-text-600"
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {details.reviews || 0} Reviews
                      </Text>
                    </View>
                  </View>
                </View>

                <View className="mt-2">
                  <Text className="text-sm font-normal font-airbnb_bk text-text-500">
                    {details.userLocation ?? 'N/A Location'}
                  </Text>
                </View>
              </View>

              {/* Stats */}
              <View className="mt-5 space-y-5">
                <View className="mb-2.5 flex-row items-center justify-between px-4">
                  <Stats
                    value={details.propertiesSold ?? 'N/A'}
                    label="Total sales closed"
                    img={require('../../../assets/icons/homeicon.png')}
                  />
                  <Stats
                    value={`${details.experience ?? '0'} yrs`}
                    label="Experience"
                    img={require('../../../assets/icons/experience3.png')}
                  />
                </View>
                <View className="mt-2.5 flex-row items-center justify-between px-4">
                  <Stats
                    value={
                      details.createdAt
                        ? formatDistanceToNow(new Date(details.createdAt)) +
                          ' ago'
                        : 'N/A'
                    }
                    label="Active"
                    img={require('../../../assets/icons/clock.png')}
                  />
                  <Stats
                    value={
                      details.receivedConnectionRequests.length +
                      details.sentConnectionRequests.length +
                      details.coustomerConnections.length
                    }
                    label="Connections"
                    img={require('../../../assets/icons/handshake.png')}
                  />
                </View>
              </View>

              {/* Buttons */}
              <View className="mt-5 space-y-4">
                <View className="items-center flex-row justify-between gap-4">
                  <Button
                    label="Contact Agent"
                    className="w-full"
                    onPress={handleClick}
                    loading={sendConnectionReqMutation.isPending}
                  />
                </View>

                <View className="flex-row gap-4">
                  <LikeAgentButton agentId={id} />
                  <ShareAgentButton
                    agentId={id}
                    agentName={details.name}
                    aboutAgent={details?.company?.companyName ?? ''}
                  />
                </View>
              </View>
            </View>
          </View>

          <View className="mt-3 py-4 border-[1.5px] rounded-xl border-secondary-100 bg-white">
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="px-4 font-airbnb_bd text-lg font-extrabold text-secondary-750"
            >
              Activity
            </Text>
            <Text className="my-3 h-[0.1px] bg-[#E0DFDC] rounded-full" />

            {/* switchable button */}
            <View className="my-5 w-2/3 px-4 flex-row items-center justify-center">
              <View className="flex-row items-center rounded-lg bg-[#f1f1f1] p-1">
                <Pressable
                  className={`flex-1 items-center rounded-lg px-6 py-2.5 ${selected === 'post' ? 'bg-primary-500' : 'bg-transparent'}`}
                  onPress={handlePostPress}
                >
                  <Text
                    className={`font-airbnb_md text-sm font-medium ${selected === 'post' ? 'text-primary-800' : 'text-text-500'}`}
                  >
                    Post
                  </Text>
                </Pressable>

                <Pressable
                  className={`flex-1 items-center rounded-lg px-6 py-2.5 ${selected === 'property' ? 'bg-primary-500' : 'bg-transparent'}`}
                  onPress={handlePropertyPress}
                >
                  <Text
                    className={`font-airbnb_md text-sm font-medium ${selected === 'property' ? 'text-primary-800' : 'text-text-500'}`}
                  >
                    Property
                  </Text>
                </Pressable>
              </View>
            </View>

            {/* post content */}
            {selected === 'post' && (
              <View ref={scrollToRef}>
                <Text className="mb-4 pl-5 font-airbnb_md text-sm font-medium text-text-600">
                  Posts ({details?.posts?.length ?? 0})
                </Text>

                {details?.posts?.length === 0 ? (
                  <Text className="pl-5 font-airbnb_bk text-sm font-normal text-text-600">
                    No posts listed
                  </Text>
                ) : (
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ paddingHorizontal: 4 }}
                    bounces={false}
                  >
                    <View className="flex-row">
                      {details?.posts?.map((item: any) => (
                        <View
                          key={item.id}
                          style={{
                            width: width * 0.8,
                            marginRight: 10,
                          }}
                          className="flex-1"
                        >
                          <Post post={item} />
                        </View>
                      ))}
                    </View>
                  </ScrollView>
                )}
              </View>
            )}

            {/* property content */}
            {selected === 'property' && (
              <View ref={scrollToRef}>
                <Text className="pl-5 font-airbnb_md text-sm font-medium text-text-600">
                  Properties ({details?.properties?.length ?? 0})
                </Text>

                {details?.properties?.length === 0 ? (
                  <Text className="pl-5 font-airbnb_bk text-sm font-normal text-text-600">
                    No properties listed
                  </Text>
                ) : (
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ paddingHorizontal: 4 }}
                    bounces={false}
                  >
                    <View className="flex-row">
                      {details?.properties?.map((item: any) => (
                        <View
                          key={item.id}
                          style={{
                            width: width * 0.8,
                            marginRight: 10,
                          }}
                          className="flex-1"
                        >
                          <PropertiesComponent
                            item={item}
                            handlePress={() =>
                              router.push({
                                pathname: '/property/[propertyId]',
                                params: {
                                  propertyId: item.id,
                                },
                              })
                            }
                            isLiked={!!item.customerFavourites.length}
                          />
                        </View>
                      ))}
                    </View>
                  </ScrollView>
                )}
              </View>
            )}
          </View>

          {details?.bio && (
            <View className="mt-5 ">
              <AgentInfoCard<false> heading={'Bio'} description={details.bio} />
            </View>
          )}

          {details?.company?.about && (
            <View className="mt-5 ">
              <AgentInfoCard<true>
                heading={'About Company'}
                description={details.company.about}
                companyDetails={propertyDetails}
              />
            </View>
          )}

          {details?.operationArea && details.operationArea.length > 0 && (
            <View className="mt-5 p-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
              <Text className="mb-3 text-lg font-airbnb_bd font-bold text-primary-750">
                Operational Areas
              </Text>
              <Text className="mb-3 text-base font-medium font-airbnb_md text-text-500">
                {details?.company?.companyLocation}
              </Text>
              <View className="flex-row flex-wrap gap-2">
                {details.operationArea.map(
                  (area: { name: string; id: string }, index: number) => (
                    <View
                      key={index}
                      className="mb-2 self-start bg-primary-100 rounded-md py-1 px-3 items-center"
                    >
                      <Text className="text-base font-medium font-airbnb_md text-primary-750">
                        {area.name}
                      </Text>
                    </View>
                  ),
                )}
              </View>
            </View>
          )}

          {details?.languages && details.languages.length > 0 && (
            <View className="mt-5 p-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
              <Text className="mb-3 text-lg font-airbnb_bd font-bold text-primary-750">
                Language
              </Text>
              <View className="flex-row flex-wrap gap-2">
                {details.languages.map(
                  (language: { name: string; id: string }, index: number) => (
                    <View
                      key={index}
                      className="mb-2 self-start bg-primary-100 rounded-md py-1 px-3 items-center"
                    >
                      <Text className="text-base font-medium font-airbnb_md text-primary-750">
                        {language.name}
                      </Text>
                    </View>
                  ),
                )}
              </View>
            </View>
          )}

          {/* testimonials */}
          {agentReviews && agentReviews.length > 0 && (
            <View className="mt-5 py-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
              <Text className="mb-3 px-4 text-lg font-airbnb_bd font-bold text-primary-750">
                Users Testimonials
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingHorizontal: 16 }}
              >
                {agentReviews.map((review: IAgentReviewCard, index: number) => (
                  <Pressable
                    key={index}
                    className="mr-4 rounded-xl overflow-hidden aspect-[9/16] object-cover"
                    style={{ height: 460, width: 316 }}
                  >
                    <View className="h-full w-full bg-black/50 rounded-xl shadow-md relative object-cover">
                      {/* Video Player yo*/}
                      <View className="justify-center items-center relative">
                        {review.cloudinaryUrl ? (
                          <>
                            <VideoPlayer url={review.cloudinaryUrl} />
                            {isLoading[review.id] && (
                              <View className="absolute top-0 left-0 right-0 bottom-0 items-center justify-center bg-black/30">
                                <ActivityIndicator
                                  size="large"
                                  color="#F04D24"
                                />
                              </View>
                            )}
                          </>
                        ) : review.filePublicUrl ? (
                          <VideoPlayer url={review.filePublicUrl} />
                        ) : (
                          <Text className="text-center text-text-500 font-airbnb_md text-base font-medium">
                            Not Available now
                          </Text>
                        )}
                      </View>

                      {/* user info yo*/}
                      <View className="p-4 bg-transparent absolute bottom-0 left-0 right-0">
                        <View className="flex-row items-center">
                          <NImage
                            key={review?.ratedBy?.cloudinaryImagePublicUrl}
                            source={
                              review?.ratedBy?.cloudinaryImagePublicUrl
                                ? {
                                    uri: review.ratedBy
                                      .cloudinaryImagePublicUrl,
                                  }
                                : review?.ratedBy?.profileImagePublicUrl
                                  ? {
                                      uri: review.ratedBy.profileImagePublicUrl,
                                    }
                                  : require('@assets/icons/default-user.png')
                            }
                            className="h-10 w-10 rounded-full"
                          />
                          <View className="ml-2">
                            <Text className="font-airbnb_bd text-base font-bold text-white">
                              {review?.ratedBy?.name || 'N/A Customer Name'}
                            </Text>
                            <View className="flex-row items-center">
                              <NImage
                                source={require('@assets/icons/star.png')}
                                className="h-3 w-3 mr-1"
                              />
                              <Text className="font-airbnb_md text-xs text-text-100">
                                {review?.userStarsCount || 'N/A'}
                              </Text>
                              <Text
                                className="max-w-xs ml-2 font-airbnb_md font-medium text-sm text-text-100"
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                {review?.connection?.customer?.city?.name ||
                                  'N/A city'}
                              </Text>
                            </View>
                          </View>
                        </View>
                        <Text
                          className="mt-2 self-start font-airbnb_md text-center text-text-100"
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        >
                          {review?.userRatingMessage || ''}
                        </Text>
                      </View>
                    </View>
                  </Pressable>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Button */}
      <View className="absolute bottom-5 w-full bg-primary-50 px-5 py-3">
        {Platform.OS === 'ios' ? (
          <BlurView
            intensity={30}
            tint="light"
            blurReductionFactor={4}
            experimentalBlurMethod="dimezisBlurView"
          >
            <Button
              label="Contact Agent"
              onPress={handleClick}
              loading={sendConnectionReqMutation.isPending}
            />
          </BlurView>
        ) : (
          <Button
            label="Contact Agent"
            onPress={handleClick}
            loading={sendConnectionReqMutation.isPending}
          />
        )}
      </View>
    </View>
  );
};

export default AgentDetails;

const styles = StyleSheet.create({
  Shadow: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
});
