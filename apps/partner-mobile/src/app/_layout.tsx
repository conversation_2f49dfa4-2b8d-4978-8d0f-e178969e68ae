// Import  global CSS file
import '../../global.css';

import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { useEffect } from 'react';
import React from 'react';

import { Platform, StyleSheet } from 'react-native';
import FlashMessage from 'react-native-flash-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import * as SplashScreen from 'expo-splash-screen';

import { APIProvider } from '@/api';
import { hydrateAuth, loadSelectedTheme } from '@/core';
import { useThemeConfig } from '@/core/use-theme-config';
import { TRPCProvider } from '@/utils/api';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
// Include the OneSignal package
import {OneSignal, LogLevel} from 'react-native-onesignal';
import { SystemBars } from 'react-native-edge-to-edge';
import { FocusAwareStatusBar } from '@/ui';

export { ErrorBoundary } from 'expo-router';

// Initialize auth and theme
hydrateAuth();
loadSelectedTheme();

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

SplashScreen.setOptions({
  duration: 500,
  fade: true,
});

export const unstable_settings = {
  initialRouteName: 'index',
};

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    Inter: require('../../assets/fonts/Inter.ttf'),
    AirbnbW_XBd: require('../../assets/fonts/AirbnbCereal_W_XBd.otf'),
    AirbnbW_Md: require('../../assets/fonts/AirbnbCereal_W_Md.otf'),
    AirbnbW_Lt: require('../../assets/fonts/AirbnbCereal_W_Lt.otf'),
    AirbnbW_Blk: require('../../assets/fonts/AirbnbCereal_W_Blk.otf'),
    AirbnbW_Bk: require('../../assets/fonts/AirbnbCereal_W_Bk.otf'),
    AirbnbW_Bd: require('../../assets/fonts/AirbnbCereal_W_Bd.otf'),
  });

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  // Initialize OneSignal in useEffect to ensure it runs only once
  useEffect(() => {
    // Enable verbose logging for debugging (remove in production)
    OneSignal.Debug.setLogLevel(LogLevel.Verbose);
    // Initialize with your OneSignal App ID
    OneSignal.initialize('************************************');
    // Use this method to prompt for push notifications.
    // We recommend removing this method after testing and instead use In-App Messages to prompt for notification permission.
    OneSignal.Notifications.requestPermission(false);
  }, []); // Ensure this only runs once on app mount

  return (
    <TRPCProvider>
      <Providers>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="index" />
          <Stack.Screen name="auth" />
          <Stack.Screen name="(app)" />
          <Stack.Screen name="home-screens" />
          <Stack.Screen name="upload-post" />
          <Stack.Screen name="chat-screens" />
          <Stack.Screen name="listing-screens" />
          <Stack.Screen name="network-screens" />
          <Stack.Screen name="profile-screens" />
          <Stack.Screen name="home-top-tabs" />
          <Stack.Screen name="property" />
        </Stack>
      </Providers>
    </TRPCProvider>
  );
}

function Providers({ children }: { children: React.ReactNode }) {
  const theme = useThemeConfig();
  const insets = useSafeAreaInsets();
  return (
    <GestureHandlerRootView
      style={styles.container}
      className={theme.dark ? `dark` : undefined}
    >
      <KeyboardProvider enabled={true}>
        <ActionSheetProvider>
          <ThemeProvider value={theme}>
            <BottomSheetModalProvider>
              {children}
              <FlashMessage
                position="top"
                style={
                  Platform.OS === 'android' ? { paddingTop: insets.top } : ''
                }
              />
              {/* <SystemBars /> */}
            </BottomSheetModalProvider>
          </ThemeProvider>
        </ActionSheetProvider>
      </KeyboardProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // marginBottom: Platform.OS === 'android' ? 20 : 0,
    // paddingTop: Platform.OS === 'android' ? 20 : 0,
  },
});
