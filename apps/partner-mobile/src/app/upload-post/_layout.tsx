import Entypo from '@expo/vector-icons/Entypo';
import { Stack, useLocalSearchParams, useNavigation } from 'expo-router';
import React from 'react';
import { Pressable } from 'react-native';

export default function Layout() {
  const navigation = useNavigation();
  const searchParams = useLocalSearchParams<{ propertyId: string }>();

  return (
    <Stack>
      <Stack.Screen
        name="post-property"
        options={{
          title: 'Post Property',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#F9F7F7' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
    </Stack>
  );
}
