import React, { useEffect, useState } from 'react';
import { View } from 'react-native';

import Stepper from '@/components/stepper';

import Step1 from '../../components/post-property/step-1';
import Step2 from '../../components/post-property/step-2';
import Step3 from '../../components/post-property/step-3';
import Step4 from '../../components/post-property/step-4';
import { api } from '@/utils/api';
import { useLocalSearchParams } from 'expo-router';

const PostProperty = () => {
  const searchParams = useLocalSearchParams<{
    propertyId: string;
    step: string;
  }>();
  const [currentStep, setCurrentStep] = useState(1);
  const apiUtils = api.useUtils();

  useEffect(() => {
    try {
      const step = parseInt(searchParams.step);
      if (step > 0 && step < 5) {
        setCurrentStep(step);
      }
    } catch (e) {
      console.log('Failed to parse step');
    }
  }, [searchParams.step]);

  const handleNextStep = () => {
    apiUtils.postProperty.invalidate();
    setCurrentStep((prevStep) => prevStep + 1);
  };
  const handlePreviousStep = () => {
    apiUtils.postProperty.invalidate();
    setCurrentStep((currentStep) => currentStep - 1);
  };

  console.log('currentStep', currentStep);

  return (
    <View className="h-full w-full">
      <Stepper currentStep={currentStep} />
      {currentStep === 1 && <Step1 onNext={handleNextStep} />}
      {currentStep === 2 && (
        <Step2 onNext={handleNextStep} onPrevious={handlePreviousStep} />
      )}
      {currentStep === 3 && (
        <Step3 onNext={handleNextStep} onPrevious={handlePreviousStep} />
      )}
      {currentStep === 4 && <Step4 onPrevious={handlePreviousStep} />}
    </View>
  );
};

export default PostProperty;
