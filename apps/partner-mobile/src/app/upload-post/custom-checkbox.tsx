import Entypo from '@expo/vector-icons/Entypo';
import { Checkbox } from 'expo-checkbox';
import React, { useState } from 'react';
import { type Control, Controller } from 'react-hook-form';
import { Modal, Pressable, ScrollView, Text, View } from 'react-native';

import { Button } from '@/ui';

interface CheckBoxModalProps {
  visible: boolean;
  items: { label: string; value: string }[];
  selectedValues: string[];
  value?: { label: string; value: string }[];
  onValueChange: (values: string[]) => void;
  onClose: () => void;
}

export const CheckBoxModal: React.FC<CheckBoxModalProps> = ({
  visible,
  items,
  selectedValues,
  onValueChange,
  onClose,
  value,
}) => {
  const toggleValue = (value: string) => {
    if (selectedValues.includes(value)) {
      // Deselecting an individual value
      onValueChange(selectedValues.filter((v) => v !== value));
    } else {
      // Selecting an individual value
      onValueChange([...selectedValues, value]);
    }
  };

  const toggleSelectAll = () => {
    if (selectedValues.length === items.length) {
      // Deselect all if all are currently selected
      onValueChange([]);
    } else {
      // Select all
      onValueChange(items.map((item) => item.value));
    }
  };

  return (
    <Modal transparent={true} animationType="fade" visible={visible}>
      <View className="flex-1 justify-center bg-black/50">
        <View className="rounded-lg bg-white p-5">
          <Pressable onPress={toggleSelectAll} className="mb-4">
            <Text className="text-primary-800 font-airbnb_md text-base font-medium">
              {selectedValues.length === items.length
                ? 'Deselect All'
                : 'Select All'}
            </Text>
          </Pressable>
          <ScrollView>
            {items.map((item) => (
              <Pressable
                key={item.value}
                onPress={() => toggleValue(item.value)}
                className="mb-2 flex-row items-center"
              >
                <Checkbox
                  color="#5f2800"
                  value={selectedValues.includes(item.value)}
                  onValueChange={() => toggleValue(item.value)}
                />
                <Text className="ml-2">{item.label}</Text>
              </Pressable>
            ))}
          </ScrollView>
          <Button label="Done" onPress={onClose} />
        </View>
      </View>
    </Modal>
  );
};

interface Item {
  label: string;
  value: string;
}

interface CustomCheckBoxProps {
  items: Item[];
  control: Control<any>;
  name: string;
  label?: string;
  value?: Item[];
  onValueChange?: (values: Item[]) => void;
}

const CustomCheckBox: React.FC<CustomCheckBoxProps> = ({
  items,
  control,
  name,
  label,
  value,
  onValueChange,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const values = value?.map((item) => item.value);
  const selectedValues = items.filter((item) => values?.includes(item.value));

  console.log('values', value, values, selectedValues);

  return (
    <View className="mb-4 flex-1">
      {label && (
        <Text className="mb-2 font-airbnb_md text-base font-medium text-text-600">
          {label}
        </Text>
      )}
      <Pressable
        onPress={() => setModalVisible(true)}
        className="flex-row items-center justify-between rounded-xl border border-[#ECE9E8] bg-primary-50 px-5 py-3.5"
      >
        <Text className="font-airbnb_bk text-sm font-normal text-text-600">
          {selectedValues.length > 0
            ? selectedValues.map((item) => item.label).join(', ')
            : 'Select options'}
        </Text>
        <Entypo name="chevron-thin-down" size={12} color="#78523D" />
      </Pressable>
      <Controller
        control={control}
        render={({ field: { onChange } }) => (
          <CheckBoxModal
            visible={modalVisible}
            items={items}
            selectedValues={values ?? []}
            onValueChange={(values) => {
              console.log(values);
              const selectedValues = items.filter((item) =>
                values?.includes(item.value),
              );
              console.log(selectedValues);
              onValueChange && onValueChange(selectedValues);
              onChange(selectedValues);
            }}
            onClose={() => setModalVisible(false)}
          />
        )}
        name={name}
        defaultValue={[]}
      />
    </View>
  );
};

export default CustomCheckBox;
