import React from 'react';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import TextInputHeader from '@/components/text-input-header';
import { FocusAwareStatusBar } from '@/ui';

import NetworkScreens from '../network-screens';

import { router } from 'expo-router';

export default function Network() {
  const insets = useSafeAreaInsets();

  return (
    <View style={{ paddingTop: insets.top + 12 }} className="bg-[#fff]">
      <FocusAwareStatusBar />
      <View className="h-full">
        <TextInputHeader
          placeholderText="Search your agent"
          onChangeText={(search) => {
            router.replace({ pathname: '/network', params: { query: search } });
          }}
        />
        <NetworkScreens />
      </View>
    </View>
  );
}
