import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, Animated, StyleSheet, View } from 'react-native';
import { Image as NImage } from 'expo-image';
import { Redirect, SplashScreen, Tabs } from 'expo-router';
import { usePathname, useRouter } from 'expo-router';

import { useAuth, useIsFirstTime } from '@/core';
import { api } from '@/utils/api';

// eslint-disable-next-line max-lines-per-function
export default function TabLayout() {
  const { data: profile, isLoading } = api.user.profile.useQuery();
  const status = useAuth.use.status();
  const router = useRouter();
  const [isFirstTime] = useIsFirstTime();
  const pathname = usePathname();
  const activeTab = pathname.split('/').pop() || 'home';
  console.log('activeTab', activeTab);

  const hideSplash = useCallback(async () => {
    await SplashScreen.hideAsync();
  }, []);

  useEffect(() => {
    hideSplash();
  }, [hideSplash]);

  if (!isLoading && !profile?.onboardingStatus) {
    // return <Redirect href="/home-screens/step-1" />;
  }
  if (status === 'signOut') {
    return <Redirect href="/auth/sign-in" />;
  }

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#F04D24" />
      </View>
    );
  }

  return (
    <Tabs
      screenOptions={{
        tabBarStyle: styles.bottomTabStyles,
        tabBarActiveTintColor: '#F04D24',
        tabBarInactiveTintColor: '#525252',
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor:
              activeTab === 'Map' || activeTab === 'Feed'
                ? '#fff9f8'
                : 'transparent',
          },
          tabBarLabelStyle: {
            fontSize: 13.5,
            fontWeight:
              activeTab === 'Feed' || activeTab === 'Map' ? '500' : '400',
            color:
              activeTab === 'Feed' || activeTab === 'Map'
                ? '#F04D24'
                : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('../../../assets/icons/home.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
          tabBarTestID: 'feed-tab',
        }}
      />

      <Tabs.Screen
        name="network"
        options={{
          title: 'Network',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 14,
            marginVertical: 5,
            backgroundColor:
              activeTab === 'Request' || activeTab === 'Connections'
                ? '#fff9f8'
                : 'transparent',
          },
          tabBarLabelStyle: {
            fontSize: 13.5,
            fontWeight:
              activeTab === 'Request' || activeTab === 'Connections'
                ? '500'
                : '400',
            color:
              activeTab === 'Request' || activeTab === 'Connections'
                ? '#F04D24'
                : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('../../../assets/icons/network.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),

          tabBarTestID: 'style-tab',
        }}
      />

      <Tabs.Screen
        name="chats"
        options={{
          title: 'Chats',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor: activeTab === 'chats' ? '#fff9f8' : 'transparent',
          },
          tabBarLabelStyle: {
            fontSize: 13.5,
            fontWeight: activeTab === 'chats' ? '500' : '400',
            color: activeTab === 'chats' ? '#F04D24' : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('../../../assets/icons/chats.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="listings"
        options={{
          title: 'Listings',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor:
              activeTab === 'My%20Listings' ? '#fff9f8' : 'transparent',
          },

          tabBarLabelStyle: {
            fontSize: 13.5,
            fontWeight: activeTab === 'My%20Listings' ? '500' : '400',
            color: activeTab === 'My%20Listings' ? '#F04D24' : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('../../../assets/icons/listings.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor:
              activeTab === 'profile' ? '#fff9f8' : 'transparent',
          },
          tabBarLabelStyle: {
            fontSize: 13.5,
            fontWeight: activeTab === 'profile' ? '500' : '400',
            color: activeTab === 'profile' ? '#F04D24' : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('../../../assets/icons/profile.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
          tabBarTestID: 'settings-tab',
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  bottomTabStyles: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    backgroundColor: '#FFF',
    paddingHorizontal: 20,
    marginBottom: 10,
    // For iOS shadow
    shadowColor: 'rgba(39, 39, 39, 0.1)',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    // For Android shadow
    elevation: 10,
  },
});
