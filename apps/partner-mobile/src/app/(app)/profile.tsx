/* eslint-disable react/react-in-jsx-scope */
import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { Link, router } from 'expo-router';
import React, { ReactNode } from 'react';
import { Alert, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Feather from '@expo/vector-icons/Feather';
import AntDesign from '@expo/vector-icons/AntDesign';
import * as WebBrowser from 'expo-web-browser';

import { translate, useAuth } from '@/core';
import { FocusAwareStatusBar, Pressable, ScrollView, Text, View } from '@/ui';
import { api } from '@/utils/api';
import { getToken } from '@/utils/session-store';
import { getBaseUrl } from '@/utils/base-url';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import { Env } from '@env';

type PropsType = {
  border: boolean;
  icon?: string;
  iconSvg?: ReactNode;
  label: string;
  onPress: () => void;
};

const SettingOptions: React.FC<PropsType> = ({
  border,
  icon,
  iconSvg,
  label,
  onPress,
}) => {
  return (
    <Pressable
      className={`mb-2.5 flex-row items-center py-3 ${border === true ? 'border-b border-[#CECECE]' : null}`}
      onPress={onPress}
    >
      <View className="w-12">
        {iconSvg ? (
          <View className="items-center mr-4">{iconSvg}</View>
        ) : (
          icon && <NImage source={icon} className={'mr-5 h-6 w-6'} />
        )}
      </View>
      <View className="flex-1 flex-row items-center justify-between">
        <Text className="font-airbnb_md text-base font-medium text-text-500">
          {label}
        </Text>
        <NImage
          source={require('../../../assets/icons/arrow.png')}
          className="aspect-square w-4"
          tintColor={'#3A3A3A'}
        />
      </View>
    </Pressable>
  );
};

// eslint-disable-next-line max-lines-per-function

export default function Profile() {
  const { data: profile } = api.user.profile.useQuery();
  const signOut = useAuth.use.signOut();
  const insets = useSafeAreaInsets();

  const confirmSignOut = () => {
    Alert.alert('Confirm', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
        onPress: () => {},
      },
      {
        text: 'Yes',
        style: 'destructive',
        onPress: async () => {
          await signOut();
          router.replace('/');
        },
      },
    ]);
  };

  const confirmDeleteAccount = () => {
    Alert.alert(
      'Confirm',
      'Are you sure you want to request deletion of your account?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {},
        },
        {
          text: 'Yes',
          style: 'destructive',
          onPress: async () => {
            // TODO: delete account
            let result = await WebBrowser.openBrowserAsync(
              getBaseUrl() + '/delete-account',
            );
          },
        },
      ],
    );
  };

  return (
    <View className="flex-1 ">
      <FocusAwareStatusBar />
      {/* Header */}
      <Pressable
        className=""
        onPress={() => router.push('/profile-screens/edit-profile-new')}
      >
        <LinearGradient
          colors={['#F04D24', '#C33D12', '#962C00']}
          start={[0, 0]}
          end={[0.7, 0]}
          style={[
            styles.gradient,
            {
              // paddingTop: insets.top
            },
          ]}
        >
          <BlurView
            intensity={50}
            style={[{ height: 96 + insets.top, paddingTop: insets.top }]}
            tint="light"
          >
            <View className="flex-row items-center justify-between px-5 pt-2.5">
              <View className="flex-row items-center">
                <NImage
                  source={
                    profile?.cloudinaryProfileImageUrl
                      ? { uri: profile?.cloudinaryProfileImageUrl }
                      : profile?.filePublicUrl
                        ? { uri: profile?.filePublicUrl }
                        : require('../../../assets/icons/user-defaultimg.png')
                  }
                  className="mr-3 h-16 w-16 rounded-xl"
                />
                <View>
                  <Text className="mb-1 text-xl font-extrabold text-white">
                    {profile?.name}
                  </Text>
                  <Text className="text-sm font-bold text-white">
                    {profile?.company?.companyName}
                  </Text>
                </View>
              </View>
              <View className="self-center">
                <Text className="text-white font-bold">View Profile</Text>
                {/*<Pressable className="mt-3 flex-row items-center self-start rounded-lg bg-white px-2 py-1">*/}
                {/*  <NImage*/}
                {/*    source={require('../../../assets/images/coin.png')}*/}
                {/*    className="mr-2 h-5 w-5"*/}
                {/*  />*/}
                {/*  <Text className="font-airbnb_md text-sm font-medium text-secondary-main700 dark:text-secondary-main700">*/}
                {/*    Gold*/}
                {/*  </Text>*/}
                {/*</Pressable>*/}
              </View>
            </View>
          </BlurView>
        </LinearGradient>
      </Pressable>
      {/* subscription */}
      <ScrollView className="flex-1">
        {/*<Pressable className="px-5 pt-6">*/}
        {/*  <NImage*/}
        {/*    source={require('../../../assets/images/lol.png')}*/}
        {/*    className="h-[124px] w-full"*/}
        {/*    contentFit="contain"*/}
        {/*  />*/}
        {/*</Pressable>*/}

        <View className="flex-1 px-5 pt-4">
          {/* settings */}
          <Text className="mt-4 pb-3 font-airbnb_xbd text-lg font-extrabold text-text-600">
            {translate('settings.title')}
          </Text>

          <View>
            <SettingOptions
              iconSvg={
                <FontAwesome name="building-o" size={28} color="#525252" />
              }
              label={'About Company'}
              onPress={() => router.push('/profile-screens/about-company')}
              border={false}
            />

            <SettingOptions
              iconSvg={
                <FontAwesome name="line-chart" size={24} color="#525252" />
              }
              label={'Stats'}
              onPress={() => router.push('/profile-screens/stats')}
              border={false}
            />
            <SettingOptions
              iconSvg={
                <FontAwesome name="newspaper-o" size={24} color="#525252" />
              }
              label="News"
              onPress={() => router.push('/home-screens/news')}
              border={false}
            />
            <SettingOptions
              iconSvg={<FontAwesome name="eye" size={24} color="#525252" />}
              label={'Recently Viewed'}
              onPress={() => router.push('/profile-screens/recently-viewed')}
              border={false}
            />
            {/*<SettingOptions*/}
            {/*  icon={require('../../../assets/icons/profile/settings.png')}*/}
            {/*  label={'Settings'}*/}
            {/*  onPress={() => router.push('/profile-screens/settings')}*/}
            {/*  border={false}*/}
            {/*/>*/}

            {/* Report & Help */}
            {/*<Text className="mt-4 pb-3 font-airbnb_xbd text-lg font-extrabold text-text-600 dark:text-text-600">*/}
            {/*  Report & Help*/}
            {/*</Text>*/}

            {/*<View>*/}
            {/*  <SettingOptions*/}
            {/*    icon={require('../../../assets/icons/profile/report.png')}*/}
            {/*    label={'Report Fraud'}*/}
            {/*    onPress={function (): void {*/}
            {/*      throw new Error('Function not implemented.');*/}
            {/*    }}*/}
            {/*    border={false}*/}
            {/*  />*/}
            {/*  <SettingOptions*/}
            {/*    icon={require('../../../assets/icons/profile/help.png')}*/}
            {/*    label={'Help Center'}*/}
            {/*    onPress={function (): void {*/}
            {/*      throw new Error('Function not implemented.');*/}
            {/*    }}*/}
            {/*    border={true}*/}
            {/*  />*/}
            {/*</View>*/}

            {/* /!* Referrals & Feedback *!/ */}
            {/* <Text className="mt-4 pb-3 font-airbnb_xbd text-lg font-extrabold text-text-600 dark:text-text-600"> */}
            {/* Referrals & Feedback */}
            {/* </Text> */}

            {/* <View> */}
            <SettingOptions
              iconSvg={
                <FontAwesome5 name="user-friends" size={24} color="#525252" />
              }
              label={'Refer Your Friend'}
              onPress={() => router.push('/profile-screens/refer')}
              border={false}
            />
            {/* <SettingOptions
              iconSvg={<FontAwesome5 name="wallet" size={24} color="#525252" />}
              label={'My Wallet'}
              onPress={() => router.push('/profile-screens/my-wallet')}
              border={false}
            /> */}
            {/*  <SettingOptions*/}
            {/*    icon={require('../../../assets/icons/profile/feedback.png')}*/}
            {/*    label={'Give Us Feedback'}*/}
            {/*    onPress={() => router.push('/profile-screens/feedback')}*/}
            {/*    border={true}*/}
            {/*  />*/}
            {/*</View>*/}

            {/*/!* Terms & Conditions *!/*/}
            {/*<Text className="mt-4 pb-3 font-airbnb_xbd text-lg font-extrabold text-text-600 dark:text-text-600">*/}
            {/*  Terms & Conditions*/}
            {/*</Text>*/}

            {/*<View>*/}
            {/*  <SettingOptions*/}
            {/*    icon={require('../../../assets/icons/profile/privacy.png')}*/}
            {/*    label={'Privacy Policy'}*/}
            {/*    onPress={function (): void {*/}
            {/*      throw new Error('Function not implemented.');*/}
            {/*    }}*/}
            {/*    border={false}*/}
            {/*  />*/}
            {/*  <SettingOptions*/}
            {/*    icon={require('../../../assets/icons/profile/privacy.png')}*/}
            {/*    label={'Terms of Services'}*/}
            {/*    onPress={function (): void {*/}
            {/*      throw new Error('Function not implemented.');*/}
            {/*    }}*/}
            {/*    border={true}*/}
            {/*  />*/}
            {/*</View>*/}

            <SettingOptions
              iconSvg={
                <FontAwesome name="sign-out" size={24} color="#525252" />
              }
              label="Logout"
              onPress={confirmSignOut}
              border={false}
            />
            <SettingOptions
              iconSvg={
                <FontAwesome name="user-times" size={24} color="#525252" />
              }
              label="Request for Deletion of Account"
              onPress={confirmDeleteAccount}
              border={false}
            />
          </View>
        </View>
        <View className="gap-2 justify-center items-center">
          <Text className="text-xs text-text-400">version {Env.VERSION}</Text>
          <Text className="font-medium">
            {' '}
            Developed By{' '}
            <Link
              href="https://nextflytech.com"
              className="text-secondary-main700 underline font-medium"
            >
              Nextfly Technologies
            </Link>
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  gradient: {
    // flex: 1,
    // height: 100,
  },
  blurContainer: {
    flex: 1,
    justifyContent: 'center',
  },
});
