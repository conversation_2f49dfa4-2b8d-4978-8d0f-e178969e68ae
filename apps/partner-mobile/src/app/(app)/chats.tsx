import React, { useState } from 'react';
import { View } from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import TextInputHeader from '@/components/text-input-header';
import { FocusAwareStatusBar } from '@/ui';

import ChatPage from '../chat-screens/chats-page';

const Chats = () => {
  const insets = useSafeAreaInsets();
  const [inputText, setInputText] = useState('');

  const onChangeText = (text: string) => {
    setInputText(text);
    // console.log('Text Input Value: ', text);
  };
  return (
    <View className="bg-[#fff]" style={{ paddingTop: insets.top + 12 }}>
      <FocusAwareStatusBar />
      <View className="w-full">
        <TextInputHeader
          placeholderText={'Search'}
          // onChangeText={onChangeText}
        />
        <ChatPage />
      </View>
    </View>
  );
};

export default Chats;
