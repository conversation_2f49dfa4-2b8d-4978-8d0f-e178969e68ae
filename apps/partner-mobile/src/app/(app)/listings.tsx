import React from 'react';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Index from '@/app/listing-screens/index';
import TextInputHeader from '@/components/text-input-header';
import { FocusAwareStatusBar } from '@/ui';

const Listings = () => {
  const insets = useSafeAreaInsets();
  return (
    <View style={{ paddingTop: insets.top }} className="bg-[#f9f7f7]">
      <FocusAwareStatusBar />
      <View className="h-full">
        {/*<TextInputHeader placeholderText="Search your agent" />*/}
        <Index />
      </View>
    </View>
  );
};

export default Listings;
