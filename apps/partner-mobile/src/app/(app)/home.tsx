import { Image as NImage } from 'expo-image';
import { useRouter, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { FocusAwareStatusBar, Pressable } from '@/ui';
import HomeTopTabs from '../home-top-tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function Home() {
  const insets = useSafeAreaInsets();
  const [isError, setIsError] = useState(false);
  const router = useRouter();

  if (isError) {
    return (
      <SafeAreaView>
        <View>
          <Text>Error Loading Data</Text>
        </View>
      </SafeAreaView>
    );
  }

  function handlePress(id: number): void {
    console.log(`Item with id ${id} was pressed`);
  }

  return (
    <>
      <View
        style={{ flex: 1, paddingTop: insets.top + 12 }}
        className="bg-[#F4EFED]"
      >
        <FocusAwareStatusBar />
        <View className="flex-1">
          {/* Header Section */}
          <View className="px-5 flex-row items-center justify-between">
            <NImage
              source={require('../../../assets/images/deer-connect-logo.png')}
              style={{ width: 35.77, height: 46.64 }}
              contentFit="contain"
            />
            <View
              style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}
            >
              <Pressable
                onPress={() => router.push('../../home-screens/search')}
                className="p-2 bg-primary-700 rounded-lg"
              >
                <NImage
                  source={require('../../../assets/icons/search-icon.png')}
                  style={{ height: 20, width: 20 }}
                  contentFit="contain"
                />
              </Pressable>
              <Pressable
                onPress={() => router.push('../../home-screens/notification')}
                className="p-2 bg-primary-700 rounded-lg"
              >
                <NImage
                  source={require('../../../assets/icons/notification.png')}
                  style={{ height: 20, width: 20 }}
                  contentFit="contain"
                />
              </Pressable>
              <Pressable
                // onPress={() => router.push('../../home-screens/notification')}
                onPress={() => router.push('/upload-post/post-property')}
                className="p-2 bg-secondary-main700 rounded-lg"
              >
                <NImage
                  source={require('../../../assets/icons/pluss.png')}
                  style={{ height: 20, width: 20 }}
                  contentFit="contain"
                />
              </Pressable>
            </View>
          </View>

          <View style={{ flex: 1 }}>
            <HomeTopTabs />
          </View>
        </View>
      </View>
    </>
  );
}
