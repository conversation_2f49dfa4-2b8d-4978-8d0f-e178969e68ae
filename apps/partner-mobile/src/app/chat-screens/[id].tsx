import Entypo from '@expo/vector-icons/Entypo';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { Image as NImage } from 'expo-image';
import { useNavigation, useLocalSearchParams } from 'expo-router';
import React, { useRef, useState, useCallback } from 'react';
import {
  Pressable,
  Text,
  TextInput,
  View,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import z from 'zod';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { LeftMsg, RightMsg } from '@/ui/chat-screen-msgs';
import { FeedbackOptions } from '../profile-screens/feedback';
import { StarRating } from '../profile-screens/feedback';
import { TextInputField } from '../profile-screens/feedback';
import { BlurView } from 'expo-blur';
import { Button, Modal } from '@/ui';
import UserPic from '@/components/user-pic';
import { format as formatDate } from 'date-fns';
import { useRouter } from 'expo-router';
import {
  KeyboardAvoidingView,
  KeyboardAwareScrollView,
} from 'react-native-keyboard-controller';
import { Chat, AgentChat, CustomerChat, Message } from '@/types';
import { ScrollView } from 'react-native-gesture-handler';

const SCROLL_THRESHOLD = 100;
const RECONNECTION_ATTEMPTS = 10;
const RECONNECTION_DELAY = 2000; // 2 seconds

const rateAgentOrPropertySchema = z.object({
  agentRatingCount: z.number().min(1).max(5),
  agentRatingMessage: z.string().min(1),
  propertyRatingMessage: z.string(),
  propertyRatingCount: z.number().min(1).max(5).optional(),
});

export default function ChatDetailScreen() {
  const { id, chatType, name, profileImage } = useLocalSearchParams<{
    id: string;
    chatType: 'agent' | 'customer';
    name: string;
    profileImage: string;
  }>();

  const utils = api.useUtils();
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView | null>(null);
  const [newMessage, setNewMessage] = useState<string>('');
  const trpcUtils = api.useUtils();
  const reconnectionTimeoutRef = useRef<NodeJS.Timeout>();
  const { data: profile } = api.user.profile.useQuery();
  const blockUserMutation = api.chat.blockUser.useMutation();
  const unBlockUserMutation = api.chat.unBlockUser.useMutation();
  const deleteChatMutation = api.chat.deleteChat.useMutation();

  const isAgentChat = chatType === 'agent';

  // Get chat data based on type
  const { data: agentChat, isLoading: isLoadingAgent } =
    api.chat.getMessages.useQuery(
      { connectionId: id },
      { enabled: isAgentChat && !!id },
    );

  const { data: customerChat, isLoading: isLoadingCustomer } =
    api.chat.getCustomerMessages.useQuery(
      { connectionId: id },
      { enabled: !isAgentChat && !!id },
    );

  const chat = (isAgentChat ? agentChat : customerChat) as Chat | undefined;
  const isLoading = isAgentChat ? isLoadingAgent : isLoadingCustomer;

  // Only show 3dot menu and modal for agent chats
  const showMenu = isAgentChat && chat;

  const { mutate: sendMessage } = api.chat.sendMessage.useMutation({
    onSuccess: () => {
      setNewMessage('');
      void trpcUtils.chat.getMessages.invalidate();
    },
    onError: (error) => {
      showMessage({
        message: error.message || 'Error sending message',
        type: 'danger',
      });
    },
  });

  const { mutate: sendCustomerMessage } =
    api.chat.sendCustomerMessage.useMutation({
      onSuccess: () => {
        setNewMessage('');
        void trpcUtils.chat.getCustomerMessages.invalidate();
      },
      onError: (error) => {
        showMessage({
          message: error.message || 'Error sending message',
          type: 'danger',
        });
      },
    });

  const { mutate: toggleStatus } = api.chat.toggleStatus.useMutation();

  const handleRatingSelected = (rating: number) => {
    // Alert.alert(`You rated: ${rating} star(s)`); // Handle the rating as needed
  };

  const navigation = useNavigation();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const [isVisible, setIsVisible] = useState(false);
  const bottomSheetModalRef1 = useRef<BottomSheetModal>(null);
  const [isVisible1, setIsVisible1] = useState(false);

  const insets = useSafeAreaInsets();

  const handlePresentModal = () => {
    bottomSheetModalRef.current?.present();
    setIsVisible(true);
  };

  const handleDismissModal = () => {
    bottomSheetModalRef.current?.dismiss();
    setIsVisible(false);
  };

  const handlePresentModal1 = () => {
    bottomSheetModalRef1.current?.present();
    setIsVisible1(true);
    handleDismissModal();
  };

  const handleDismissModal1 = () => {
    bottomSheetModalRef1.current?.dismiss();
    setIsVisible1(false);
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !id || !profile?.id) {
      return showMessage({
        message: 'Please enter something.',
        type: 'danger',
      });
    }

    if (isAgentChat) {
      sendMessage({
        content: newMessage.trim(),
        senderId: profile.id,
        connectionId: id,
      });
    } else {
      sendCustomerMessage({
        content: newMessage.trim(),
        senderId: profile.id,
        connectionId: id,
      });
    }
  };

  const goToUserProfile = () => {
    handleDismissModal();
    router.push(`/network-screens/${chat?.user?.id}`);
  };

  const confirmDeleteChat = () => {
    Alert.alert('Confirmation', 'Are you sure you want to delete the chat.', [
      {
        text: 'Cancel',
        //  onPress: () => console.log('Cancel Pressed'),
        style: 'cancel',
      },
      {
        text: 'Yes Delete',
        style: 'destructive',
        onPress: () => {
          if (!chat) {
            return;
          }

          if (!chat.id) {
            return;
          }

          deleteChatMutation.mutate(
            { connectionId: chat.id },
            {
              onSuccess: () => {
                router.back();
                utils.invalidate();
                showMessage({
                  message: 'You have deleted the chat',
                  type: 'success',
                });
              },
            },
          );
        },
      },
    ]);
  };

  const confirmBlockUser = () => {
    Alert.prompt(
      'Confirmation',
      'Are you sure you want to block the user.',
      [
        {
          text: 'Cancel',
          // onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: 'Yes Block',
          style: 'destructive',
          onPress: (text) => {
            if (!chat) {
              return;
            }
            const userId = chat.user?.id;

            if (!chat.id) {
              return;
            }

            if (!text) {
              showMessage({
                message: 'Please enter block reason.',
                type: 'danger',
              });
              return false;
            }

            blockUserMutation.mutate(
              { connectionId: chat.id, blockReason: text },
              {
                onSuccess: () => {
                  handleDismissModal();
                  utils.invalidate();
                  showMessage({
                    message: 'You have blocked the user',
                    type: 'success',
                  });
                },
              },
            );
          },
        },
      ],
      'plain-text',
    );
  };

  const confirmUnBlockUser = () => {
    Alert.alert('Confirmation', 'Are you sure you want to unblock the user.', [
      {
        text: 'Cancel',
        // onPress: () => console.log('Cancel Pressed'),
        style: 'cancel',
      },
      {
        text: 'Yes Unblock',
        style: 'default',
        onPress: () => {
          if (!chat) {
            return;
          }

          if (!chat.id) {
            return;
          }

          unBlockUserMutation.mutate(
            { connectionId: chat.id },
            {
              onSuccess: () => {
                handleDismissModal();
                utils.invalidate();
                showMessage({
                  message: 'You have blocked the user',
                  type: 'success',
                });
              },
            },
          );
        },
      },
    ]);
  };

  const onChatRatingSuccess = () => {
    bottomSheetModalRef1.current?.dismiss();
    setIsVisible1(false);
    utils.invalidate();
  };

  if (!chat) {
    return (
      <View className="flex-1 w-full justify-center items-center">
        <ActivityIndicator size={40} color="#f04d24" />
      </View>
    );
  }

  const {
    id: chatId,
    status,
    messages,
    property,
    rating,
    blocked,
    createdAt,
  } = chat;
  const { user, customer } = chat;

  const isCustomerChat = chatType === 'customer';
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={-insets.bottom}
    >
      {isVisible || isVisible1 ? (
        <View className="absolute z-10 h-full w-full bg-black opacity-50" />
      ) : null}
      <View className="h-full w-full">
        {/* Header */}
        <View
          className="flex-row items-center justify-between bg-[#FFFAF9] px-5 py-6"
          style={{ paddingTop: insets.top + 12 }}
        >
          {/* Profile details and back button */}
          <View className="flex flex-row">
            <View className="flex-row items-center">
              <Pressable onPress={() => navigation.goBack()}>
                <Entypo
                  name="chevron-thin-left"
                  size={18}
                  color="#1E1E1E"
                  className="pr-3"
                />
              </Pressable>
              <UserPic
                picUrl={profileImage}
                size={34}
                color="#784100"
                className="aspect-square h-10 w-10 rounded-full"
              />
              <View className="px-3">
                <Text className="font-airbnb_bd text-base font-bold text-primary-700">
                  {name}
                </Text>
                {chat?.property?.propertyTitle && (
                  <Text className="font-airbnb_bk text-[10px] font-normal text-text-main700">
                    {chat?.property?.propertyTitle}
                  </Text>
                )}
              </View>
            </View>

            {/* type */}
            <View className="mb-5 rounded-lg bg-primary-0 px-2 py-1.5">
              <Text className="font-airbnb_md text-[8px] font-medium text-secondary-main700">
                {isAgentChat ? 'Agent' : 'Customer'}
              </Text>
            </View>
          </View>

          {/* header 3 dot menu - only show for agent chats */}
          {showMenu && (
            <Pressable onPress={handlePresentModal}>
              <NImage
                source={require('../../../assets/icons/dot2.png')}
                className="h-10 w-10"
              />
            </Pressable>
          )}
        </View>

        {/* Chat screen */}
        <ScrollView
          className="px-5"
          showsVerticalScrollIndicator={false}
          ref={scrollViewRef}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd()}
        >
          {messages.map((msg: Message) =>
            msg.senderId === profile?.id ? (
              <RightMsg
                message={msg.content}
                time={formatTime(msg.createdAt)}
                key={msg.id}
              />
            ) : (
              <LeftMsg
                message={msg.content}
                time={formatTime(msg.createdAt)}
                key={msg.id}
              />
            ),
          )}
        </ScrollView>

        {/* keyboard */}
        <View
          className="border-t border-[#FFFBF9]"
          style={{ paddingBottom: insets.bottom }}
        >
          <View className="my-4 w-full flex-row items-center justify-center">
            {chat?.blocked ? (
              <View className="mx-4 flex items-center justify-center rounded-md bg-primary-100 py-4 w-full">
                <Text>This conversation is blocked.</Text>
              </View>
            ) : (
              <View
                className={`flex-row items-center justify-between rounded-2xl border border-primary-0 bg-white px-4`}
              >
                <TextInput
                  className={`h-12 w-[90%] font-airbnb_bk text-sm font-normal text-primary-2-800`}
                  placeholder={'Type your message'}
                  placeholderTextColor={'#525252'}
                  value={newMessage}
                  onChangeText={(text) => {
                    setNewMessage(text);
                  }}
                />
                <Pressable onPress={handleSendMessage}>
                  <NImage
                    source={require('../../../assets/icons/send.png')}
                    className="w-5 h-5"
                    contentFit="contain"
                    tintColor={'#333333'}
                  />
                </Pressable>
              </View>
            )}
          </View>
        </View>

        {/* Bottom Sheet Modal - only show for agent chats */}
        {showMenu && (
          <Modal
            ref={bottomSheetModalRef}
            index={0}
            snapPoints={['50%']}
            onDismiss={handleDismissModal}
            backgroundStyle={{ backgroundColor: 'white' }}
            handleIndicatorStyle={{ backgroundColor: 'gray' }}
            enablePanDownToClose={true}
          >
            <View className="flex-1 px-5">
              <Pressable
                onPress={goToUserProfile}
                className="flex-row items-center justify-between py-5"
              >
                <View className="flex-row items-center">
                  <NImage
                    source={require('../../../assets/icons/profile.png')}
                    contentFit="contain"
                    className="mr-5 h-6 w-6"
                    tintColor={'#1E1E1E'}
                  />
                  <Text className="font-airbnb_md text-base font-medium text-text-600">
                    View Profile
                  </Text>
                </View>
                <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
              </Pressable>

              {chat?.rating?.length === 0 ? (
                <Pressable
                  className="flex-row items-center justify-between py-5"
                  onPress={() => handlePresentModal1()}
                >
                  <View className="flex-row items-center">
                    <NImage
                      source={require('../../../assets/icons/profile/feedback.png')}
                      contentFit="contain"
                      className="mr-5 h-6 w-6"
                      tintColor={'#1E1E1E'}
                    />
                    <Text className="font-airbnb_md text-base font-medium text-text-600">
                      Rate Agent
                    </Text>
                  </View>
                  <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
                </Pressable>
              ) : (
                <View className="flex-row items-center py-5">
                  <NImage
                    source={require('../../../assets/icons/profile/feedback.png')}
                    contentFit="contain"
                    className="mr-5 h-6 w-6"
                    tintColor={'#1E1E1E'}
                  />
                  <Text className="font-airbnb_md text-base font-medium text-text-600">
                    You rated agent {chat?.rating?.[0]?.userStarsCount ?? 0}{' '}
                    stars
                  </Text>
                </View>
              )}

              <Pressable
                onPress={confirmDeleteChat}
                className="flex-row items-center justify-between py-5"
              >
                <View className="flex-row items-center">
                  <NImage
                    source={require('../../../assets/icons/bin.png')}
                    contentFit="contain"
                    className="mr-5 h-6 w-6"
                    tintColor={'#1E1E1E'}
                  />
                  <Text className="font-airbnb_md text-base font-medium text-text-600">
                    Delete Chat
                  </Text>
                </View>
                <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
              </Pressable>

              {chat?.blocked ? (
                <Pressable
                  onPress={confirmUnBlockUser}
                  className="flex-row items-center justify-between py-5"
                >
                  <View className="flex-row items-center">
                    <NImage
                      source={require('../../../assets/icons/block.png')}
                      contentFit="contain"
                      className="mr-5 h-6 w-6"
                      tintColor={'#1E1E1E'}
                    />
                    <Text className="font-airbnb_md text-base font-medium text-text-600">
                      Unblock Chat
                    </Text>
                  </View>
                  <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
                </Pressable>
              ) : (
                <Pressable
                  onPress={confirmBlockUser}
                  className="flex-row items-center justify-between py-5"
                >
                  <View className="flex-row items-center">
                    <NImage
                      source={require('../../../assets/icons/block.png')}
                      contentFit="contain"
                      className="mr-5 h-6 w-6"
                      tintColor={'#1E1E1E'}
                    />
                    <Text className="font-airbnb_md text-base font-medium text-text-600">
                      Block
                    </Text>
                  </View>
                  <Entypo name="chevron-thin-right" size={18} color="#1E1E1E" />
                </Pressable>
              )}
            </View>
          </Modal>
        )}

        {showMenu && (
          <Modal
            ref={bottomSheetModalRef1}
            index={0}
            snapPoints={['50%', '70%']}
            onDismiss={handleDismissModal1}
            backgroundStyle={{ backgroundColor: 'white' }}
            handleIndicatorStyle={{ backgroundColor: 'gray' }}
            enablePanDownToClose={true}
          >
            <AgentRatingForm
              chatId={chatId}
              hasProperty={property !== null}
              onSuccess={onChatRatingSuccess}
            />
          </Modal>
        )}
      </View>
    </KeyboardAvoidingView>
  );
}

const AgentRatingForm = ({
  chatId,
  hasProperty,
  onSuccess,
}: {
  chatId: string;
  hasProperty: boolean;
  onSuccess?: () => void;
}) => {
  const rateChatMutation = api.chat.rateChat.useMutation();
  const form = useForm<z.infer<typeof rateAgentOrPropertySchema>>({
    resolver: zodResolver(rateAgentOrPropertySchema),
    defaultValues: {
      agentRatingCount: 0,
      agentRatingMessage: '',
      propertyRatingMessage: '',
      propertyRatingCount: undefined,
    },
  });

  const onSubmit = (data: z.infer<typeof rateAgentOrPropertySchema>) => {
    rateChatMutation.mutate(
      { ...data, connectionId: chatId },
      {
        onSuccess: () => {
          showMessage({
            message: 'Feedback done.',
            type: 'success',
          });
          onSuccess && onSuccess();
        },
        onError: () => {
          showMessage({
            message: 'Feedback failed.',
            type: 'danger',
          });
        },
      },
    );
  };

  const onError = (error: any) => {
    showMessage({
      message: 'Please check the form.',
      type: 'danger',
    });
  };

  return (
    <KeyboardAwareScrollView
      showsVerticalScrollIndicator={false}
      style={{ flex: 1 }}
    >
      <View className="flex-1 px-5">
        <View>
          <View className="mt-5 pb-8">
            <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
              Rate the agent
            </Text>
            <View className="flex-row items-center">
              <Controller
                control={form.control}
                name="agentRatingCount"
                render={({ field, fieldState }) => (
                  <StarRating
                    onRatingSelected={field.onChange}
                    // rating={field.value}
                  />
                )}
              />
            </View>
          </View>

          {/*<View className="pb-8">*/}
          {/*  <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">*/}
          {/*    What you like about the agent?*/}
          {/*  </Text>*/}
          {/*  <FeedbackOptions />*/}
          {/*</View>*/}
          <Controller
            control={form.control}
            name="agentRatingMessage"
            render={({ field, fieldState }) => (
              <View className="pb-8">
                <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
                  Any comment or review regarding agent?
                </Text>
                <TextInput
                  placeholder=""
                  multiline={true}
                  className="rounded-xl border border-[#ECE9E8] px-5 py-3.5"
                  style={{ maxHeight: 62 }}
                  value={field.value}
                  onChangeText={field.onChange}
                />
              </View>
            )}
          />
          {hasProperty && (
            <>
              <View className="mt-5 pb-8">
                <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
                  Rate Property
                </Text>
                <View className="flex-row items-center">
                  <Controller
                    control={form.control}
                    name="propertyRatingCount"
                    render={({ field, fieldState }) => (
                      <StarRating
                        onRatingSelected={field.onChange}
                        // rating={field.value}
                      />
                    )}
                  />
                </View>
              </View>

              {/*<View className="pb-8">*/}
              {/*  <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">*/}
              {/*    What you like about the agent?*/}
              {/*  </Text>*/}
              {/*  <FeedbackOptions />*/}
              {/*</View>*/}
              <Controller
                control={form.control}
                name="propertyRatingMessage"
                render={({ field, fieldState }) => (
                  <View className="pb-8">
                    <Text className="mb-3 font-airbnb_md text-base font-medium text-text-600">
                      Any comment or review regarding property?
                    </Text>
                    <TextInput
                      placeholder=""
                      multiline={true}
                      className="rounded-xl border border-[#ECE9E8] px-5 py-3.5"
                      style={{ maxHeight: 62 }}
                      value={field.value}
                      onChangeText={field.onChange}
                    />
                  </View>
                )}
              />
            </>
          )}
        </View>

        {/* Bottom Button */}
        <View className="mb-6 flex-1">
          <Button
            label="Send Feedback"
            loading={rateChatMutation.isPending}
            onPress={form.handleSubmit(onSubmit, onError)}
          />
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
};

const formatTime = (date: Date | string) => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDate(dateObj, 'h:mm a');
  } catch (error) {
    return '';
  }
};
