import { FlashList } from '@shopify/flash-list';
import { Image as NImage } from 'expo-image';
import { router, useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Pressable,
  Text,
  View,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  Animated,
  StyleSheet,
} from 'react-native';
import ChatsEmpty from '@/components/chats-empty-page';
import { api } from '@/utils/api';
import { skipToken } from '@tanstack/react-query';
import UserPic from '@/components/user-pic';
import { format } from 'date-fns';
import { AgentCardConversation, CustomerCardConversation } from '@/types';

const TabsList = [
  { key: 'agent', name: 'Agent' },
  { key: 'customer', name: 'Customer' },
];

const ChatPage = () => {
  const router = useRouter();
  const [selected, setSelected] = useState<'agent' | 'customer'>('agent');

  // search conversation
  const { query } = useLocalSearchParams<{ query: string }>();
  const {
    data: searchedConversations,
    isLoading: isLoadingSearchedConversation,
    isError: isErrorSearchedConversations,
    refetch: refetchSearchedConversations,
  } = api.chat.searchConversation.useQuery(
    query && selected === 'agent' ? { queryString: query } : skipToken,
  );

  const {
    data: searchedCustomerConversations,
    isLoading: isLoadingSearchedCustomerConversation,
    isError: isErrorSearchedCustomerConversations,
    refetch: refetchSearchedCustomerConversations,
  } = api.chat.searchCustomerConversation.useQuery(
    query && selected === 'customer' ? { queryString: query } : skipToken,
  );

  const {
    data: conversations,
    isLoading,
    isError,
    refetch,
  } = api.chat.getConversations.useQuery();

  // logic to display data accordingly
  const dataToDisplay = query?.length
    ? selected === 'agent'
      ? (searchedConversations?.results ?? [])
      : (searchedCustomerConversations?.results ?? [])
    : selected === 'agent'
      ? (conversations?.agentsConversations ?? [])
      : (conversations?.customerConversations ?? []);

  const { data: profile } = api.user.profile.useQuery();

  const onRefresh = async () => {
    if (query) {
      if (selected === 'agent') {
        await refetchSearchedConversations();
      } else {
        await refetchSearchedCustomerConversations();
      }
    } else {
      await refetch();
    }
  };

  if (
    isLoading ||
    isLoadingSearchedConversation ||
    isLoadingSearchedCustomerConversation
  ) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size={40} color="#fff" />
      </View>
    );
  }

  if (
    isError ||
    isErrorSearchedConversations ||
    isErrorSearchedCustomerConversations
  ) {
    return (
      <View className="items-center justify-center">
        <Text>Error loading conversations</Text>
      </View>
    );
  }

  return (
    <ScrollView
      className="h-full"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={
            isLoadingSearchedConversation ||
            isLoadingSearchedCustomerConversation
          }
          onRefresh={onRefresh}
        />
      }
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      {/* switchable button */}
      <View className="mx-5 py-3 border-b border-secondary-100">
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 0 }}
          className="w-full "
        >
          <View className=" px-5 flex-row gap-5 items-center justify-between">
            {TabsList.map((item) => (
              <Pressable
                key={item.key}
                onPress={() => setSelected(item.key as 'agent' | 'customer')}
                className={`px-5 py-3 rounded-lg justify-center items-center ${
                  selected === item.key
                    ? 'bg-secondary-main700'
                    : 'bg-secondary-100 '
                }`}
              >
                <Text
                  className={`font-normal text-sm font-airbnb_md ${
                    selected === item.key
                      ? 'text-white'
                      : 'text-secondary-main700'
                  }`}
                >
                  {item.name}
                </Text>
              </Pressable>
            ))}
          </View>
        </ScrollView>
      </View>

      <View className="flex-1">
        {dataToDisplay?.length === 0 ? (
          <ChatsEmpty />
        ) : (
          <FlashList
            showsVerticalScrollIndicator={false}
            data={dataToDisplay}
            keyExtractor={(item) => item.id.toString()}
            estimatedItemSize={200}
            className="mt-3"
            renderItem={({ item, index }) => {
              let user;
              let lastMessage;
              let noOfMessages;
              let isAgent = selected === 'agent';
              const agentItem = item;
              user =
                isAgent && agentItem.senderId === profile?.id
                  ? agentItem.receiver
                  : isAgent && agentItem.receiverId === profile?.id
                    ? agentItem.sender
                    : //@ts-expect-error use is defined
                      agentItem.customer;

              noOfMessages = agentItem.messages.length;
              lastMessage = agentItem.messages[0];

              return (
                <Pressable
                  className={`mx-5 border-b border-[#DED3CD]`}
                  onPress={() =>
                    router.push({
                      pathname: '/chat-screens/[id]',
                      params: {
                        id: item.id,
                        chatType: selected,
                        name: user?.name,
                        profileImage: isAgent
                          ? user?.cloudinaryProfileImageUrl ||
                            user?.filePublicUrl
                          : user?.cloudinaryImagePublicUrl ||
                            user?.profileImagePublicUrl,
                      },
                    })
                  }
                >
                  <View
                    className={`my-2 rounded-lg ${
                      index % 2 === 0 ? 'bg-primary-50' : 'bg-[#FAFAFA]'
                    } p-3`}
                  >
                    <View className="flex-row">
                      <UserPic
                        picUrl={
                          isAgent
                            ? (user as AgentCardConversation['agent'])
                                ?.cloudinaryProfileImageUrl ||
                              (user as AgentCardConversation['agent'])
                                ?.filePublicUrl
                            : (user as CustomerCardConversation['customer'])
                                ?.cloudinaryImagePublicUrl ||
                              (user as CustomerCardConversation['customer'])
                                ?.profileImagePublicUrl
                        }
                        color="#F04D24"
                        size={54}
                        className="mr-2.5 aspect-square h-16 rounded-full"
                      />
                      <View className="flex-1">
                        <View className="flex-row items-center justify-between">
                          <View>
                            <Text className="font-airbnb_bd text-base font-bold text-primary-700">
                              {user?.name ?? 'N/A'}
                            </Text>

                            {item.property?.propertyTitle && (
                              <Text className="mt-2 font-airbnb_bk text-[10px] font-normal text-text-main700">
                                {item.property?.propertyTitle}
                              </Text>
                            )}

                            {item.messages[0]?.content && (
                              <Text className="mt-2 font-airbnb_bk text-xs font-normal text-text-500">
                                {item.messages[0]?.content}
                              </Text>
                            )}
                          </View>

                          <View>
                            <View className="flex-row justify-between items-center">
                              <View className="rounded-lg bg-[#F4F0EE] p-2.5">
                                <Text className="font-airbnb_md text-xs font-medium text-secondary-main700">
                                  {isAgent ? 'Agent' : 'Customer'}
                                </Text>
                              </View>
                              <View>
                                <Text className="ml-2 mt-1 font-airbnb_md text-xs font-medium text-text-500">
                                  {format(
                                    new Date(item.createdAt),
                                    'dd/MM/yyyy',
                                  )}
                                </Text>
                                <Text className="ml-2 mt-1 font-airbnb_md text-xs font-medium text-text-500 text-right">
                                  {format(new Date(item.createdAt), 'h:mm a')}
                                </Text>
                              </View>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </Pressable>
              );
            }}
          />
        )}
      </View>
    </ScrollView>
  );
};

export default ChatPage;

const styles = StyleSheet.create({
  slider: {
    position: 'absolute',
    height: 36,
    backgroundColor: '#fef9ea',
    borderRadius: 8,
    zIndex: 1,
  },
  gradientBox: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    marginRight: 12,
    height: 124,
    // width: '44%',
    width: '46%',
    // width: 'auto',
    // flex: 1,
  },
});
