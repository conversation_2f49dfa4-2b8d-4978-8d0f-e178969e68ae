import { create } from 'zustand';
import { z } from 'zod';
import {
  PropertyForEnum,
  FurnishingEnum,
  PropertyStateEnum,
  PossessionStateEnum,
  FacingEnum,
  CustomerPropertiesFilterSchema,
} from '@/utils/form-validators';

type IFilterForm = z.infer<typeof CustomerPropertiesFilterSchema>;

interface PropertyFiltersActions {
  setFilter: <K extends keyof IFilterForm>(
    key: K,
    value: IFilterForm[K],
  ) => void;
  resetFilters: () => void;
  setMultipleFilters: (filters: Partial<IFilterForm>) => void;
}

const initialState: IFilterForm = {
  areaUnitId: '', // Required field needs a default value
  propertyFor: 'SALE',
  take: '10',
};

export const usePropertyFiltersStore = create<
  IFilterForm & PropertyFiltersActions
>((set) => ({
  ...initialState,

  setFilter: (key, value) =>
    set((state) => ({
      ...state,
      [key]: value,
    })),

  resetFilters: () => set(initialState),

  setMultipleFilters: (filters) =>
    set((state) => ({
      ...state,
      ...filters,
    })),
}));

// Selector hooks for better performance
export const usePropertyFilter = <K extends keyof IFilterForm>(
  key: K,
): IFilterForm[K] => usePropertyFiltersStore((state) => state[key]);
