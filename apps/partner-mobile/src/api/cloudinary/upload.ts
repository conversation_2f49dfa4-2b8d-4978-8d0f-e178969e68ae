import { CloudinaryUploadResponse } from './types';

/**
 * Performs a signed upload to Cloudinary using server-generated signature
 *
 * @param imageUri - The local URI of the image file to upload
 * @param folder - The Cloudinary folder to upload to (optional)
 * @param tags - Tags to associate with the image (optional)
 * @returns Promise with the Cloudinary upload response
 */
export const signedUploadToCloudinary = async ({
  imageUri,
  folder,
  signature,
  timestamp,
  preset,
  apiKey,
  cloudName,
}: {
  imageUri: string;
  folder: string;
  signature: string;
  timestamp: string;
  preset: string;
  apiKey: string;
  cloudName: string;
}): Promise<CloudinaryUploadResponse> => {
  try {
    console.log('[DEBUG] signedUploadToCloudinary called with:', {
      imageUri: imageUri.substring(0, 20),
      folder: folder.substring(0, 20),
      signature: signature.substring(0, 20),
      timestamp: timestamp.substring(0, 20),
    });

    const localUri = imageUri;
    // Assuming you are passing the same timestamp used for signature generation
    // This needs to be the same value sent to the backend endpoint.

    // Create form data for upload
    const formData = new FormData();
    formData.append('file', {
      uri: localUri,
      type: 'image/jpeg',
      name: `upload-${Date.now()}.jpg`,
    } as any);
    console.log('append timestamp');

    formData.append('timestamp', timestamp);
    console.log('append folder');
    formData.append('folder', folder);
    console.log('append upload_preset');
    formData.append('upload_preset', preset);
    console.log('append api_key');
    formData.append('api_key', apiKey);
    console.log('append signature');
    formData.append('signature', signature);
    console.log('formData', formData);

    // Send directly to Cloudinary API
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName || 'default'}/image/upload`;
    console.log(
      '[DEBUG] Uploading to Cloudinary with signed request:',
      uploadUrl
    );

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
      headers: {
        // Removed the 'Content-Type': 'multipart/form-data' header as fetch handles it
      },
    });
    console.log('response', JSON.stringify(response, null, 2));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[DEBUG] Error response from Cloudinary:', errorText);
      // Added more detailed error logging
      try {
        const errorJson = JSON.parse(errorText);
        console.error('[DEBUG] Cloudinary error details:', errorJson);
      } catch (parseError) {
        console.error('[DEBUG] Could not parse error response as JSON.');
      }
      throw new Error(`Failed to upload image: ${response.status}`);
    }

    // Parse response
    let result;
    try {
      const responseText = await response.text();
      console.log(
        '[DEBUG] Response text preview:',
        responseText.substring(0, 200)
      );
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('[DEBUG] Error parsing JSON response:', parseError);
      throw new Error('Invalid response format from Cloudinary');
    }

    console.log(
      '[DEBUG] Upload successful, result:',
      JSON.stringify(result).substring(0, 200)
    );
    return result;
  } catch (error) {
    console.error('Error in signed upload to Cloudinary:', error);
    throw error;
  }
};

/**
 * Performs a signed upload of video content to Cloudinary using server-generated signature
 *
 * @param videoUri - The local URI of the video file to upload
 * @param folder - The Cloudinary folder to upload to (optional)
 * @param tags - Tags to associate with the video (optional)
 * @returns Promise with the Cloudinary upload response
 */
export const signedVideoUploadToCloudinary = async ({
  videoUri,
  folder,
  signature,
  timestamp,
  preset,
  apiKey,
  cloudName,
}: {
  videoUri: string;
  folder: string;
  signature: string;
  timestamp: string;
  preset: string;
  apiKey: string;
  cloudName: string;
}): Promise<CloudinaryUploadResponse> => {
  try {
    console.log('[DEBUG] signedVideoUploadToCloudinary called with:', {
      videoUri: videoUri.substring(0, 20),
      folder: folder.substring(0, 20),
      signature: signature.substring(0, 20),
      timestamp: timestamp.substring(0, 20),
    });

    const localUri = videoUri;

    // Create form data for upload
    const formData = new FormData();
    formData.append('file', {
      uri: localUri,
      type: 'video/mp4',
      name: `upload-${Date.now()}.mp4`,
    } as any);
    console.log('append timestamp');

    formData.append('timestamp', timestamp);
    console.log('append folder');
    formData.append('folder', folder);
    console.log('append upload_preset');
    formData.append('upload_preset', preset);
    console.log('append api_key');
    formData.append('api_key', apiKey);
    console.log('append signature');
    formData.append('signature', signature);
    console.log('formData', formData);

    // Send directly to Cloudinary API
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName || 'default'}/video/upload`;
    console.log(
      '[DEBUG] Uploading to Cloudinary with signed request:',
      uploadUrl
    );

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
      headers: {
        // Removed the 'Content-Type': 'multipart/form-data' header as fetch handles it
      },
    });
    console.log('response', JSON.stringify(response, null, 2));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[DEBUG] Error response from Cloudinary:', errorText);
      // Added more detailed error logging
      try {
        const errorJson = JSON.parse(errorText);
        console.error('[DEBUG] Cloudinary error details:', errorJson);
      } catch (parseError) {
        console.error('[DEBUG] Could not parse error response as JSON.');
      }
      throw new Error(`Failed to upload video: ${response.status}`);
    }

    // Parse response
    let result;
    try {
      const responseText = await response.text();
      console.log(
        '[DEBUG] Response text preview:',
        responseText.substring(0, 200)
      );
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('[DEBUG] Error parsing JSON response:', parseError);
      throw new Error('Invalid response format from Cloudinary');
    }

    console.log(
      '[DEBUG] Upload successful, result:',
      JSON.stringify(result).substring(0, 200)
    );
    return result;
  } catch (error) {
    console.error('Error in signed video upload to Cloudinary:', error);
    throw error;
  }
};
