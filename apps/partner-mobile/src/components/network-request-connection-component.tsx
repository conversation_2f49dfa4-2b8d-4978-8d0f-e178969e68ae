import { Image as NImage } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { Button } from '@/components/network-button-rating-experience-component';
import UserPic from '@/components/user-pic';
import { api } from '@/utils/api';
import { ConnectionRequest } from '@/types';

type PropsType = {
  item: ConnectionRequest;
  onConnectPress?: () => void;
  onDeclinePress?: () => void;
  onChatPress?: () => void;
  onViewProfilePress?: () => void;
  onCardPress?: () => void;
  onCancelPress?: () => void;
};

// eslint-disable-next-line max-lines-per-function
const NetworkRequestConnectionComponent: React.FC<PropsType> = ({
  item,
  onConnectPress,
  onDeclinePress,
  onChatPress,
  onViewProfilePress,
  onCancelPress,
}) => {
  const { data: profile } = api.user.profile.useQuery();

  const user = profile?.id === item.sender.id ? item.receiver : item.sender;

  return (
    <Pressable
      onPress={() => {
        router.push({
          pathname: '/network-screens/[id]',
          params: { id: user.id },
        });
      }}
      className="mb-2.5 mt-4 rounded-[10px] border border-[#E9E2DD] bg-primary-50 px-2.5 py-3"
    >
      <View className="border-b border-[#E9E2DD]">
        {/* top part */}
        <View className="mb-2 flex-row items-center justify-between">
          {/* image and name part */}
          <View className="flex-row items-center gap-2">
            <UserPic
              picUrl={user.cloudinaryProfileImageUrl ?? user.filePublicUrl}
              size={42}
              color="#784100"
              className="h-12 w-12 rounded-full"
            />
            {/*<NImage*/}
            {/*  source={*/}
            {/*    item.sender.filePublicUrl*/}
            {/*      ? { uri: item.sender.filePublicUrl }*/}
            {/*      : require('../../assets/images/profile7.png')*/}
            {/*  }*/}
            {/*  className="h-12 w-12"*/}
            {/*/>*/}
            <View className="ml-1.5">
              <Text className="font-airbnb_xbd text-lg font-extrabold text-primary-750">
                {user?.name}
              </Text>
              <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                {user.companyDetails?.companyName ?? ' '}
              </Text>
            </View>
          </View>

          {/* rating, or experience and active part */}
          <View>
            <View className="mb-2 flex-row items-center self-end">
              <NImage
                source={require('../../assets/icons/star.png')}
                className="h-4 w-4"
              />
              <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-primary-750">
                {user.rating || 0}
              </Text>
            </View>

            <View className="flex-row items-center self-start rounded-md bg-primary-100 px-2.5 py-0.5">
              <Text className="font-airbnb_bk text-[10px] font-normal text-primary-750">
                Active Properties:
              </Text>
              <Text className="font-airbnb_bd text-[10px] font-bold text-primary-750">
                {' '}
                {user.properties.length}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* experience and work record */}
      <View className="flex-row items-center justify-between py-2.5">
        <View className="flex-row items-center">
          <NImage
            source={require('../../assets/icons/sold.png')}
            className="mr-2 h-5 w-5"
            tintColor={'#784100'}
          />
          <Text className="font-airbnb_bk text-[11px] font-normal text-text-main700">
            {user.propertiesSold || 0} Properties Sold
          </Text>
        </View>

        <View className="flex-row items-center">
          <NImage
            source={require('../../assets/icons/experience.png')}
            className="mr-2 h-5 w-5"
            tintColor={'#784100'}
          />
          <Text className="font-airbnb_bk text-[11px] font-normal text-text-main700">
            {user.experience || 0}yrs Experience
          </Text>
        </View>
      </View>

      {/* Buttons */}
      {item.status === 'PENDING' && (
        <View className="flex-row items-center justify-between">
          {onConnectPress && (
            <Button
              label="Connect"
              backgroundColor={'bg-secondary-main700'}
              textColor={'text-white'}
              onPress={onConnectPress}
            />
          )}
          {onDeclinePress && (
            <Button
              label="Decline"
              backgroundColor={'bg-secondary-100'}
              textColor={'text-secondary-main700'}
              onPress={onDeclinePress}
            />
          )}
          {onChatPress && (
            <Button
              label="Chat"
              backgroundColor={'bg-secondary-main700'}
              textColor={'text-white'}
              onPress={onChatPress}
            />
          )}
        </View>
      )}
      {/* Show Cancel and View Profile for sent requests */}
      {item.status === 'PENDING' && (onCancelPress || onViewProfilePress) && (
        <View className="flex-row items-center justify-between">
          {onCancelPress && (
            <Button
              label="Cancel Request"
              backgroundColor={'bg-secondary-main700'}
              textColor={'text-white'}
              onPress={onCancelPress}
            />
          )}
          {onViewProfilePress && (
            <Button
              label="View Profile"
              backgroundColor={'bg-secondary-100'}
              textColor={'text-secondary-main700'}
              onPress={onViewProfilePress}
            />
          )}
        </View>
      )}
      {/* Show chat and view profile for accepted requests */}
      {item.status === 'ACCEPTED' && (onChatPress || onViewProfilePress) && (
        <View className="flex-row items-center justify-between">
          {onChatPress && (
            <Button
              label="Chat"
              backgroundColor={'bg-secondary-main700'}
              textColor={'text-white'}
              onPress={onChatPress}
            />
          )}
          {onViewProfilePress && (
            <Button
              label="View Profile"
              backgroundColor={'bg-secondary-100'}
              textColor={'text-secondary-main700'}
              onPress={onViewProfilePress}
            />
          )}
        </View>
      )}
    </Pressable>
  );
};

export default NetworkRequestConnectionComponent;
