import React from 'react';
import { Pressable, View } from 'react-native';
import { Image } from 'expo-image';
import { useActionSheet } from '@expo/react-native-action-sheet';

interface PostOptionsMenuProps {
  postId: string;
  isOwnPost: boolean;
  onReportPress: () => void;
  onDeletePress?: () => void;
}

const PostOptionsMenu: React.FC<PostOptionsMenuProps> = ({
  postId,
  isOwnPost,
  onReportPress,
  onDeletePress,
}) => {
  const { showActionSheetWithOptions } = useActionSheet();

  const handleOptionsPress = () => {
    const options = [];
    const destructiveButtonIndex = [];
    let cancelButtonIndex = 0;

    if (isOwnPost) {
      // Options for user's own post
      if (onDeletePress) {
        options.push('Delete Post');
        destructiveButtonIndex.push(0);
        cancelButtonIndex = 1;
      }
      options.push('Cancel');
    } else {
      // Options for other users' posts
      options.push('Report Post');
      options.push('Cancel');
      cancelButtonIndex = 1;
    }

    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
        destructiveButtonIndex,
        title: 'Post Options',
      },
      (selectedIndex?: number) => {
        if (selectedIndex === undefined) return;

        if (isOwnPost) {
          switch (selectedIndex) {
            case 0:
              if (onDeletePress) {
                onDeletePress();
              }
              break;
            default:
              break;
          }
        } else {
          switch (selectedIndex) {
            case 0:
              onReportPress();
              break;
            default:
              break;
          }
        }
      },
    );
  };

  return (
    <Pressable
      onPress={handleOptionsPress}
      className="p-2"
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
    >
      <Image
        source={require('@assets/icons/horizontaldot.png')}
        className="w-5 h-5"
        contentFit="contain"
      />
    </Pressable>
  );
};

export default PostOptionsMenu;
