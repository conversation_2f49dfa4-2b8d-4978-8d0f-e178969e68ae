import { View, Text, TextInput, TextInputBase } from 'react-native';
import React, { useState } from 'react';
import { api } from '@/utils/api';
import AgentInfoCard from '../agent-info-card';
import { CustomModal } from '../custom-modal';
import { showMessage } from 'react-native-flash-message';
import { Button, Input, Modal, useModal } from '@/ui';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

const Bio = ({ bio }: { bio: string }) => {
  const utils = api.useUtils();
  const [bioText, setBioText] = useState<string>(bio || "");
  const { ref, present, dismiss } = useModal();


  // bio update mutation
  const { mutate: bioUpdate } = api.user.bioUpdate.useMutation();
  const handleBioUpdate = (bioText: string) => {
    bioUpdate(
      { bio: bioText },
      {
        onSuccess: () => {
          showMessage({
            message: 'Bio updated successfully',
            type: 'success',
          });
          dismiss();
          utils.user.profile.invalidate();
        },
        onError: (error) => {
          showMessage({
            message: error.message,
            type: 'danger',
          });
        },
      },
    );
  };

  return (
    <View>
      {bio && bio.length > 0 ? (
        <View className="mt-5">
          <AgentInfoCard<false>
            heading={'Bio'}
            description={bio ?? 'N/A'}
            onEdit={() => present()}
          />
        </View>
      ) : (
        <View className="mt-5">
          <AgentInfoCard<false>
            heading={'Bio'}
            description={'No bio available'}
            onAdd={() => present()}
          />
        </View>
      )}

      {/* bio modal */}
      <Modal
        ref={ref}
        onDismiss={dismiss}
      >
          <KeyboardAvoidingView className='px-5'>
            
            <Text className="mb-4 self-center text-lg font-airbnb_bd font-bold text-primary-750">
              Edit Bio
            </Text>

              <Text className="mb-2 font-airbnb_md text-base font-medium text-primary-800">
                Write Bio (optional)
              </Text>
              <TextInput
                defaultValue={bioText}
                onChangeText={setBioText}
                multiline
                maxLength={100}
                className="min-h-[100px] max-h-[150px] p-4 border border-secondary-main700 rounded-xl bg-primary-50"
                style={{ textAlignVertical: 'top' }}
              />
              <Button
                label="Save"
                onPress={() => handleBioUpdate(bioText)}
                className="mt-4"
              />
            </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

export default Bio;
