import { Pressable, Text, View } from 'react-native';
import React, { useState, useEffect } from 'react';
import { Image } from 'expo-image';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { Button, Modal, useModal } from '@/ui';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

const Language = () => {
  const { ref, present, dismiss } = useModal();

  const { data: AllLanguages } = api.languages.getAllLanguages.useQuery();

  const { data: MyLanguages, refetch: refetchMyLanguages } =
    api.languages.getMyLanguage.useQuery();

  const [selectedLanguages, setSelectedLanguages] = useState<string[]>([]);

  // Initialize selectedLanguages with MyLanguages when modal opens
  useEffect(() => {
    if (MyLanguages) {
      setSelectedLanguages(MyLanguages.map((lang) => lang.id));
    }
  }, [MyLanguages]);

  const { mutate: mutateLanguage } =
    api.languages.addOrRemoveLanguage.useMutation({
      onSuccess: () => {
        showMessage({
          message: 'Language updated successfully',
          type: 'success',
        });
        dismiss();
        refetchMyLanguages();
      },
      onError: () => {
        showMessage({
          message: 'Error updating language',
          type: 'danger',
        });
      },
    });

  const toggleLanguageSelection = (langId: string) => {
    setSelectedLanguages((prev) => {
      if (prev.includes(langId)) {
        return prev.filter((id) => id !== langId);
      } else {
        return [...prev, langId];
      }
    });
  };

  const handleSaveLanguages = () => {
    // Get the current language IDs from MyLanguages
    const currentLanguageIds = MyLanguages
      ? MyLanguages.map((lang) => lang.id)
      : [];

    // Find languages to add (in selected but not in current)
    const languagesToAdd = selectedLanguages.filter(
      (id) => !currentLanguageIds.includes(id),
    );

    // Find languages to remove (in current but not in selected)
    const languagesToRemove = currentLanguageIds.filter(
      (id) => !selectedLanguages.includes(id),
    );

    // Add new languages
    languagesToAdd.forEach((langId) => {
      mutateLanguage({
        id: langId,
      });
    });

    // Remove languages that are no longer selected
    languagesToRemove.forEach((langId) => {
      mutateLanguage({
        id: langId,
      });
    });

    // Close modal after saving
    dismiss();
    setSelectedLanguages([]);
  };

  const clearAllSelections = () => {
    setSelectedLanguages([]);
  };

  return (
    <View>
      <View className="mt-5 p-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
        <View className="flex-row items-center justify-between">
          <Text className="mb-3 text-lg font-airbnb_bd font-bold text-primary-750">
            Language
          </Text>
          {MyLanguages && MyLanguages.length > 0 ? (
            <Pressable onPress={present} hitSlop={10}>
              <Image
                source={require('@assets/icons/edit5.png')}
                className="w-4 h-4"
              />
            </Pressable>
          ) : (
            <Pressable
              onPress={present}
              className="bg-secondary-main700 rounded-[10px] px-4 py-2"
            >
              <Text className="text-sm font-medium font-airbnb_md text-white">
                Add
              </Text>
            </Pressable>
          )}
        </View>

        {MyLanguages && MyLanguages.length > 0 ? (
          <View className="flex-row flex-wrap gap-2">
            {MyLanguages.map((language: { name: string; id: string }) => (
              <View
                key={language.id}
                className="mb-2 self-start bg-primary-100 rounded-md py-1 px-3 items-center"
              >
                <Text className="text-base font-medium font-airbnb_md text-primary-750">
                  {language.name}
                </Text>
              </View>
            ))}
          </View>
        ) : (
          <Text className="text-base font-normal font-airbnb_bk text-text-500">
            No languages added yet
          </Text>
        )}
      </View>

      <Modal ref={ref} onDismiss={dismiss}>
        <KeyboardAvoidingView className="px-5">
          <Text className="mb-4 self-center text-lg font-airbnb_bd font-bold text-primary-750">
            Edit Language
          </Text>

          <View className="mb-4">
            {AllLanguages?.map((lang) => (
              <Pressable
                key={lang.id}
                className={`mb-2 p-3 rounded-md ${
                  selectedLanguages.includes(lang.id)
                    ? 'bg-primary-200'
                    : 'bg-gray-100'
                }`}
                onPress={() => toggleLanguageSelection(lang.id)}
              >
                <Text
                  className={`text-base font-medium ${
                    selectedLanguages.includes(lang.id)
                      ? 'text-primary-750'
                      : 'text-text-500'
                  }`}
                >
                  {lang.name}
                </Text>
              </Pressable>
            ))}
          </View>

          <View className="flex-row justify-between gap-2">
            <Button
              onPress={clearAllSelections}
              label="Clear All"
              variant="outline"
              className="w-1/2"
            />
            <Button
              onPress={handleSaveLanguages}
              label="Save"
              className="w-1/2"
            />
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

export default Language;
