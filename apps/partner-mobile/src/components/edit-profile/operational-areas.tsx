import React, { useState } from 'react';
import { View, Text, Alert, Pressable, TextInput } from 'react-native';
import { Image as NImage } from 'expo-image';
import { api } from '@/utils/api';
import { Button, cn, Modal, useModal } from '@/ui';
import { useForm, Controller } from 'react-hook-form';
import TextInputGoogleAutocomplete from '../text-input-google-autocomplete';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { showMessage } from 'react-native-flash-message';
import AntDesign from '@expo/vector-icons/AntDesign';
import { ScrollView } from 'react-native-gesture-handler';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

const formSchema = z.object({
  operationArea: z.string().optional(),
});

const OperationalAreas = () => {
  const { ref, present, dismiss } = useModal();

  const { data: getOperationArea, refetch: refetchOperationAreas } =
    api.operationArea.getOperationArea.useQuery();
  const { data: profileData } = api.user.profile.useQuery();

  const onboardingOperationAreaMutation =
    api.operationArea.onboardingOperationArea.useMutation();

  const deleteOperationArea =
    api.operationArea.deleteOperationArea.useMutation();

  const { control, setValue, reset } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      operationArea: '',
    },
  });

  const onOperationalAreaAddressComponentsChange = (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => {
    onboardingOperationAreaMutation.mutate(
      {
        operationArea: data.address,
        operationAreaLatitude: data.lat,
        operationAreaGooglePlaceId: data.place_id,
        operationAreaLongitude: data.lng,
        operationAreaAddressComponent: data.address_components,
      },
      {
        onSuccess: () => {
          showMessage({
            message: 'Operational Area added successfully.',
            type: 'success',
          });
          reset({
            operationArea: '',
          });
          refetchOperationAreas();
        },
        onError: (err) => {
          showMessage({
            message: err?.message ?? 'Operational Area failed.',
            type: 'danger',
          });
        },
      },
    );
  };

  const openConfirmModalDeleteOperationalArea = (operationAreaId: string) => {
    Alert.alert(
      'Remove Operational Area',
      'Are you sure you want to remove this operational area?',
      [
        {
          text: 'Cancel',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => {
            deleteOperationArea.mutate(
              { operationAreaId },
              {
                onSuccess: () => {
                  refetchOperationAreas();
                  showMessage({
                    message: 'Operational area deleted successfully.',
                    type: 'success',
                  });
                },
                onError: (err) => {
                  showMessage({
                    message: err?.message ?? 'Operational Area failed.',
                    type: 'danger',
                  });
                },
              },
            );
          },
        },
      ],
    );
  };

  return (
    <View>
      <View className="mt-5 p-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
        <View className="flex-row items-center justify-between">
          <Text className="mb-3 text-lg font-airbnb_bd font-bold text-primary-750">
            Operational Areas
          </Text>
          {getOperationArea && getOperationArea.length > 0 ? (
            <Pressable onPress={present} hitSlop={10}>
              <NImage
                source={require('@assets/icons/edit5.png')}
                className="w-4 h-4"
              />
            </Pressable>
          ) : (
            <Pressable
              onPress={present}
              className="bg-secondary-main700 rounded-[10px] px-4 py-2"
            >
              <Text className="text-sm font-medium font-airbnb_md text-white">
                Add
              </Text>
            </Pressable>
          )}
        </View>

        {getOperationArea && getOperationArea.length > 0 ? (
          <>
            <Text
              className="mb-3 text-base font-medium font-airbnb_md text-text-500"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {profileData?.userLocation}
            </Text>

            <View className="flex-row flex-wrap gap-2">
              {getOperationArea.map(
                (area: { name: string; id: string }, index: number) => (
                  <View
                    key={index}
                    className="mb-2 self-start bg-primary-100 rounded-md py-1 px-3 items-center"
                  >
                    <Text className="text-base font-medium font-airbnb_md text-primary-750">
                      {area.name}
                    </Text>
                  </View>
                ),
              )}
            </View>
          </>
        ) : (
          <Text className="text-base font-normal font-airbnb_bk text-text-500">
            No operational areas added yet
          </Text>
        )}
      </View>

      <Modal ref={ref} onDismiss={dismiss} snapPoints={['80%']}>
        <KeyboardAvoidingView className="flex-1 px-5">
          <Text className="mb-4 self-center text-lg font-airbnb_bd font-bold text-primary-750">
            Edit Operational Areas
          </Text>

          <View className="mb-4 flex-1">
            <Controller
              control={control}
              name="operationArea"
              render={({ field, fieldState: { error } }) => {
                return (
                  <TextInputGoogleAutocomplete
                    onAddressComponentsChange={
                      onOperationalAreaAddressComponentsChange
                    }
                  >
                    <View className="mb-4">
                      <Text
                        className={cn(
                          'mb-2 font-airbnb_md text-base font-medium text-primary-800 ',
                          error && 'text-danger-600 ',
                        )}
                      >
                        Add Operational Area
                      </Text>
                      <View
                        className={cn(
                          'mt-0 flex-row items-center rounded-xl border border-secondary-main700 bg-primary-50',
                          error && 'border-danger-600',
                        )}
                      >
                        <Text className="flex-1 px-5 py-3.5 font-airbnb_bk text-sm font-normal leading-5 text-text-600">
                          {field.value || ' '}
                        </Text>
                      </View>
                      {error?.message && (
                        <Text className="text-sm text-danger-400">
                          {error?.message}
                        </Text>
                      )}
                    </View>
                  </TextInputGoogleAutocomplete>
                );
              }}
            />

            <Text className="mb-2 font-airbnb_md text-base font-medium text-primary-800">
              Your Operational Areas
            </Text>

            <View className="flex-1 overflow-hidden">
              <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  paddingHorizontal: 16,
                  paddingTop: 10,
                  paddingBottom: 24,
                }}
              >
                <View className="flex flex-row flex-wrap items-center gap-3">
                  {getOperationArea?.map((area) => (
                    <View
                      key={area.id}
                      className="flex flex-row items-center gap-2 rounded-[6px] bg-primary-100 px-3 py-2"
                    >
                      <Text
                        className="flex-1 font-airbnb_md text-sm text-primary-750"
                        numberOfLines={3}
                        ellipsizeMode="tail"
                      >
                        {area.name}
                      </Text>
                      <Pressable
                        onPress={() =>
                          openConfirmModalDeleteOperationalArea(area.id)
                        }
                      >
                        <AntDesign
                          name="closecircle"
                          size={16}
                          color="#252525"
                        />
                      </Pressable>
                    </View>
                  ))}
                </View>
              </ScrollView>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

export default OperationalAreas;
