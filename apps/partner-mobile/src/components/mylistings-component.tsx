/* eslint-disable max-lines-per-function */
import { Image as NImage, ImageBackground } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { Pressable, Text, View } from 'react-native';

import { type ListingItemProps } from '@/ui/listings-mylistings-data';

const MylistingsComponent: React.FC<ListingItemProps> = ({ item }) => {
  return (
    <Pressable className="mx-5 flex-1 border-b border-text-50">
      <View className="my-3 flex-row items-center">
        <ImageBackground
          source={require('../../assets/images/mylistbgimg.png')}
          contentFit="fill"
          style={{ aspectRatio: 3 / 4, width: '28.5%', padding: 6 }}
        >
          <View className="self-start rounded-md bg-primary-0 px-2 py-1.5">
            <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700">
              For Sale
            </Text>
          </View>
        </ImageBackground>

        <View className="w-[72%] pl-3 ">
          <View className="flex-row items-center justify-between">
            {item.features.map((label) => (
              <View key={label} className="mx-2.5">
                <Text className="font-airbnb_md text-[8px] font-medium text-primary-700">
                  {label}
                </Text>
              </View>
            ))}
          </View>

          <View className="pt-2">
            <Text className="font-airbnb_bd text-base font-bold text-text-main700">
              {item.locationname}
            </Text>
            <View className="mt-0.5 flex-row items-center">
              <NImage
                source={require('../../assets/icons/location2.png')}
                contentFit="contain"
                className="w-2.5 h-2.5"
              />
              <Text className="ml-1 font-airbnb_bk text-[10px] font-normal text-text-600">
                {item.location}
              </Text>
            </View>
            <Text className="mt-2 self-start font-airbnb_bk text-[10px] font-normal text-text-600">
              {item.locationdetails}
            </Text>
          </View>

          <View className="mt-2 flex-row items-center justify-around">
            <Pressable
              className="self-start rounded-lg border border-secondary-main700 px-9 py-2.5"
              onPress={() => router.push('/listing-screens/check-response')}
            >
              <Text className="text-secondary-main700">Check Responses</Text>
            </Pressable>

            <Pressable
              onPress={() => {
                const serializedItem = JSON.stringify(item); // Serialize the item
                router.push(
                  `/listing-screens/check-response?item=${encodeURIComponent(serializedItem)}`,
                );
              }}
            >
              <NImage
                source={require('../../assets/icons/edit.png')}
                className="h-6 w-6"
              />
            </Pressable>
            <Pressable>
              <NImage
                source={require('../../assets/icons/sharelistings.png')}
                className="h-6 w-6"
              />
            </Pressable>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default MylistingsComponent;
