/* eslint-disable max-lines-per-function */
import { Image as NImage, ImageBackground } from 'expo-image';
import React, { useEffect, useState } from 'react';
import { Pressable, Text, View, ActivityIndicator } from 'react-native';
import * as Sharing from 'expo-sharing';
import { Button } from '@/ui';
import {
  calcPerUnitPriceAccordingToAreaUnit,
  FormatAreaIn,
  formatPriceAddLabels,
} from '@/utils/format-terms';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { getBaseUrl } from '@/utils/base-url';
import PropertyWishlistBtn from '@/components/property/wishlist-btn';
import PropertyShareBtn from '@/components/property/share-btn';

type FavouritesComponentProps = {
  item: {
    id: string;
    registeryFileKey: string | null;
    propertyTitle: string | null;
    propertyFor: string | null;
    areaUnit: {
      id: string;
      name: string;
      createdAt: Date;
      updatedAt: Date;
      shortForm: string;
      conversionMultiplyer: number;
    } | null;
    area: number | null;
    aboutProperty: string | null;
    propertyAddress: string | null;
    propertyGooglePlaceId: string | null;
    propertyAddressComponents: any | null;
    propertyLocation: string | null;
    societyOrLocalityName: string | null;
    buildYear: number | null;
    possessionState: string | null;
    furnishing: string | null;
    totalFloors: number | null;
    floorNumber: number | null;
    carParking: number | null;
    facing: string | null;
    propertyState: string | null;
    createdAt: Date | null;
    updatedAt: Date | null;
    propertyTypeId: string | null;
    userId: string;
    propertyMarkersLatLng: { lat: string; lng: string }[] | null;
    propertyStatus: string | null;
    bedrooms: number | null;
    bathrooms: number | null;
    rating: string | null;
    review: string | null;
    popularProperty: boolean | null;
    soldAt: string | null;
    propertyPrice: string | null;
    securityDeposit: number | null;
    areaInSqMeters: number | null;
    totalViews: number | null;
    propertyLatitude: number | null;
    propertyLongitude: number | null;
    propertyCategoryId: string | null;
    utilities: {
      id: string;
      utility: string;
      distanceInKm: number;
    }[];
    amenities: {
      id: string;
      name: string;
      fileKey: string;
      filePublicUrl: string;
      cloudinaryUrl: string;
    }[];
    user: {
      company: {};
      companyDetails: {};
    };
    comments: {
      user: {};
    };
    mediaSections: {
      id: string;
      title: string;
      media: {
        id: string;
        fileKey: string;
        filePublicUrl: string;
        cloudinaryUrl: string;
      }[];
    }[];
  };
  handlePress: () => void;
};

const FavouritesComponent: React.FC<FavouritesComponentProps> = ({
  item,
  handlePress,
}: FavouritesComponentProps) => {
  const utils = api.useUtils();
  const url =
    item.mediaSections &&
    item.mediaSections.length &&
    (item.mediaSections[0]?.media[0]?.cloudinaryUrl ??
      item.mediaSections[0]?.media[0]?.filePublicUrl);

  const sendConnectionReqMutation =
    api.connection.sendConnectionRequest.useMutation();

  const { data: isLiked, isLoading } =
    api.likeProperty.isPropertyLiked.useQuery(
      { propertyId: item.id },
      {
        refetchOnWindowFocus: false,
      },
    );

  const likePropertyMutation = api.likeProperty.addLikedProperty.useMutation();

  const toggleLike = () => {
    // setIsLiked((prev) => !prev);
    // handlePress(item.id);
    likePropertyMutation.mutate(
      { propertyId: item.id },
      {
        onSuccess: (resp) => {
          utils.invalidate();
          showMessage({
            message: resp.message,
            type: 'success',
          });
        },
        onError: () => {
          showMessage({
            message: 'Error in removing property from favourites',
            type: 'danger',
          });
        },
      },
    );
  };

  const onAgentContactPress = () => {
    sendConnectionReqMutation.mutate(
      { receiverId: item.userId, propertyId: item.id },
      {
        onSuccess: (opts) => {
          console.log('success', opts);
          showMessage({
            message: 'Connection request sent successfully',
            type: 'success',
          });
        },
        onError: (err) => {
          console.log('error', err);
          showMessage({
            message: err?.message ?? 'Error in sending connection request',
            type: 'danger',
          });
        },
      },
    );
  };

  return (
    <Pressable onPress={() => handlePress && handlePress()}>
      <View className="mt-5 flex-1">
        <ImageBackground
          source={require('../../assets/images/favbg.png')}
          contentFit="fill"
          style={{ flex: 1 }}
        >
          <ImageBackground
            source={url ? { uri: url } : ''}
            contentFit="contain"
            style={{
              aspectRatio: 21 / 9,
              width: '100%',
              backgroundColor: url ? 'transparent' : '#898e8c',
            }}
          >
            <View className="flex-1 flex-col justify-between p-3">
              <View className="self-start rounded-md bg-[#F4FFFD] px-2 py-1.5">
                <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700">
                  {item.propertyFor ? 'For Sale' : 'Rent'}
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                {/*{item.features.map((label) => (*/}
                {/*  <View*/}
                {/*    key={label}*/}
                {/*    className="self-start rounded-[4px] bg-[#FFFBF9] p-1.5"*/}
                {/*  >*/}
                {/*    <Text className="font-airbnb_md text-[8px] font-medium text-primary-700">*/}
                {/*      {label}*/}
                {/*    </Text>*/}
                {/*  </View>*/}
                {/*))}*/}
              </View>
            </View>
          </ImageBackground>
          <View className="justify-center px-3 pt-3">
            <View className="flex-row items-center gap-5">
              <View className="flex-1">
                <Text className="font-airbnb_bd text-base font-bold text-text-main700">
                  {item.propertyTitle}
                </Text>
                <View className="mt-0.5 flex-row items-center">
                  <NImage
                    source={require('../../assets/icons/location2.png')}
                    className="w-2.5 h-2.5"
                  />
                  <Text className="ml-1 font-airbnb_bk text-[10px] font-normal text-text-600">
                    {item.propertyAddress}
                  </Text>
                </View>
              </View>
              <View className="">
                <Text className="font-airbnb_xbd text-lg font-extrabold text-primary-700">
                  {/*{item.area} {item.areaIn && FormatAreaIn(item.areaIn)}*/}
                  {formatPriceAddLabels(
                    calcPerUnitPriceAccordingToAreaUnit({
                      area: item.areaInSqMeters,
                      areaIn: 'SQUAREMETER',
                      propertyPrice: item.propertyPrice,
                    }),
                  )}
                  /{FormatAreaIn('SQUAREMETER')}
                </Text>
              </View>
            </View>

            <Text
              className="mt-3 font-airbnb_bk text-[10px] font-normal text-text-600"
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              {item.aboutProperty}
            </Text>

            <View className="flex-row items-center justify-between">
              <Button
                onPress={onAgentContactPress}
                loading={sendConnectionReqMutation.isPending}
                className="h-12 w-3/5"
                label="Contact Agent"
              />

              <View className="flex-row items-center">
                <PropertyWishlistBtn propertyId={item.id} />
                <PropertyShareBtn
                  propertyId={item.id}
                  propertyTitle={item.propertyTitle ?? ''}
                  aboutProperty={item.aboutProperty ?? ''}
                  bg={true}
                />
              </View>
            </View>
          </View>
        </ImageBackground>
      </View>
    </Pressable>
  );
};

export default FavouritesComponent;
