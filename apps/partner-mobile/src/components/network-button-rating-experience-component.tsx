import { Image as NImage } from 'expo-image';
import React from 'react';
import { Pressable, Text, View } from 'react-native';

type PropsType = {
  label: string;
  backgroundColor: string;
  textColor: string;
  onPress?: () => void;
};

export const RequestRatingComponent = () => {
  return (
    <View>
      <View className="mb-2 flex-row items-center self-end">
        <NImage
          source={require('../../assets/icons/star.png')}
          className="h-4 w-4"
        />
        <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-primary-750">
          4.5
        </Text>
      </View>

      <View className="flex-row items-center self-start rounded-md bg-primary-100 px-2.5 py-0.5">
        <Text className="font-airbnb_bk text-[10px] font-normal text-primary-750">
          Active Properties:
        </Text>
        <Text className="font-airbnb_bd text-[10px] font-bold text-primary-750">
          {' '}37
        </Text>
      </View>
    </View>
  );
};

export const ConnectionRatingComponent = () => {
  return (
    <View>
      <View className="flex-row items-center justify-between">
        <View className="mb-2 flex-row items-center self-end">
          <NImage
            source={require('../../assets/icons/experience1.png')}
            className="h-4 w-4"
          />
          <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-primary-750">
            5.2
          </Text>
        </View>

        <View className="mb-2 flex-row items-center self-end">
          <NImage
            source={require('../../assets/icons/star.png')}
            className="h-4 w-4"
          />
          <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-primary-750">
            4.5
          </Text>
        </View>
      </View>

      <View className="flex-row items-center self-start rounded-md bg-primary-100 px-2.5 py-0.5">
        <Text className="font-airbnb_bk text-[10px] font-normal text-primary-750">
          Active Properties:
        </Text>
        <Text className="font-airbnb_bd text-[10px] font-bold text-primary-750">
          {' '}37
        </Text>
      </View>
    </View>
  );
};

export const Button = ({
  label,
  backgroundColor,
  textColor,
  onPress,
}: PropsType) => {
  return (
    <Pressable
      className={`w-[44.4%] items-center self-start rounded-xl ${backgroundColor} px-6 py-3`}
      onPress={onPress}
    >
      <Text className={`font-airbnb_md text-base font-medium ${textColor}`}>
        {label}
      </Text>
    </Pressable>
  );
};
