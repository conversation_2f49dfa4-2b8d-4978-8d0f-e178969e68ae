import { ActivityIndicator, Pressable, View, Text } from 'react-native';
import React from 'react';
import { api } from '@/utils/api';
import { Image } from 'expo-image';
import { showMessage } from 'react-native-flash-message';

const LikeAgentButton = ({ agentId }: { agentId: string }) => {
  const utils = api.useUtils();
  const { data: isLiked } = api.likedAgents.isAgentLiked.useQuery(
    { agentId: agentId },
    {
      refetchOnWindowFocus: false,
    },
  );

  const likeAgentMutation = api.likedAgents.addLikedAgents.useMutation();

  const toggleLike = () => {
    likeAgentMutation.mutate(
      { agentId: agentId },
      {
        onSuccess: (resp) => {
          utils.likedAgents.isAgentLiked.invalidate();
          showMessage({
            message: resp.message || 'Added to Favorite',
            type: 'success',
          });
        },
        onError: (error) => {
          console.error('Error adding liked agent:', error);
          if (error.data) {
            console.error('Error details:', error.data);
          }
          const errorMessage =
            error.message || 'Error in adding agent to favourites';
          showMessage({
            message: errorMessage,
            type: 'danger',
          });
        },
      },
    );
  };

  return (
    <Pressable
      className="flex-1 flex-row items-center justify-center px-3 py-3.5 rounded-lg border-[1.5px] border-primary-750"
      onPress={() => {
        toggleLike();
      }}
    >
      {likeAgentMutation.isPending ? (
        <ActivityIndicator size={24} color="" />
      ) : (
        <View className="flex-row items-center">
          <Image
            source={
              isLiked
                ? require('@assets/icons/like11.png')
                : require('@assets/icons/unlike11.png')
            }
            className="mr-3 h-6 w-6"
            contentFit="contain"
          />
          <Text className="text-wrap text-base text-primary-750 font-airbnb_md font-medium">
            {isLiked ? 'Added to Favorite' : 'Add to Favorite'}
          </Text>
        </View>
      )}
    </Pressable>
  );
};

export default LikeAgentButton;
