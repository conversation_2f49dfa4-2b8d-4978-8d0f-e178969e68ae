import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useRouter } from 'expo-router';
import { useForm, SubmitHandler } from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { Pressable } from 'react-native';
import * as z from 'zod';
import {} from '@/ui/';

import { Button, ControlledInput, ScrollView, Text, View } from '@/ui';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';

const phoneRegex = /^[+]?[\s0-9]*$/;

// Create schema with explicit type
const schema = z.object({
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: 'Invalid phone number' })
    .min(10, 'Phone number must be at least 10 digits')
    .max(10, 'Phone number must be at most 10 digits'),
}) as z.ZodType<{ phoneNumber: string }>;

export type FormType = z.infer<typeof schema>;

export type SignInFormProps = {
  onSubmit: SubmitHandler<FormType>;
};

export const SignInForm = ({ onSubmit }: SignInFormProps) => {
  const router = useRouter();
  const { handleSubmit, control } = useForm<FormType>({
    resolver: zodResolver(schema as any),
  });

  const signInMutation = api.user.signin.useMutation();

  const onSubmitInternal = async (data: FormType) => {
    try {
      const resp = await signInMutation.mutateAsync(data);
      showMessage({
        message: resp.messageTitle as string,
        description: resp.messageDescription,
        type: 'success',
      });
      router.push({
        pathname: '/auth/otp',
        params: {
          phoneNumber: data.phoneNumber,
        },
      });
    } catch (error) {
      showMessage({
        type: 'danger',
        message: 'Please check your mobile number',
        // position: 'bottom',
      });
      console.error('Error during sign-in:', error);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      keyboardVerticalOffset={10}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flex: 1 }}
      >
        <View className="p-4">
          <Text
            testID="form-title"
            className="mb-2 font-airbnb_xbd text-2xl font-extrabold text-secondary-650"
          >
            Login
          </Text>
          <Text className="mb-5 font-airbnb_bk text-base font-normal text-text-550">
            Enter your phone number and login into your account
          </Text>

          <View className="mb-6">
            <ControlledInput
              testID="phone-number"
              control={control}
              keyboardType="numeric"
              placeholder="Enter your phone number here"
              name="phoneNumber"
              label="Phone Number"
            />
          </View>

          <Button
            testID="continue-button"
            label="Continue"
            loading={signInMutation.isPending}
            onPress={handleSubmit(onSubmitInternal)}
          />

          <View className="mt-4 flex-row items-center justify-center gap-4">
            <Pressable onPress={() => router.push('/auth/sign-up')}>
              <Text className="text-secondary-650">
                Don't have an account? Sign up
              </Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};
