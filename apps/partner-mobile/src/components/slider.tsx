import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Slider from '@react-native-community/slider';

interface SliderProps {
  label: string;
  minimumValue: number;
  maximumValue: number;
  value: number;
  sign?: string;
  step?: number;
  onChange: (value: number) => void;
}

const Sliderr: React.FC<SliderProps> = ({
  label,
  minimumValue,
  maximumValue,
  onChange,
  value,
  step,
  sign,
}) => {
  return (
    <View style={styles.container}>
      {/* Label and Value */}
      <View className="flex-row items-center justify-between w-full">
        <Text className="text-base font-medium text-text-600 font-airbnb_md">
          {label}
        </Text>
        <Text
          style={{ left: value * 0.0000001 }}
          className="text-base font-medium text-text-600 font-airbnb_md"
        >
          {value.toLocaleString()}
          {sign ? sign : ''}
        </Text>
      </View>

      {/* Slider */}
      <Slider
        style={styles.slider}
        minimumValue={minimumValue}
        maximumValue={maximumValue}
        minimumTrackTintColor="#C58E00"
        maximumTrackTintColor="#CECECE"
        thumbTintColor="#C58E00"
        value={value}
        step={step}
        onValueChange={onChange}
        tapToSeek={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    borderBottomWidth: 0.33,
    borderColor: '#525252',
  },
  slider: {
    width: '100%',
    height: 3,
  },
});

export default Sliderr;
