import { Modal, Text, View } from 'react-native';
import { Button, Pressable } from '@/ui';
import MultiSliderComponent from '@/components/multi-slider';
import z from 'zod';
import React, { useEffect, useState } from 'react';
import { Image } from 'expo-image';
import { ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FilterComponent from '@/components/filter-input';
import { api } from '@/utils/api';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useLocalSearchParams, router } from 'expo-router';
import { CustomerPropertiesFilterSchema } from '@/utils/form-validators';
import { usePropertyFiltersStore } from '@/store/property-filters.store';

type IFilterForm = z.infer<typeof CustomerPropertiesFilterSchema>;

const PropertyFilters = ({
  isVisible,
  totalResults,
  handleModalVisible,
}: {
  isVisible: boolean;
  totalResults: number;
  handleModalVisible: () => void;
}) => {
  const [filter, setFilter] = useState<IFilterForm>({
    propertyFor: 'SALE',
    // propertyCategory: 'RESIDENTIAL',
    areaUnitId: '',
    take: '10',
  });
  const { propertyFor, propertyCategory, setMultipleFilters, resetFilters } =
    usePropertyFiltersStore();
  const { data: propertyCategories, isLoading } =
    api.postProperty.getPropertyCategories.useQuery();
  // const { data, isLoading } = api.listing.getAllDynamicFilters.useQuery(
  //   propertyCategoryId
  //     ? {
  //         propertyCategoryId: propertyCategoryId,
  //       }
  //     : skipToken,
  // );
  const insets = useSafeAreaInsets();

  useEffect(() => {
    setFilter(() => ({
      ...filter,
      propertyFor,
      propertyCategory,
    }));
  }, [propertyFor, propertyCategory]);

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleModalVisible}
    >
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255, 245, 242, 0.50)',
          paddingTop: insets.top,
        }}
      >
        <View className="px-5 py-5 flex-1 bg-white w-full">
          {/* heading */}
          <View className="pb-5 flex-row items-center justify-between">
            <Pressable onPress={handleModalVisible} className="w-24">
              <Image
                source={require('../../assets/icons/cancel.png')}
                contentFit="contain"
                className="h-6 w-6"
                tintColor={'#252525'}
              />
            </Pressable>

            <Text
              className="text-base font-medium font-airbnb_md text-secondary-850"
              numberOfLines={1}
              ellipsizeMode="tail"
              style={{ maxWidth: '50%' }}
            >
              Filter
            </Text>
            <Text
              className="w-24 text-sm font-normal text-text-550 font-airbnb_bk text-right"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {totalResults} result
            </Text>
          </View>

          <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
            <View className="gap-4">
              <FilterComponent
                FilterOptions={['SALE', 'RENT'].map((item) => ({
                  label: item,
                  value: item,
                }))}
                value={propertyFor ? [propertyFor] : []}
                onValueChange={(value) => {
                  console.log('value', value);
                  setFilter((filter) => ({
                    ...filter,
                    propertyFor: value[0] as 'SALE' | 'RENT',
                  }));
                }}
                singleSelect={true}
                label={'Property For'}
              />
              <FilterComponent
                FilterOptions={
                  propertyCategories?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  })) ?? []
                }
                value={filter.propertyCategory ? [filter.propertyCategory] : []}
                onValueChange={(value) => {
                  console.log('value', value);
                  setFilter((filter) => ({
                    ...filter,
                    propertyCategory: value[0],
                  }));
                }}
                singleSelect={true}
                label={'Property Category'}
              />
              {/*<FilterComponent FilterOptions={[]} label={'Property Type'} />*/}
              {/*<FilterComponent FilterOptions={[]} label={'Pet Allowed'} />*/}
              {/*<MultiSliderComponent*/}
              {/*  label={'Price Range'}*/}
              {/*  // step={1}*/}
              {/*  minimumValue={0}*/}
              {/*  maximumValue={5_00_00_000}*/}
              {/*  // value={price}*/}
              {/*  // onChange={setPrice}*/}
              {/*/>*/}
            </View>
          </ScrollView>
          {/* buttons */}
          <View className="flex-row  items-center justify-between gap-6">
            <Button
              onPress={() => {
                resetFilters();
                handleModalVisible();
              }}
              label="Clear all result"
              className="flex-1 bg-secondary-100"
              textClassName="text-secondary-main700"
            />
            <Button
              label="Apply"
              onPress={() => {
                console.log('filter', filter);
                setMultipleFilters(filter);
                handleModalVisible();
              }}
              className="flex-1 bg-secondary-main700"
              textClassName="text-white"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PropertyFilters;
