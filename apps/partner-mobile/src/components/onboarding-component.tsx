import { Image as NImage } from 'expo-image';
import React from 'react';
import { Dimensions, type ImageSourcePropType, Text, View } from 'react-native';
const { height } = Dimensions.get('screen');

type OnboardingComponentProps = {
  label: string;
  text: string;
  image: ImageSourcePropType;
};

const OnboardingComponent: React.FC<OnboardingComponentProps> = ({
  label,
  text,
  image,
}) => {
  return (
    <View
      className="items-center justify-start"
      style={{ height: height * 0.55 }}
    >
      <NImage
        source={image}
        contentFit="contain"
        className="aspect-square w-[323px] h[323px]"
      />
      <Text className="mt-9 text-center font-airbnb_xbd text-2xl font-extrabold text-primary-700">
        {label}
      </Text>
      <Text className="mt-2 text-center font-airbnb_bk text-base font-normal text-text-main700">
        {text}
      </Text>
    </View>
  );
};

export default OnboardingComponent;
