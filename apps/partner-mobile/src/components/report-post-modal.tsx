import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { TextInput } from 'react-native-gesture-handler';
import { showMessage } from 'react-native-flash-message';
import { Modal, useModal } from '@/ui/modal';
import { Button } from '@/ui';
import { api } from '@/utils/api';

const reportPostSchema = z.object({
  reason: z
    .string()
    .min(5, { message: 'Text should be more than 5 characters' }),
});

type ReportPostFormData = z.infer<typeof reportPostSchema>;

interface ReportPostModalProps {
  postId: string;
}

const ReportPostModal = React.forwardRef<any, ReportPostModalProps>(
  ({ postId }, ref) => {
    const { mutate: reportPost, isPending } = api.social.reportPost.useMutation();
    const utils = api.useUtils();

    const form = useForm<ReportPostFormData>({
      resolver: zodResolver(reportPostSchema),
      defaultValues: {
        reason: '',
      },
    });

    const onSubmit = (values: ReportPostFormData) => {
      const { reason } = values;
      reportPost(
        { postId, reason },
        {
          onSuccess: (response) => {
            showMessage({
              message: response.message,
              type: 'success',
            });
            form.reset();
            // Close modal
            if (ref && 'current' in ref && ref.current) {
              ref.current.dismiss();
            }
            // Invalidate feed to refresh
            utils.social.invalidate();
          },
          onError: (error) => {
            showMessage({
              message: error.message,
              type: 'danger',
            });
          },
        },
      );
    };

    return (
      <Modal ref={ref} title="Report Post" snapPoints={['50%']}>
        <View className="px-4 pb-6">
          <Text className="mb-4 text-sm text-text-500">
            Please state your reason for reporting this post. Type in your response and click submit to report this post.
          </Text>

          <Controller
            control={form.control}
            name="reason"
            render={({ field, fieldState }) => (
              <View className="mb-4">
                <TextInput
                  className="min-h-[100px] p-3 border border-text-100 rounded-lg bg-white text-text-600"
                  placeholder="Type here..."
                  placeholderTextColor="#6B6B6B"
                  value={field.value}
                  onChangeText={field.onChange}
                  multiline
                  textAlignVertical="top"
                />
                {fieldState.error && (
                  <Text className="mt-1 text-sm text-red-500">
                    {fieldState.error.message}
                  </Text>
                )}
              </View>
            )}
          />

          <Button
            label="Submit"
            loading={isPending}
            onPress={form.handleSubmit(onSubmit, (errors) => {
              if (errors.reason) {
                showMessage({
                  message: errors.reason.message || 'Please provide a valid reason',
                  type: 'danger',
                });
              }
            })}
            className="w-full"
          />
        </View>
      </Modal>
    );
  },
);

ReportPostModal.displayName = 'ReportPostModal';

export default ReportPostModal;
