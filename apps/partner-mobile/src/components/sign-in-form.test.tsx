import React from 'react';
import { cleanup, fireEvent, render, screen, waitFor } from '@/core/test-utils';
import { SignInForm } from './sign-in';
import type { SignInFormProps } from './sign-in';

afterEach(cleanup);

const onSubmitMock: jest.Mock<SignInFormProps['onSubmit']> = jest.fn();

describe('SignInForm', () => {
  it('renders correctly', async () => {
    render(<SignInForm onSubmit={onSubmitMock} />);
    expect(await screen.findByText(/Login/i)).toBeOnTheScreen();
  });

  it('should display required error when values are empty', async () => {
    render(<SignInForm onSubmit={onSubmitMock} />);

    const button = screen.getByTestId('continue-button');
    expect(screen.queryByText(/Phone number is required/i)).not.toBeOnTheScreen();

    fireEvent.press(button);
    expect(await screen.findByText(/Phone number is required/i)).toBeOnTheScreen();
  });

  it('should display error when phone number is invalid', async () => {
    render(<SignInForm onSubmit={onSubmitMock} />);

    const button = screen.getByTestId('continue-button');
    const phoneInput = screen.getByTestId('phone-number');

    fireEvent.changeText(phoneInput, 'invalid-phone');
    fireEvent.press(button);

    expect(await screen.findByText(/Invalid phone number/i)).toBeOnTheScreen();
  });

  it('should call onSubmit with correct values when valid values are provided', async () => {
    render(<SignInForm onSubmit={onSubmitMock} />);

    const button = screen.getByTestId('continue-button');
    const phoneInput = screen.getByTestId('phone-number');

    fireEvent.changeText(phoneInput, '1234567890');
    fireEvent.press(button);

    await waitFor(() => {
      expect(onSubmitMock).toHaveBeenCalledTimes(1);
    });

    expect(onSubmitMock).toHaveBeenCalledWith(
      {
        phoneNumber: '1234567890',
      },
      undefined,
    );
  });
});
