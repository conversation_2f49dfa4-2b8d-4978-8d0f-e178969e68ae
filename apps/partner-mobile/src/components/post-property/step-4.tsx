/* eslint-disable max-lines-per-function */
import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import React, { useState, useRef, useEffect } from 'react';
import {
  Alert,
  Dimensions,
  Modal,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
  ActivityIndicator,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { BlurButtonWithBack } from '@/components/blur-button';
import { Button, Input } from '@/ui';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { signedUploadToCloudinary } from '@/api/cloudinary';

type PropertyMediaTypeEnum = "IMAGE" | "VIDEO"

const { height, width } = Dimensions.get('screen');

interface CloudinaryImageData {
  url: string;
  publicId: string;
}

const ImagePick = ({
  propertyId,
  sectionId,
  images,
  refetchMedia,
  setIsUploading,
}: {
  propertyId: string;
  sectionId: string;
  images: {
    id: string;
    fileKey: string | null;
    filePublicUrl: string | null;
    cloudinaryId: string | null;
    cloudinaryUrl: string | null;
  }[];
  refetchMedia: () => void;
  setIsUploading: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const createNewMediaMutation =
    api.postProperty.createNewPropertyMedia.useMutation();
  const deleteMediaMutation =
    api.postProperty.deletePropertyExistingMedia.useMutation();
  const [isUploadingLocal, setIsUploadingLocal] = useState<boolean>(false);
  const [deletingMediaIds, setDeletingMediaIds] = useState<string[]>([]);

  // Create a stable timestamp for the signature query
  const timestampRef = useRef(Math.round(Date.now() / 1000));

  // Fetch signature for Cloudinary upload
  const { data: signatureData } = api.cloudinary.generateSignature.useQuery({
    paramsToSign: {
      timestamp: timestampRef.current,
      folderFor: 'users',
      forlderPurpose: 'properties',
    },
  });

  // Track whether any operation is in progress
  const isOperationInProgress = isUploadingLocal || deletingMediaIds.length > 0;

  // Update parent component's upload state
  useEffect(() => {
    setIsUploading(isOperationInProgress);
  }, [isOperationInProgress, setIsUploading]);

  const deleteMedia = async (mediaId: string) => {
    try {
      setDeletingMediaIds((prev) => [...prev, mediaId]);
      await deleteMediaMutation.mutateAsync({
        mediaId,
      });
      refetchMedia();
      showMessage({
        message: 'Media deleted successfully',
        type: 'success',
      });
    } catch (error) {
      showMessage({
        message: 'Failed to delete media',
        type: 'danger',
      });
    } finally {
      setDeletingMediaIds((prev) => prev.filter((id) => id !== mediaId));
    }
  };

  const pickImages = async () => {
    if (isOperationInProgress) return;

    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission denied',
        'Sorry, we need camera permissions to make this work!',
      );
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.75,
      selectionLimit: 1,
    });

    if (!result.canceled) {
      uploadToCloudinary(result.assets[0].uri);
    }
  };

  const uploadToCloudinary = async (imageUri: string) => {
    try {
      setIsUploadingLocal(true);

      if (!signatureData || !signatureData.signature) {
        throw new Error('Signature not available');
      }

      // Upload the image to Cloudinary
      const result = await signedUploadToCloudinary({
        imageUri: imageUri,
        folder: signatureData.uploadFolderUrl,
        signature: signatureData.signature,
        timestamp: timestampRef.current.toString(),
        preset: signatureData.cloudPreset,
        apiKey: signatureData.apiKey,
        cloudName: signatureData.cloudName,
      });

      // Create property media with Cloudinary data
      await createNewMediaMutation.mutateAsync({
        cloudinaryId: result.public_id,
        cloudinaryUrl: result.secure_url,
        mediaSectionId: sectionId,
        mediaType: PropertyMediaTypeEnum.IMAGE,
      });

      refetchMedia();
      showMessage({
        message: 'Image uploaded successfully',
        type: 'success',
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      showMessage({
        message: 'Failed to upload image',
        type: 'danger',
      });
    } finally {
      setIsUploadingLocal(false);
    }
  };

  // Function to get the display URL of an image
  const getImageUrl = (image: {
    cloudinaryUrl: string | null;
    filePublicUrl: string | null;
  }) => {
    return image.cloudinaryUrl || image.filePublicUrl;
  };

  return (
    <View className="flex-row items-center">
      <Pressable
        onPress={pickImages}
        disabled={isOperationInProgress}
        className={`mr-8 items-center justify-center rounded-xl border-2 border-primary-700 bg-primary-50 ${isOperationInProgress ? 'opacity-50' : ''}`}
        style={{ height: 75, width: 75 }}
      >
        {isUploadingLocal ? (
          <ActivityIndicator size={40} color="#c58e00" />
        ) : (
          <Text className="text-5xl text-primary-700">+</Text>
        )}
      </Pressable>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {images.map((image, index) => (
          <View
            key={index}
            className="mr-8 overflow-hidden rounded-xl border-2 border-primary-700"
          >
            {deletingMediaIds.includes(image.id) ? (
              <View
                style={{ height: 75, width: 75 }}
                className="items-center justify-center"
              >
                <ActivityIndicator size="small" color="#c58e00" />
              </View>
            ) : (
              <>
                <NImage
                  source={{ uri: getImageUrl(image) }}
                  style={{ height: 75, width: 75, resizeMode: 'cover' }}
                />
                <Pressable
                  onPress={() =>
                    !isOperationInProgress && deleteMedia(image.id)
                  }
                  disabled={isOperationInProgress}
                  className={`absolute right-0 top-0 rounded-full bg-[#FFF2F2] p-1 ${isOperationInProgress ? 'opacity-50' : ''}`}
                >
                  <NImage
                    source={require('../../../assets/icons/delete.png')}
                    className="h-4 w-4"
                  />
                </Pressable>
              </>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const Step4 = ({ onPrevious }: { onPrevious: () => void }) => {
  const { propertyId } = useLocalSearchParams<{ propertyId: string }>();

  // Query to check if step 3 should be skipped
  const { data: step3Property } = api.postProperty.getStep3.useQuery(
    { id: propertyId },
    { enabled: !!propertyId },
  );

  // Check if step 3 has nothing to show
  const nothingtoShow =
    step3Property?.PropertyCategory?.showSocietyName === 'HIDE' &&
    step3Property?.PropertyCategory?.showBuildYear === 'HIDE' &&
    step3Property?.PropertyCategory?.showPossessionState === 'HIDE' &&
    step3Property?.PropertyCategory?.showFurnishing === 'HIDE' &&
    step3Property?.PropertyCategory?.showFacing === 'HIDE' &&
    step3Property?.PropertyCategory?.showTotalFloors === 'HIDE' &&
    step3Property?.PropertyCategory?.showFloorNumber === 'HIDE' &&
    step3Property?.PropertyCategory?.showCarParking === 'HIDE' &&
    step3Property?.PropertyCategory?.showPropertyState === 'HIDE';

  // Custom previous handler to skip step 3 if it has nothing to show
  const handlePrevious = () => {
    if (nothingtoShow) {
      // If step 3 has nothing to show, go directly to step 2
      onPrevious();
      onPrevious(); // Call onPrevious twice to skip step 3
    } else {
      onPrevious(); // Normal previous step behavior
    }
  };
  const [inputValue, setInputValue] = useState('');
  const router = useRouter();
  const searchParams = useLocalSearchParams<{ propertyId: string }>();

  const { data: property, refetch } = api.postProperty.getStep4.useQuery(
    {
      id: searchParams.propertyId,
    },
    {
      enabled: !!searchParams.propertyId,
    },
  );
  const createMediaSectionMutation =
    api.postProperty.createPropertyMediaSection.useMutation();
  const deleteMediaSectionMutation =
    api.postProperty.deletePropertyMediaSection.useMutation();
  const deleteMediaMutation =
    api.postProperty.deletePropertyExistingMedia.useMutation();
  const statusMutation = api.postProperty.updatePropertyStatus.useMutation();
  const [modalVisible, setModalVisible] = useState(false);
  const [isDeletingSection, setIsDeletingSection] = useState<string | null>(
    null,
  );
  const [isAnyUploadInProgress, setIsAnyUploadInProgress] = useState(false);

  // Function to check if any operation is pending
  const isAnyOperationPending = () => {
    return (
      createMediaSectionMutation.isPending ||
      deleteMediaSectionMutation.isPending ||
      deleteMediaMutation.isPending ||
      isAnyUploadInProgress ||
      isDeletingSection !== null
    );
  };

  const handleRemoveItem = (
    index: number,
    array: File[] | undefined,
    setArray: React.Dispatch<React.SetStateAction<File[] | undefined>>,
  ) => {
    if (!array) return;
    const newArray = array.filter((_, i) => i !== index);
    setArray(newArray.length > 0 ? newArray : undefined);
  };

  const handleSubmit = () => {
    if (property) {
      const totalImages = property.mediaSections.reduce((total, item) => {
        return total + item.media.length;
      }, 0);
      if (totalImages < 3) {
        showMessage({
          message: 'Please add atleast 3 images',
          type: 'danger',
        });
        return;
      }

      console.log('inside the mutation for updating the status');
      statusMutation
        .mutateAsync({ id: searchParams.propertyId })
        .then((resp) => {
          showMessage({
            message: 'Property updated successfully',
            type: 'success',
          });
          router.push('/listings');
        })
        .catch((err) => {
          showMessage({
            message: err.message,
            type: 'danger',
          });
          console.log('error occured------------------', err);
        });
    } else {
      showMessage({
        message: 'Property not found',
        type: 'danger',
      });
    }
  };

  const handleCancel = () => {
    console.log('inside the handle cancel');
    setModalVisible(false);
    setInputValue('');
  };

  const handleSave = () => {
    if (!inputValue.trim()) {
      showMessage({
        message: 'Please enter a section name',
        type: 'danger',
      });
      return;
    }

    createMediaSectionMutation
      .mutateAsync({
        title: inputValue,
        propertyId: searchParams.propertyId,
      })
      .then((resp) => {
        refetch();
        setModalVisible(false);
        setInputValue('');
        showMessage({
          message: 'Media section added successfully',
          type: 'success',
        });
      })
      .catch((err) => {
        console.log('error is', err);
        showMessage({
          message: 'Failed to add media section',
          type: 'danger',
        });
      });
  };

  const deleteMediaSection = async (sectionId: string) => {
    try {
      setIsDeletingSection(sectionId);

      // Find the section to delete
      const section = property?.mediaSections.find((s) => s.id === sectionId);
      if (!section) {
        throw new Error('Section not found');
      }

      // Delete all media in the section first
      if (section.media.length > 0) {
        for (const media of section.media) {
          await deleteMediaMutation.mutateAsync({
            mediaId: media.id,
          });
        }
      }

      // Then delete the section
      const resp = await deleteMediaSectionMutation.mutateAsync({
        mediaSectionId: sectionId,
      });

      refetch();
      showMessage({
        message: resp.message || 'Section deleted successfully',
        type: 'success',
      });
    } catch (e) {
      console.error('Error deleting section:', e);
      showMessage({
        message: 'Error deleting media section. Please try again.',
        type: 'danger',
      });
    } finally {
      setIsDeletingSection(null);
    }
  };

  const saveMediaSecionModal = (
    <View className="flex-1 w-full items-center justify-center bg-black/50">
      <View className="rounded-2xl bg-white px-5 py-6">
        <Pressable
          onPress={handleCancel}
          className="absolute z-20 right-3 top-3 rounded-full bg-red-500 p-2"
        >
          <NImage
            source={require('../../../assets/icons/cancel.png')}
            className="h-4 w-4"
            tintColor={'white'}
          />
        </Pressable>
        <View className="items-center">
          <Text className="mb-1 font-airbnb_bd text-xl font-bold text-secondary-main700">
            Add Media Section
          </Text>
          <Text className="font-airbnb_bk text-xs font-normal text-text-550">
            By entering the heading name you'll be adding a media file.
          </Text>
        </View>

        <View className="mt-6">
          <Input
            label="Section Name"
            placeholder="Eg. Bathroom"
            value={inputValue}
            onChangeText={setInputValue}
          />
        </View>

        <View className="items-center">
          <Button
            label="Save"
            loading={createMediaSectionMutation.isPending}
            onPress={handleSave}
            className="w-[32.33%] px-6 py-3.5"
          />
        </View>
      </View>
    </View>
  );

  return (
    <View className="h-[90%] w-full">
      <ScrollView className="mb-10 h-full" showsVerticalScrollIndicator={false}>
        <View className="mb-5 flex-row items-center justify-between px-5">
          <View>
            <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-700">
              Media
            </Text>
            <Text className="mb-5 mt-1 font-airbnb_bk text-xs font-normal text-text-550">
              Add minimum 3 photos to continue further
            </Text>
          </View>
          <Pressable
            className={`flex-row items-center ${isAnyOperationPending() ? 'opacity-50' : ''}`}
            onPress={() => !isAnyOperationPending() && setModalVisible(true)}
            disabled={isAnyOperationPending()}
          >
            <NImage
              source={require('../../../assets/icons/plus.png')}
              className="mr-1 h-3 w-3"
            />
            <Text className="font-airbnb_md text-sm font-medium text-secondary-main700">
              Add Media
            </Text>
          </Pressable>
        </View>

        {property &&
          property.mediaSections.map((section, sectionIndex) => (
            <View key={sectionIndex} className="mb-2.5">
              <View className="flex-row items-center justify-between px-5">
                <Text className="mb-0.5 font-airbnb_md text-base font-medium text-text-600">
                  {section.title}
                </Text>
                {isDeletingSection === section.id ? (
                  <ActivityIndicator size="small" color="#c58e00" />
                ) : (
                  <Pressable
                    onPress={() =>
                      !isAnyOperationPending() && deleteMediaSection(section.id)
                    }
                    disabled={isAnyOperationPending()}
                    className={isAnyOperationPending() ? 'opacity-50' : ''}
                  >
                    <Text className="text-red-500">Delete Row</Text>
                  </Pressable>
                )}
              </View>
              <View className="flex-row items-center justify-between pl-5">
                <View style={{ flex: 1 }}>
                  <ImagePick
                    propertyId={searchParams.propertyId}
                    sectionId={section.id}
                    images={section.media}
                    refetchMedia={refetch}
                    setIsUploading={setIsAnyUploadInProgress}
                  />
                </View>
              </View>
            </View>
          ))}
        <View className="mb-24" />
      </ScrollView>

      <BlurButtonWithBack
        onPrevious={handlePrevious}
        loading={statusMutation.isPending}
        label="Post"
        onNext={handleSubmit}
      />

      {/* Modal Pop-up to add a custom section */}
      <Modal transparent={true} animationType="fade" visible={modalVisible}>
        {Platform.OS === 'ios' ? (
          <BlurView
            intensity={40}
            style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
            tint="dark"
          >
            {saveMediaSecionModal}
          </BlurView>
        ) : (
          saveMediaSecionModal
        )}
      </Modal>
    </View>
  );
};

export default Step4;
