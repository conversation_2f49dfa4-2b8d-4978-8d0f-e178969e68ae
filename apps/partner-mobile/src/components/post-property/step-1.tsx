import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { Text, View, Pressable, ActivityIndicator } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import CustomPicker from '@/components/post-property/custom-picker';
import { BlurButton } from '@/components/blur-button';
import { ControlledInput } from '@/components/post-property-component';
import { router, useLocalSearchParams } from 'expo-router';
import DocUploadModal from './doc-upload-modal';
import { cn } from '@/ui';
import { PropertyForEnum, step1FormSchema } from '@/utils/form-validators';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import * as WebBrowser from 'expo-web-browser';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { TRPCClientError } from '@trpc/client';

const ACCEPTED_FILE_TYPES = ['application/pdf', 'image/png'];

const FILE_TYPE_ERROR =
  'Registry letter file is required. Only PDF and PNG files are allowed';
const REQUIRED_FILE_ERROR = 'Please upload a registry letter file';

type FormValues = z.infer<typeof extendedStep1FormSchema>;

const extendedStep1FormSchema = step1FormSchema.extend({
  registryLetterFile: z
    .custom<FileList>()
    .optional()
    .transform((files) => {
      if (!files || files.length === 0) return undefined;
      return files[0];
    })
    .refine((file) => {
      if (!file) return true; // Skip file type validation if no file
      return ACCEPTED_FILE_TYPES.includes(
        file.type as 'application/pdf' | 'image/png',
      );
    }, FILE_TYPE_ERROR),
  // registeryFileKey: z.string().optional(),
});

// eslint-disable-next-line max-lines-per-function
const Step1 = ({ onNext }: { onNext: () => void }) => {
  const searchParams = useLocalSearchParams<{ propertyId: string }>();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const { data: profile } = api.user.profile.useQuery();
  const form = useForm<FormValues>({
    resolver: zodResolver(extendedStep1FormSchema),
    defaultValues: {
      registeryFileKey: '',
      propertyTitle: '',
      propertyFor: PropertyForEnum[0],
      propertyCategoryId: undefined,
      propertyTypeId: undefined,
      bedrooms: undefined,
      bathrooms: undefined,
      propertyPrice: undefined,
      securityDeposit: undefined,
      areaUnitId: undefined,
      area: undefined,
      aboutProperty: '',
    },
    mode: 'onChange',
  });

  const { control, watch, reset, setValue, handleSubmit } = form;

  const propertyFor = watch('propertyFor');
  const propertyCategory = watch('propertyCategoryId');
  const registeryFileKeyWatch = watch('registeryFileKey');
  const propertyId = searchParams.propertyId;

  const { data: property, isPending: isPropertyPending } =
    api.postProperty.getStep1.useQuery(
      {
        id: propertyId,
      },
      {
        enabled: !!propertyId,
      },
    );
  // const {
  //   data: { showPropertyType },
  // } = api.postProperty.getPropertyCategoryById.useQuery(
  //   {
  //     categoryId: propertyCategory,
  //   },
  //   { enabled: !!propertyCategory },
  // );
  const { data: propertyTypes } = api.postProperty.getPropertyType.useQuery(
    {
      categoryId: propertyCategory,
    },
    {
      enabled: !!propertyCategory,
    },
  );
  const { data: areaUnits } = api.postProperty.getAreaUnits.useQuery(
    {
      categoryId: propertyCategory,
    },
    { enabled: !!propertyCategory },
  );
  const { data: propertyCategories } =
    api.postProperty.getPropertyCategories.useQuery();

  const {
    data: selectedPropertyCategory,
    isPending: isPendingSelectedPropertyCategory,
  } = api.postProperty.getSelectedPropertyCategory.useQuery(
    { categoryId: propertyCategory },
    { enabled: !!propertyCategory },
  );

  const step1Mutation = api.postProperty.step1.useMutation({
    onSuccess: (resp) => {
      showMessage({
        message: resp.messageTitle,
        type: 'success',
      });
      onNext();
      if (!propertyId) {
        router.push(
          `/upload-post/post-property?propertyId=${resp.propertyId}&step=2`,
        );
      }
    },
    onError: (err) => {
      console.log(
        'error shape is ',
        err.data?.zodError?.fieldErrors,
        err.shape?.message,
        typeof err.shape?.message,
        Array.isArray(err.shape?.message),
      );

      const errorMessages = err.data?.zodError?.fieldErrors
        ? Object.entries(err.data.zodError.fieldErrors)
            .map(
              ([key, errors]) =>
                `${key}: ${(errors as string[])?.[0] || 'Validation error'}`,
            )
            .join('; ')
        : err.message || 'An unknown error occurred';

      showMessage({
        message: errorMessages,
        type: 'danger',
      });
    },
  });
  const awsMutation = api.aws.getPresignedUrl.useMutation();
  const awsGetPrivateFileUrlMutation = api.aws.getPrivateFileUrl.useMutation();

  useEffect(() => {
    if (propertyFor === 'SALE') {
      setValue('securityDeposit', 0);
    }
  }, [propertyFor, setValue]);
  useEffect(() => {
    if (propertyFor === 'RENT') {
      form.setValue('securityDeposit', 0);
    } else {
      form.setValue('securityDeposit', undefined);
    }
  }, [propertyFor, form]);

  const onSubmit = async (data: z.infer<typeof step1FormSchema>) => {
    // Ensure bedrooms and bathrooms are numbers
    const bedrooms = Number(data.bedrooms);
    const bathrooms = Number(data.bathrooms);
    const securityDeposit = Number(data.securityDeposit);
    const ab = await form.trigger('aboutProperty');
    console.log('ab', ab);
    console.log('form errors', form.formState.errors);

    if (!ab) {
      return;
    }
    // Prepare form data
    const formData = {
      ...data,
      propertyPrice: Number(data.propertyPrice),
      bedrooms: isNaN(bedrooms) ? undefined : bedrooms,
      bathrooms: isNaN(bathrooms) ? undefined : bathrooms,
      securityDeposit: isNaN(securityDeposit) ? undefined : securityDeposit,
      area: Number(data.area),
      id: propertyId,
    };
    step1Mutation.mutateAsync({ ...formData, id: propertyId });
  };

  const requestPermissions = async () => {
    const { status: cameraRollStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    return cameraRollStatus === 'granted';
  };

  const pickDocument = async () => {
    const result = await DocumentPicker.getDocumentAsync({
      copyToCacheDirectory: true,
      multiple: false,
      type: ['application/pdf', 'image/png'],
    });

    // Check the type of the result
    if (result && !result.canceled) {
      // Now we can safely access uri
      const document = result.assets[0];
      setSelectedFile(document); // This is the successful document result
      console.log('Selected File:', document);
      uploadFile('property-doc.jpeg', document.uri, 'image/jpeg');
    } else {
      console.log('Document picking was canceled or failed.');
    }

    setModalVisible(false);
  };

  const pickImage = async () => {
    if (!(await requestPermissions())) return;

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setSelectedFile(result.assets[0]);
      uploadFile(
        result.assets[0].fileName ?? 'property-doc.jpeg',
        result.assets[0].uri,
        'image/jpeg',
      );
    }
    setModalVisible(false);
  };

  const removeDoc = () => {
    setSelectedFile(null);
    setModalVisible(false);
  };

  const uploadFile = async (
    fileName: string,
    filePath: string,
    fileType: string,
  ) => {
    setIsUploading(true);
    const key =
      `/properties/${propertyId ? `${propertyId}/` : ''}` +
      Date.now() +
      '-' +
      fileName;
    const { url, key: preSignedUrlKey } = await awsMutation.mutateAsync({
      fileName: key,
      contentType: fileType,
      publicAvailable: false,
    });
    console.log('url isss', url);

    if (!url) {
      setIsUploading(false);
      showMessage({
        message: 'Something went wrong',
        type: 'danger',
      });
      return;
    }

    console.log('fileType', {
      name: fileName,
      type: fileType,
      uri: filePath,
    });

    const response = await fetch(filePath);
    const blob = await response.blob(); // Convert the image to a blob

    const requestOptions = {
      method: 'PUT',
      headers: {
        'Content-Type': blob.type,
      },
      body: blob,
    };
    const res = await fetch(url, requestOptions);
    console.log('res', res);
    setValue('registeryFileKey', preSignedUrlKey);
    setIsUploading(false);
  };

  const viewUploadedFile = async () => {
    const fileKey = registeryFileKeyWatch;
    if (!fileKey) {
      return;
    }

    const { url, error } = await awsGetPrivateFileUrlMutation.mutateAsync({
      fileKey,
      expiry: 10 * 60,
    });

    if (error || !url) {
      showMessage({
        message: 'Unable to open the url',
        type: 'danger',
      });
      return;
    }

    WebBrowser.openBrowserAsync(url);
  };

  // useEffect(() => {
  //   if (property) {
  //     console.log('property', property);
  //     reset({
  //       propertyTitle: property.propertyTitle ?? '',
  //       propertyFor: property.propertyFor,
  //       propertyCategoryId: property.PropertyCategory?.id ?? '',
  //       propertyTypeId: property.propertyTypeId,
  //       bedrooms: property.bedrooms?.toString(),
  //       bathrooms: property.bathrooms?.toString(),
  //       propertyPrice:
  //         property.propertyPrice && property.propertyPrice.toString(),
  //       securityDeposit: property.securityDeposit
  //         ? property.securityDeposit.toString()
  //         : '0',
  //       areaUnitId: property.areaUnitId,
  //       area: property.area?.toString(),
  //       aboutProperty: property.aboutProperty,
  //       registeryFileKey: property.registeryFileKey ?? '',
  //     });
  //     // setValue('propertyTitle', property.propertyTitle ?? '');
  //     // setValue('propertyFor', property.propertyFor);
  //     // setValue('propertyCategoryId', property.PropertyCategory?.id ?? '');
  //     // setValue('propertyTypeId', property.propertyTypeId);
  //     // setValue('bedrooms', property.bedrooms ?? 0);
  //     // setValue('bathrooms', property.bathrooms ?? 0);
  //     // setValue(
  //     //   'propertyPrice',
  //     //   property.propertyPrice ? Number(property.propertyPrice) : 0,
  //     // );
  //     // setValue(
  //     //   'securityDeposit',
  //     //   property.securityDeposit ? Number(property.securityDeposit) : 0,
  //     // );
  //     // setValue('areaIn', property.areaIn);
  //     // setValue('area', property.area ?? 0);
  //     // setValue('aboutProperty', property.aboutProperty ?? '');
  //     // setValue('registeryFileKey', property.registeryFileKey ?? '');
  //   }
  // }, [property]);

  useEffect(() => {
    if (property) {
      form.setValue('propertyTitle', property.propertyTitle ?? '');
      form.setValue('propertyFor', property.propertyFor);
      form.setValue('propertyCategoryId', property.propertyCategoryId ?? '');
      form.setValue('propertyTypeId', property.propertyTypeId);
      form.setValue(
        'bedrooms',
        property.bedrooms ? property.bedrooms.toString() : undefined,
      );
      form.setValue(
        'bathrooms',
        property.bathrooms ? property.bathrooms.toString() : undefined,
      );
      form.setValue(
        'propertyPrice',
        property.propertyPrice ? property.propertyPrice : 0,
      );
      form.setValue(
        'securityDeposit',
        property.securityDeposit ? property.securityDeposit : undefined,
      );
      form.setValue('areaUnitId', property.areaUnitId ?? '');
      form.setValue('area', property.area ? property.area.toString() : '0');
      form.setValue('aboutProperty', property.aboutProperty ?? undefined);
      form.setValue('registeryFileKey', property.registeryFileKey ?? '');
      //   form.setValue("propertyTypeId", property.propertyTypeId);
      //   form.setValue("areaUnitId", property.areaUnitId ?? "");
    }
  }, [property, form, isPendingSelectedPropertyCategory, isPropertyPending]);

  //   console.log("categoryid", form.getValues("propertyCategoryId"));

  useEffect(() => {
    if (propertyId) return;

    if (selectedPropertyCategory?.showBedrooms === 'SHOW') {
      form.setValue('bedrooms', 0);
    } else {
      form.setValue('bedrooms', undefined);
    }
    if (selectedPropertyCategory?.showBathrooms === 'SHOW') {
      form.setValue('bathrooms', 0);
    } else {
      form.setValue('bathrooms', undefined);
    }
    if (selectedPropertyCategory?.showAboutProperty === 'SHOW') {
      form.setValue('aboutProperty', '');
    } else {
      form.setValue('aboutProperty', undefined);
    }
  }, [selectedPropertyCategory, form, propertyCategory, propertyId]);

  // const selectedPropertyCategory =
  //   watch('propertyCategoryId') && propertyCategories
  //     ? propertyCategories.find(
  //         (item) => item.id === watch('propertyCategoryId'),
  //       )
  //     : undefined;
  console.log(`disabled ${!!property && !!property.propertyCategoryId}`);
  const disablePropertyCategorySelection =
    !!property && !!property.propertyCategoryId;
  return (
    <View className="h-[90%] w-full">
      <KeyboardAwareScrollView
        className="mb-10 px-5"
        showsVerticalScrollIndicator={false}
      >
        <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-700">
          Overview
        </Text>
        <Text className="mb-5 mt-1 font-airbnb_bk text-xs font-normal text-text-550">
          Provide an overview of your property
        </Text>

        <Controller
          name="propertyCategoryId"
          control={control}
          render={({ field: { value, onChange } }) => (
            <CustomPicker
              disabled={disablePropertyCategorySelection}
              items={
                propertyCategories?.map((item) => ({
                  label: item.name,
                  value: item.id,
                })) ?? []
              }
              value={value}
              onValueChange={
                disablePropertyCategorySelection ? () => {} : onChange
              }
              name="propertyCategoryId"
              label="Property Category"
            />
          )}
        />
        <Controller
          name="registeryFileKey"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <View className="mb-1">
              <Text
                className={cn(
                  'mb-2 font-airbnb_md text-base font-medium text-text-600',
                  error && 'text-danger-600',
                )}
              >
                Upload Registry Letter
              </Text>
              <Pressable onPress={() => setModalVisible(true)}>
                <View
                  className={cn(
                    'mt-0 flex-row items-center rounded-xl bg-primary-50',
                    error && 'border-danger-600',
                  )}
                >
                  <Text className="flex-1 px-5 py-3.5 font-airbnb_bk text-sm font-normal leading-5 text-text-600">
                    {selectedFile?.fileName || 'Choose File '}
                  </Text>
                </View>
              </Pressable>
              {error?.message && (
                <Text className="text-sm text-danger-400">
                  {error?.message}
                </Text>
              )}
            </View>
          )}
        />
        {isUploading ? (
          <ActivityIndicator size={20} color="#5f2800" />
        ) : (
          registeryFileKeyWatch && (
            <Pressable onPress={viewUploadedFile}>
              <Text className="mb-2 font-airbnb_md text-base font-medium text-text-600 ">
                View File
              </Text>
            </Pressable>
          )
        )}

        <ControlledInput
          control={control}
          name="propertyTitle"
          label="Property Title"
          placeholder=""
        />
        <Controller
          name="propertyFor"
          control={control}
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <CustomPicker
              items={[
                { label: 'Sale', value: 'SALE' },
                { label: 'Rent', value: 'RENT' },
              ]}
              error={error?.message}
              value={value}
              onValueChange={onChange}
              name="propertyFor"
              label="Property For"
            />
          )}
        />
        <Controller
          name="propertyTypeId"
          control={control}
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <CustomPicker
              items={
                propertyTypes?.map((item) => ({
                  label: item.name,
                  value: item.id,
                })) ?? []
              }
              error={error?.message}
              value={value}
              onValueChange={onChange}
              name="propertyTypeId"
              label="Property Type"
            />
          )}
        />

        {selectedPropertyCategory &&
          selectedPropertyCategory.showBedrooms !== 'HIDE' && (
            <View className="w-full flex-row items-center gap-5">
              <ControlledInput
                control={control}
                name="bedrooms"
                label="Bedrooms"
                keyboardType="number-pad"
                placeholder=""
              />
            </View>
          )}
        {selectedPropertyCategory &&
          selectedPropertyCategory.showBathrooms !== 'HIDE' && (
            <View className="w-full flex-row items-center gap-5">
              <ControlledInput
                control={control}
                name="bathrooms"
                label="Bathrooms"
                keyboardType="number-pad"
                placeholder=""
              />
            </View>
          )}

        {selectedPropertyCategory &&
          selectedPropertyCategory.showSecurityDeposit !== 'HIDE' &&
          propertyFor !== 'SALE' && (
            <ControlledInput
              control={control}
              name="securityDeposit"
              label="Security Deposit"
              keyboardType="number-pad"
              placeholder=""
            />
          )}

        <ControlledInput
          control={control}
          name="propertyPrice"
          label={`Property ${propertyFor === 'SALE' ? 'Price' : 'Rent'}`}
          placeholder=""
        />

        {selectedPropertyCategory &&
          selectedPropertyCategory.showAreaIn !== 'HIDE' && (
            <Controller
              name="areaUnitId"
              control={control}
              render={({
                field: { value, onChange },
                fieldState: { error },
              }) => (
                <CustomPicker
                  items={
                    areaUnits?.map((item) => ({
                      label: item.name,
                      value: item.id,
                    })) ?? []
                  }
                  error={error?.message}
                  value={value}
                  onValueChange={onChange}
                  name="areaIn"
                  label="Area In"
                />
              )}
            />
          )}

        {selectedPropertyCategory &&
          selectedPropertyCategory.showArea !== 'HIDE' && (
            <ControlledInput
              control={control}
              name="area"
              label="Area"
              keyboardType="number-pad"
              placeholder=""
            />
          )}

        {selectedPropertyCategory &&
          selectedPropertyCategory.showAboutProperty !== 'HIDE' && (
            <ControlledInput
              control={control}
              name="aboutProperty"
              label="About Property"
              placeholder=""
              multiline={true}
            />
          )}

        {/* modal */}
        <DocUploadModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          onChoosePhoto={pickImage}
          onChooseDoc={pickDocument}
          onRemoveSelected={removeDoc}
        />

        <View className="mb-32 " />
      </KeyboardAwareScrollView>

      <BlurButton
        loading={step1Mutation.isPending}
        onNext={handleSubmit(onSubmit, (err) =>
          console.log(`submittion error `, err),
        )}
        label={'Next'}
      />
    </View>
  );
};

export default Step1;
