import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { ActivityIndicator, Text, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Redirect, useLocalSearchParams } from 'expo-router';
import { BlurButtonWithBack } from '@/components/blur-button';
import { ControlledInput } from '@/components/post-property-component';
import { zodResolver } from '@hookform/resolvers/zod';
import CustomCheckBox from '../../app/upload-post/custom-checkbox';
import CustomPicker from './custom-picker';
import { api } from '@/utils/api';
import { 
  step3FormSchema, 
  FacingEnum,
  FurnishingEnum,
  PossessionStateEnum,
  PropertyStateEnum, 
} from '@/utils/form-validators';
import { showMessage } from 'react-native-flash-message';
import { z } from 'zod';

// eslint-disable-next-line max-lines-per-function
const Step3 = ({
  onNext,
  onPrevious,
}: {
  onNext: () => void;
  onPrevious: () => void;
}) => {
  const searchParams = useLocalSearchParams<{ propertyId: string }>();
  const { data: property, refetch } = api.postProperty.getStep3.useQuery(
    {
      id: searchParams.propertyId,
    },
    {
      enabled: !!searchParams.propertyId,
    },
  );
  const { data: amenities } = api.postProperty.getAmenities.useQuery();
  console.log('\n\n>>>>>>>>>property', property);
  console.log('\n\n>>>>>>>>>amenities', amenities);
  const nothingtoShow =
    property?.PropertyCategory?.showSocietyName === 'HIDE' &&
    property?.PropertyCategory?.showBuildYear === 'HIDE' &&
    property?.PropertyCategory?.showPossessionState === 'HIDE' &&
    property?.PropertyCategory?.showFurnishing === 'HIDE' &&
    property?.PropertyCategory?.showFacing === 'HIDE' &&
    property?.PropertyCategory?.showTotalFloors === 'HIDE' &&
    property?.PropertyCategory?.showFloorNumber === 'HIDE' &&
    property?.PropertyCategory?.showCarParking === 'HIDE' &&
    property?.PropertyCategory?.showPropertyState === 'HIDE';
  console.log('nothingtoShow', nothingtoShow);
  if (nothingtoShow) {
    onNext(); // Call onNext to move to the next step
    return null;
  }

  if (!property) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <Step3Form
      property={property}
      amenities={amenities ?? []}
      onNext={onNext}
      onPrevious={onPrevious}
      refetch={refetch}
    />
  );
};

const Step3Form = ({
  property,
  amenities,
  onNext,
  onPrevious,
  refetch,
}: {
  property: {
    amenities: { name: string; id: string }[];
    id: string;
    propertyCategoryId: string | null;
    societyOrLocalityName: string | null;
    buildYear: number | null;
    possessionState: PossessionStateEnum | null;
    furnishing: FurnishingEnum | null;
    totalFloors: number | null;
    floorNumber: number | null;
    carParking: number | null;
    facing: FacingEnum | null;
    propertyState: PropertyStateEnum | null;
    PropertyCategory: {
      id: string;
      name: string;
      showSocietyName: string;
      showBuildYear: string;
      showPossessionState: string;
      showAmenities: string;
      showFurnishing: string;
      showFacing: string;
      showTotalFloors: string;
      showFloorNumber: string;
      showCarParking: string;
      showPropertyState: string;
    } | null;
  };
  amenities: { id: string; name: string }[];
  onNext: () => void;
  onPrevious: () => void;
  refetch: () => void;
}) => {
  const step3Mutation = api.postProperty.step3.useMutation();
  const { data: selectedPropertyCategory } =
    api.postProperty.getSelectedPropertyCategory.useQuery(
      { categoryId: property.propertyCategoryId ?? '' },
      {
        enabled: !!property.propertyCategoryId,
      },
    );

  const form = useForm<z.infer<typeof step3FormSchema>>({
    resolver: zodResolver(step3FormSchema),
    defaultValues: {
      totalFloors: undefined,
      floorNumber: undefined,
    },
  });

  useEffect(() => {
    console.log('propertyyyyyyyyyyyyyyyyy', property);
    if (property && form) {
      form.setValue(
        'societyOrLocalityName',
        property.societyOrLocalityName ?? undefined,
      );
      form.setValue('buildYear', property.buildYear ?? 2024);
      form.setValue(
        'possessionState',
        property.possessionState ?? 'READY_TO_MOVE',
      );
      form.setValue('facing', property.facing ?? 'NORTH');
      form.setValue('furnishing', property.furnishing ?? 'RAW');
      form.setValue('totalFloors', property.totalFloors ?? 0);
      form.setValue('floorNumber', property.floorNumber ?? 0);
      form.setValue('carParking', property.carParking ?? 0);
      form.setValue('propertyState', property.propertyState ?? 'NEW');
      form.setValue(
        'amenities',
        property.amenities.map((a) => ({ label: a.name, value: a.id })),
      );
    }
  }, [property, form]);

  useEffect(() => {
    console.log('uuuuupppppppdating field', property.PropertyCategory);
    if (property.PropertyCategory?.showSocietyName === 'SHOW') {
      form.setValue('societyOrLocalityName', '');
    } else {
      form.setValue('societyOrLocalityName', undefined);
    }
    if (property.PropertyCategory?.showBuildYear === 'SHOW') {
      form.setValue('buildYear', 2025);
    } else {
      form.setValue('buildYear', undefined);
    }
    if (property.PropertyCategory?.showPossessionState === 'SHOW') {
      //   form.setValue("possessionState", "");
    } else {
      form.setValue('possessionState', undefined);
    }
    if (property.PropertyCategory?.showFurnishing === 'SHOW') {
      form.setValue('furnishing', undefined);
    } else {
      form.setValue('furnishing', undefined);
    }
    if (property.PropertyCategory?.showFacing === 'SHOW') {
      form.setValue('facing', undefined);
    } else {
      form.setValue('facing', undefined);
    }
    if (property.PropertyCategory?.showTotalFloors === 'SHOW') {
      form.setValue('totalFloors', 0);
    } else {
      form.setValue('totalFloors', undefined);
    }
    if (property.PropertyCategory?.showFloorNumber === 'SHOW') {
      form.setValue('floorNumber', 0);
    } else {
      form.setValue('floorNumber', undefined);
    }
    if (property.PropertyCategory?.showCarParking === 'SHOW') {
      form.setValue('carParking', 0);
    } else {
      form.setValue('carParking', undefined);
    }
    if (property.PropertyCategory?.showPropertyState === 'SHOW') {
      form.setValue('propertyState', undefined);
    } else {
      form.setValue('propertyState', undefined);
    }
  }, [selectedPropertyCategory, form]);

  const onSubmit = (values: z.infer<typeof step3FormSchema>) => {
    step3Mutation
      .mutateAsync({
        ...values,
        amenities: values.amenities.map((a) => ({
          id: a.value,
          name: a.label,
        })),
        id: property.id,
      })
      .then((resp) => {
        refetch();
        showMessage({
          message: resp.message,
          type: 'success',
        });

        onNext();
        console.log(values);
      })
      .catch((err) => {
        showMessage({
          message: err.message,
          type: 'danger',
        });
      });
  };
  return (
    <View className="h-[90%] w-full">
      <ScrollView
        className="mb-10 h-full px-5"
        showsVerticalScrollIndicator={false}
      >
        <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-700">
          Attributes
        </Text>
        <Text className="mb-5 mt-1 font-airbnb_bk text-xs font-normal text-text-550">
          Provide attributes of your property
        </Text>
        {property && property.PropertyCategory?.showSocietyName !== 'HIDE' && (
          <ControlledInput
            control={form.control}
            name="societyOrLocalityName"
            label="Society name"
            placeholder=""
            // required={property.PropertyCategory?.showSocietyName === 'SHOW'}
          />
        )}
        {property && property.PropertyCategory?.showBuildYear !== 'HIDE' && (
          <ControlledInput
            control={form.control}
            name="buildYear"
            label="Build Year"
            placeholder="e.g., 2025"
            keyboardType="number-pad"
            // required={property.PropertyCategory?.showBuildYear === 'SHOW'}
          />
        )}
        {property &&
          property.PropertyCategory?.showPossessionState !== 'HIDE' && (
            <Controller
              control={form.control}
              name="possessionState"
              render={({
                field: { value, onChange },
                fieldState: { error },
              }) => (
                <CustomPicker
                  items={[
                    { label: 'Ready To Move', value: 'READY_TO_MOVE' },
                    { label: 'Under 6 Months', value: 'UNDER_6_MONTHS' },
                    { label: 'Under 1 Year', value: 'UNDER_1_YEAR' },
                    { label: 'Below 3 Years', value: 'BELOW_3_YEARS' },
                  ]}
                  value={value ?? null}
                  onValueChange={onChange}
                  name="possessionState"
                  label="Possession State"
                  error={error?.message}
                  // required={
                  //   property.PropertyCategory?.showPossessionState === 'SHOW'
                  // }
                />
              )}
            />
          )}

        {property && property.PropertyCategory?.showAmenities !== 'HIDE' && (
          <Controller
            control={form.control}
            name="amenities"
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <CustomCheckBox
                items={
                  amenities?.map((a) => ({
                    label: a.name,
                    value: a.id,
                  })) ?? []
                }
                control={form.control}
                name="amenities"
                label="Amenities"
                value={value}
                onValueChange={onChange}
              />
            )}
          />
        )}

        {property && property.PropertyCategory?.showFurnishing !== 'HIDE' && (
          <Controller
            control={form.control}
            name="furnishing"
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <CustomPicker
                items={[
                  { label: 'Raw', value: 'RAW' },
                  { label: 'Semi-Furnished', value: 'SEMIFURNISHED' },
                  { label: 'Fully-Furnished', value: 'FULLYFURNISHED' },
                ]}
                value={value ?? null}
                onValueChange={onChange}
                name="furnishing"
                label="Furnishing"
                // required={property.PropertyCategory?.showFurnishing === 'SHOW'}
              />
            )}
          />
        )}
        {property && property.PropertyCategory?.showFacing !== 'HIDE' && (
          <Controller
            control={form.control}
            name="facing"
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <CustomPicker
                items={[
                  { label: 'North', value: 'NORTH' },
                  { label: 'South', value: 'SOUTH' },
                  { label: 'East', value: 'EAST' },
                  { label: 'West', value: 'WEST' },
                  { label: 'North East', value: 'NORTH_EAST' },
                  { label: 'North West', value: 'NORTH_WEST' },
                  { label: 'South East', value: 'SOUTH_EAST' },
                  { label: 'South West', value: 'SOUTH_WEST' },
                ]}
                value={value ?? null}
                onValueChange={onChange}
                name="facing"
                label="Facing"
                // required={property.PropertyCategory?.showFacing === 'SHOW'}
              />
            )}
          />
        )}
        {property && property.PropertyCategory?.showTotalFloors !== 'HIDE' && (
          <ControlledInput
            control={form.control}
            name="totalFloors"
            label="Total Floors"
            keyboardType="numeric"
            placeholder="e.g., 2"
            // required={property.PropertyCategory?.showTotalFloors === 'SHOW'}
          />
        )}
        {property && property.PropertyCategory?.showFloorNumber !== 'HIDE' && (
          <ControlledInput
            control={form.control}
            name="floorNumber"
            label="Floor Number"
            keyboardType="numeric"
            placeholder="e.g., 2"
            // required={property.PropertyCategory?.showFloorNumber === 'SHOW'}
          />
        )}
        {property && property.PropertyCategory?.showCarParking !== 'HIDE' && (
          <ControlledInput
            control={form.control}
            name="carParking"
            label="Car Parking"
            keyboardType="numeric"
            placeholder="e.g., 2"
            // required={property.PropertyCategory?.showCarParking === 'SHOW'}
          />
        )}
        {property &&
          property.PropertyCategory?.showPropertyState !== 'HIDE' && (
            <Controller
              control={form.control}
              name="propertyState"
              render={({
                field: { value, onChange },
                fieldState: { error },
              }) => (
                <CustomPicker
                  items={[
                    { label: 'New', value: 'NEW' },
                    { label: 'Resale', value: 'RESALE' },
                    { label: 'Upcoming', value: 'UPCOMING' },
                  ]}
                  name="propertyState"
                  label="Property Is"
                  value={value ?? null}
                  onValueChange={onChange}
                  // required={
                  //   property.PropertyCategory?.showPropertyState === 'SHOW'
                  // }
                />
              )}
            />
          )}

        <View className="mb-28 " />
      </ScrollView>

      <BlurButtonWithBack
        loading={step3Mutation.isPending}
        onPrevious={onPrevious}
        onNext={form.handleSubmit(onSubmit, (err) => {
          console.log('error step 3', JSON.stringify(err));
          console.log('error step 3', form.getValues());
          showMessage({
            message: 'Please check the form',
            type: 'danger',
          });
        })}
        label={'Next'}
      />
    </View>
  );
};

export default Step3;
