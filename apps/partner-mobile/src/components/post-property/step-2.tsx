import { Image as NImage } from 'expo-image';
import { useLocalSearchParams } from 'expo-router';
import React, { useState, useRef, useEffect } from 'react';
import Entypo from '@expo/vector-icons/Entypo';
import { BlurView } from 'expo-blur';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import {
  Dimensions,
  StyleSheet,
  Platform,
  Pressable,
  SafeAreaView,
  Text,
  View,
  Modal,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native-gesture-handler';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { BlurButtonWithBack } from '@/components/blur-button';
import { ControlledInput } from '@/components/post-property-component';
import { Button, cn } from '@/ui';
import { api } from '@/utils/api';
import { step2FormSchema } from '@/utils/form-validators';
import { showMessage } from 'react-native-flash-message';
import MapView, { Marker, Polygon } from 'react-native-maps';
import {
  GooglePlacesAutocomplete,
  Query,
} from 'react-native-google-places-autocomplete';
import { iosMapApiKey, androidMapApiKey } from '@/utils/google-api-keys';
import TextInputGoogleAutoComplete from '@/components/text-input-google-autocomplete';

const { height, width } = Dimensions.get('screen');
const query: Query = {
  key: Platform.OS === 'ios' ? iosMapApiKey : androidMapApiKey,
  language: 'en',
  components: 'country:in',
};

type FormValues = z.infer<typeof step2FormSchema>;

// eslint-disable-next-line max-lines-per-function
const Step2 = ({
  onNext,
  onPrevious,
}: {
  onNext: () => void;
  onPrevious: () => void;
}) => {
  const insets = useSafeAreaInsets();
  const searchParams = useLocalSearchParams<{ propertyId: string }>();
  const [utilities, setUtilities] = useState([{ id: 1 }]);
  const mapRef = useRef<MapView>(null);
  const initialRegion = {
    latitude: 28.7041,
    longitude: 77.1025,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };
  const [showPinLocationModal, setShowPinLocationModal] =
    useState<boolean>(false);
  const [drawingPoints, setDrawingPoints] = useState<
    { lat: number; lng: number }[]
  >([]);
  const [tempDrawingPoints, setTempDrawingPoints] = useState<
    { lat: number; lng: number }[]
  >([]);
  const { data: property, refetch } = api.postProperty.getStep2.useQuery(
    {
      id: searchParams.propertyId,
    },
    {
      enabled: !!searchParams.propertyId,
    },
  );
  const step2Mutation = api.postProperty.step2.useMutation();

  const form = useForm<FormValues>({
    resolver: zodResolver(step2FormSchema),
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'utilities',
  });

  useEffect(() => {
    console.log('property', property);
    if (property) {
      form.setValue('propertyAddress', property.propertyAddress ?? '');
      form.setValue('propertyLatitude', Number(property.propertyLatitude));
      form.setValue('propertyLongitude', Number(property.propertyLongitude));
      form.setValue(
        'propertyGooglePlaceId',
        property.propertyGooglePlaceId ?? '',
      );
      form.setValue(
        'propertyAddressComponents',
        property.propertyAddressComponents,
      );
      form.setValue('propertyLocation', property.propertyLocation ?? '');
      form.setValue(
        'utilities',
        property.utilities.map((u) => ({
          utility: u.utility,
          distanceInKm: u.distanceInKm.toString() ?? '',
        })),
      );

      const latLngs = (property.propertyMarkersLatLng ?? []) as {
        lat: number;
        lng: number;
      }[];

      if (latLngs.length > 0) {
        setDrawingPoints(latLngs);
      }
    }
  }, [property]);

  useEffect(() => {
    if (drawingPoints.length !== 0) {
      form.setValue('propertyMarkersLatLng', drawingPoints);
    }
  }, [drawingPoints, form]);

  const onSubmit = (values: z.infer<typeof step2FormSchema>) => {
    step2Mutation
      .mutateAsync({ ...values, id: searchParams.propertyId })
      .then((resp) => {
        refetch();
        showMessage({
          message: resp.messageTitle,
          type: 'success',
        });
        onNext();
      })
      .catch((err) => {
        showMessage({
          message: err.message,
          type: 'danger',
        });
      });
  };

  const handleMapPress = (event: any) => {
    const { coordinate } = event.nativeEvent;
    // const currentDate = new Date();
    // const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}_${String(currentDate.getHours()).padStart(2, '0')}:${String(currentDate.getMinutes()).padStart(2, '0')}:${String(currentDate.getSeconds()).padStart(2, '0')}`;

    setTempDrawingPoints((prev) => [
      ...prev,
      {
        lat: coordinate.latitude,
        lng: coordinate.longitude,
      },
    ]);
  };

  const clearPolygon = () => {
    setTempDrawingPoints([]);
  };

  const saveLocations = () => {
    setDrawingPoints(tempDrawingPoints);
    setShowPinLocationModal(false);
  };

  const ReusableGooglePlacesAutocomplete = (
    <GooglePlacesAutocomplete
      placeholder="Search"
      fetchDetails={true}
      onFail={(error) => {
        showMessage({
          message: error.message,
          type: 'danger',
        });
      }}
      onPress={(data, details = null) => {
        if (details && details.geometry?.location) {
          const { lat, lng } = details.geometry.location;
          console.log(lat, lng);
          mapRef.current?.animateToRegion({
            latitude: lat,
            longitude: lng,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          });
        } else {
          showMessage({
            message: 'No location found',
            type: 'danger',
          });
        }
      }}
      query={query}
      styles={{
        textInputContainer: {
          backgroundColor: 'white',
          borderColor: '#F04D24',
          borderWidth: 1,
          borderRadius: 4,
          marginHorizontal: 20,
        },
        textInput: {
          cursorCGolor: '#F04D24',
        },
        row: {
          flexDirection: 'row',
          // position: 'absolute',
          backgroundColor: 'none',
        },
        description: {
          // // flexDirection: 'row',
          // flex: 1,
          // // width: '100%',
          // flexWrap: 'wrap',
          // backgroundColor: 'red',
        },
      }}
    />
  );

  const onAddressComponentsChange = (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => {
    // if (!setValue) {
    //   return;
    // }
    form.setValue('propertyAddress', data.address);
    form.setValue('propertyLatitude', Number(data.lat));
    form.setValue('propertyLongitude', Number(data.lng));
    form.setValue('propertyGooglePlaceId', data.place_id);
    form.setValue('propertyAddressComponents', data.address_components);
  };

  return (
    <>
      <SafeAreaView className="flex-1 w-full">
        <ScrollView
          className="mb-10 h-full px-5"
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-row items-center justify-between">
            <View>
              <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-700">
                Location
              </Text>
              <Text className="mb-5 mt-1 font-airbnb_bk text-xs font-normal text-text-550">
                Provide location details of your property
              </Text>
            </View>

            <Pressable onPress={() => setShowPinLocationModal(true)}>
              <NImage
                source={require('../../../assets/icons/map.png')}
                className="h-6 w-6 self-end"
              />
              <Text className="mb-5 mt-1 font-airbnb_bk text-xs font-normal text-primary-750">
                Pin property on map
              </Text>
            </Pressable>
          </View>
          {property &&
            property.PropertyCategory?.showPropertyAddress !== 'HIDE' && (
              <Controller
                control={form.control}
                name="propertyAddress"
                render={({ field, fieldState: { error } }) => (
                  <TextInputGoogleAutoComplete
                    onAddressComponentsChange={onAddressComponentsChange}
                    // northMaxLat={city?.northMaxLat}
                    // southMaxLat={city?.southMaxLat}
                    // westMaxLng={city?.westMaxLng}
                    // eastMaxLng={city?.eastMaxLng}
                    value={field.value}
                  >
                    <View className="mb-4">
                      <Text
                        className={cn(
                          'mb-2 font-airbnb_md text-base font-medium text-text-600 ',
                          error && 'text-danger-600',
                        )}
                      >
                        Property Address
                      </Text>
                      <View
                        className={cn(
                          'mt-0 flex-row items-center rounded-xl border border-[#ECE9E8] bg-primary-50',
                          error && 'border-danger-600',
                        )}
                      >
                        <Text className="flex-1 px-5 py-3.5 font-airbnb_bk text-sm font-normal leading-5 text-text-600">
                          {field.value || ' '}
                        </Text>
                      </View>
                      {error?.message && (
                        <Text className="text-sm text-danger-400">
                          {error?.message}
                        </Text>
                      )}
                    </View>
                  </TextInputGoogleAutoComplete>
                )}
              />
            )}
          {property &&
            property.PropertyCategory?.showPropertyLocation !== 'HIDE' && (
              <ControlledInput
                control={form.control}
                name="propertyLocation"
                label="Flat, House no., Building, Company, Apartment"
                // placeholder="Enter"
              />
            )}

          {property && property.PropertyCategory?.showUtilities !== 'HIDE' && (
            <>
              <View className="w-full flex-row items-center gap-5">
                <Text className="mb-2 font-airbnb_md text-base font-medium text-text-600">
                  Add Utilities
                </Text>
              </View>

              {fields.map((field, index) => (
                <View
                  key={index + 1}
                  className="w-full flex-row items-center gap-5"
                >
                  <ControlledInput
                    control={form.control}
                    name={`utilities.${index}.utility`}
                    placeholder="Utility name"
                    width="flex-1"
                  />
                  <ControlledInput
                    control={form.control}
                    name={`utilities.${index}.distanceInKm`}
                    placeholder="Distance (in Km)"
                    keyboardType="number-pad"
                    width="flex-1"
                  />

                  <Pressable onPress={() => remove(index)} className="">
                    <NImage
                      source={require('../../../assets/icons/remove.png')}
                      className="h-6 w-6"
                    />
                  </Pressable>
                </View>
              ))}

              <View>
                <Button
                  variant="onboarding"
                  label="Add Utilities"
                  onPress={() => append({ utility: '', distanceInKm: '0' })}
                  className="bg-primary-300"
                  textClassName="text-secondary-850"
                />
              </View>
            </>
          )}

          <View className="mb-32 " />
        </ScrollView>
        <BlurButtonWithBack
          onPrevious={onPrevious}
          loading={step2Mutation.isPending}
          onNext={form.handleSubmit(onSubmit, (err) => {
            console.log(err);
            if (err.propertyAddress?.message || err.propertyLocation?.message) {
              showMessage({
                message: 'Please check the form',
                type: 'danger',
              });
              return;
            }
            if (err.propertyMarkersLatLng?.message) {
              showMessage({
                message: 'Please add property markers',
                type: 'danger',
              });
              return;
            }
            if (err.utilities?.root?.message) {
              showMessage({
                message: 'Please add atleast 2 utilities',
                type: 'danger',
              });
              return;
            }
            if (err) {
            }
          })}
          label={'Next'}
        />
      </SafeAreaView>
      <Modal
        visible={showPinLocationModal}
        animationType="slide"
        transparent={true}
      >
        <View className="flex-1 w-full relative">
          {/* Top search bar */}
          <View className="absolute top-0 left-0 right-0 z-50">
            {Platform.OS === 'ios' ? (
              <BlurView
                intensity={10}
                style={{ ...styles.blurContainer, paddingTop: insets.top }}
              >
                <View className="flex-row px-5 py-3">
                  <Pressable onPress={() => setShowPinLocationModal(false)}>
                    <Entypo
                      name="chevron-thin-left"
                      size={18}
                      color="#451F0A"
                      className="pr-3 mt-5"
                    />
                  </Pressable>
                  <View className="flex-1">
                    {ReusableGooglePlacesAutocomplete}
                  </View>
                </View>
              </BlurView>
            ) : (
              <View className="flex-row items-center bg-[#F4F0EE80] px-5 py-3">
                <Pressable onPress={() => setShowPinLocationModal(false)}>
                  <Entypo
                    name="chevron-thin-left"
                    size={18}
                    color="#451F0A"
                    className="pr-3"
                  />
                </Pressable>
                <View className="flex-1">
                  {ReusableGooglePlacesAutocomplete}
                </View>
              </View>
            )}
          </View>

          {/* Map View */}
          <View className="items-center justify-center flex-1">
            <MapView
              ref={mapRef}
              style={{ flex: 1, width: width * 1 }}
              userInterfaceStyle="light"
              initialRegion={initialRegion}
              onPress={handleMapPress}
            >
              {/* Draw markers for selected locations */}
              {tempDrawingPoints.map((location, index) => (
                <Marker
                  key={index}
                  coordinate={{
                    latitude: location.lat,
                    longitude: location.lng,
                  }}
                  draggable={true}
                  onDragStart={(e) => {
                    console.log(
                      'onDragStart',
                      e.nativeEvent.coordinate.latitude,
                      e.nativeEvent.coordinate.longitude,
                    );
                  }}
                  onDragEnd={(e) => {
                    console.log(
                      'onDragEnd',
                      e.nativeEvent.coordinate.latitude,
                      e.nativeEvent.coordinate.longitude,
                    );
                    e.nativeEvent.coordinate;
                    tempDrawingPoints.splice(index, 1, {
                      lat: e.nativeEvent.coordinate.latitude,
                      lng: e.nativeEvent.coordinate.longitude,
                    });
                    setTempDrawingPoints([...tempDrawingPoints]);
                  }}
                >
                  {tempDrawingPoints.length === 1 ? (
                    <View style={styles.singleMarkerContainer}>
                      <Entypo name="location-pin" size={32} color="#dc3545" />
                    </View>
                  ) : (
                    <View style={styles.markerContainer}>
                      <Text style={styles.markerText}>{index + 1}</Text>
                    </View>
                  )}
                </Marker>
              ))}

              {/* Draw polygon if there are at least 3 points */}
              {tempDrawingPoints.length >= 3 && (
                <Polygon
                  coordinates={tempDrawingPoints.map((point) => ({
                    latitude: point.lat,
                    longitude: point.lng,
                  }))}
                  strokeWidth={2}
                  strokeColor="#000"
                  fillColor="rgba(255,0,0,0.2)"
                />
              )}
            </MapView>
          </View>

          {/* Bottom buttons */}
          <View>
            {Platform.OS === 'ios' ? (
              <BlurView
                intensity={15}
                tint="light"
                blurReductionFactor={4}
                experimentalBlurMethod="dimezisBlurView"
                className="absolute bottom-14 w-full flex-col justify-end bg-primary-50/30 px-5 py-3 blur-lg"
              >
                <View className="flex-row items-center justify-between">
                  <Pressable
                    className="rounded-xl bg-secondary-100 px-6 py-3.5"
                    onPress={() => {
                      clearPolygon();
                      setShowPinLocationModal(false);
                    }}
                  >
                    <Text className="font-airbnb_md text-base font-medium text-[#451F0A]">
                      Reset
                    </Text>
                  </Pressable>
                  <Button
                    disabled={tempDrawingPoints.length < 3}
                    label="Save Area"
                    className="w-1/2"
                    onPress={saveLocations}
                  />
                </View>
              </BlurView>
            ) : (
              <View className="absolute bottom-14 mb-10 w-full flex-col justify-end bg-primary-50/30 px-5 py-3 blur-lg">
                <View className="flex-row items-center justify-between">
                  <Pressable
                    className="rounded-xl bg-[#E9E2DD] px-6 py-3.5"
                    onPress={() => {
                      clearPolygon();
                      setShowPinLocationModal(false);
                    }}
                  >
                    <Text className="font-airbnb_md text-base font-medium text-primary-400">
                      Reset
                    </Text>
                  </Pressable>
                  <Button
                    disabled={tempDrawingPoints.length < 3}
                    label="Save Area"
                    className="w-1/2"
                    onPress={saveLocations}
                  />
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  blurContainer: {
    backgroundColor: 'rgba(244, 240, 238, 0.5)',
  },
  markerContainer: {
    backgroundColor: '#dc3545',
    padding: 5,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#000',
  },
  singleMarkerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default Step2;
