import Entypo from '@expo/vector-icons/Entypo';
import { Picker } from '@react-native-picker/picker';
import React, { useState } from 'react';
import { type Control, Controller } from 'react-hook-form';
import { Modal, Pressable, Text, View } from 'react-native';

import { Button, cn } from '@/ui';

interface PickerModalProps {
  visible: boolean;
  items: { label: string; value: string }[];
  selectedValue: string | null;
  onValueChange: (value: string) => void;
  onClose: () => void;
}

export const PickerModal: React.FC<PickerModalProps> = ({
  visible,
  items,
  selectedValue,
  onValueChange,
  onClose,
}) => {
  return (
    <Modal transparent={true} animationType="fade" visible={visible}>
      <View className="flex-1 justify-center bg-black/50">
        <View className="rounded-lg bg-white p-5">
          <Picker
            selectedValue={selectedValue || ''} //fallbck for khali string
            onValueChange={(itemValue) => onValueChange(itemValue)}
            className="h-40 w-full"
          >
            {/* Default Select option */}
            <Picker.Item label="Select an option" value="" />
            {items.map((item) => (
              <Picker.Item
                key={item.value}
                label={item.label}
                value={item.value}
              />
            ))}
          </Picker>
          <Button label="Done" onPress={onClose} />
        </View>
      </View>
    </Modal>
  );
};

interface Item {
  label: string;
  value: string;
}

interface CustomPickerProps {
  items: Item[];
  value: string | null;
  onValueChange: (value: string) => void;
  name: string;
  label?: string;
  error?: string;
  disabled?: boolean;
}

const CustomPicker: React.FC<CustomPickerProps> = ({
  items,
  value,
  onValueChange,
  name,
  error,
  label,
  disabled,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const valueText = items.find((item) => item.value === value)?.label;

  return (
    <View className={`mb-4 flex-1 ${disabled ? 'opacity-50' : ''}`}>
      {label && (
        <Text
          className={cn(
            'mb-2 font-airbnb_md text-base font-medium text-text-600',
            error && 'text-danger-600',
          )}
        >
          {label}
        </Text>
      )}
      <Pressable
        onPress={() => setModalVisible(true)}
        disabled={disabled}
        className="flex-row items-center justify-between rounded-xl border border-[#ECE9E8] bg-primary-50 px-5 py-3.5"
      >
        <Text className="font-airbnb_bk text-sm font-normal text-text-600">
          {valueText || 'Select an option'}
        </Text>
        <Entypo name="chevron-thin-down" size={12} color="'#78523D" />
      </Pressable>
      <PickerModal
        visible={modalVisible}
        items={items}
        selectedValue={value}
        onValueChange={(value) => {
          onValueChange(value);
        }}
        onClose={() => setModalVisible(false)}
      />
      {error && <Text className="text-sm text-danger-400">{error}</Text>}
    </View>
  );
};

export default CustomPicker;
