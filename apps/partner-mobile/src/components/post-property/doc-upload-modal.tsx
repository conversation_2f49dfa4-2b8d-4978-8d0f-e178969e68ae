import { Image as NImage } from 'expo-image';
import React from 'react';
import { Modal, Pressable, StyleSheet, Text, View } from 'react-native';

interface ModalOptionProps {
  onPress: () => void;
  icon: any;
  label: string;
}

interface DocUploadModalProps {
  visible: boolean;
  onClose: () => void;
  onChoosePhoto: () => void;
  onChooseDoc: () => void;
  onRemoveSelected: () => void;
}

const ModalOption: React.FC<ModalOptionProps> = ({ onPress, icon, label }) => (
  <Pressable onPress={onPress} className="flex-row items-center py-3">
    <NImage source={icon} className="mr-5 h-6 w-6" />
    <Text className="font-airbnb_md text-base font-medium text-text-500">
      {label}
    </Text>
  </Pressable>
);

const DocUploadModal: React.FC<DocUploadModalProps> = ({
  visible,
  onClose,
  onChoosePhoto,
  onChooseDoc,
  onRemoveSelected,
}) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View
          className="rounded-2xl bg-[#FDFDFD] px-5 py-6"
          style={{ backgroundColor: '#FDFDFD' }}
        >
          <Text className="font-airbnb_bd text-xl font-bold text-secondary-500">
            Upload Document or Photo
          </Text>

          <ModalOption
            onPress={onChoosePhoto}
            icon={require('../../../assets/icons/gallery.png')}
            label="Choose Photo"
          />
          <ModalOption
            onPress={onChooseDoc}
            icon={require('../../../assets/icons/docs.png')}
            label="Choose Document"
          />
          <ModalOption
            onPress={onRemoveSelected}
            icon={require('../../../assets/icons/bin1.png')}
            label="Remove Selected Media"
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
});

export default DocUploadModal;
