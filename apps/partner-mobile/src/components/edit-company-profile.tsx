import React, { useEffect } from 'react';
import {
  Submit<PERSON><PERSON><PERSON>,
  FieldErrors,
  Control,
  Controller,
  UseFormSetValue,
} from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import * as z from 'zod';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';

import { Button, cn, ControlledInput, ScrollView, Text, View } from '@/ui';
import { companyDetailsSchema } from '@/utils/form-validators';
import TextInputGoogleAutoComplete from '@/components/text-input-google-autocomplete';

export type EditCompanyProfileProps = {
  control: Control<z.infer<typeof companyDetailsSchema>>;
  setValue?: UseFormSetValue<z.infer<typeof companyDetailsSchema>>;
  onSubmit?: SubmitHandler<z.infer<typeof companyDetailsSchema>>;
  label?: boolean;
};

const EditCompanyProfile = ({
  label,
  control,
  setValue,
}: EditCompanyProfileProps) => {
  const { data: company } = api.company.getCompany.useQuery();
  const { data: profile } = api.user.profile.useQuery();

  const onError = (
    error: FieldErrors<z.infer<typeof companyDetailsSchema>>,
  ) => {
    console.log('Error', error);
    showMessage({
      message: 'Please check form fields',
      type: 'danger',
    });
  };

  const onAddressComponentsChange = (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => {
    if (!setValue) {
      return;
    }
    setValue('latitude', data.lat);
    setValue('longitude', data.lng);
    setValue('address', data.address);
  };

  const city = profile?.city;

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      keyboardVerticalOffset={10}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        // contentContainerStyle={{ flex: 1 }}
        className="flex-1"
      >
        <View className={`px-5 ${label === true ? '' : 'pb-36 mt-5'}`}>
          <ControlledInput
            testID="company-name"
            control={control}
            name="name"
            label="Company Name"
            placeholder="Enter your company name here"
          />

          <ControlledInput
            testID="email-input"
            control={control}
            name="email"
            label="Email Id"
            keyboardType="email-address"
            autoCapitalize="none"
            placeholder="Enter your email here"
          />

          <ControlledInput
            testID="phone-number"
            control={control}
            keyboardType="numeric"
            name="phoneNumber"
            label="Phone Number"
            placeholder="Enter your phone number here"
          />

          <Controller
            name="address"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <TextInputGoogleAutoComplete
                onAddressComponentsChange={onAddressComponentsChange}
                northMaxLat={city?.northMaxLat}
                southMaxLat={city?.southMaxLat}
                westMaxLng={city?.westMaxLng}
                eastMaxLng={city?.eastMaxLng}
                value={field.value}
              >
                <View className="mb-4">
                  <Text
                    className={cn(
                      'mb-2 font-airbnb_md text-base font-medium text-text-600',
                      error && 'text-danger-600',
                    )}
                  >
                    Address
                  </Text>
                  <View
                    className={cn(
                      'mt-0 flex-row items-center rounded-xl border border-[#ECE9E8] ',
                      error && 'border-danger-600',
                    )}
                  >
                    <Text className="flex-1 px-5 py-3.5 font-airbnb_bk text-sm font-normal leading-5 text-text-600">
                      {field.value || ' '}
                    </Text>
                  </View>
                  {error?.message && (
                    <Text className="text-sm text-danger-400">
                      {error?.message}
                    </Text>
                  )}
                </View>
              </TextInputGoogleAutoComplete>
            )}
          />
          {/*<ControlledInput*/}
          {/*  testID="company-location"*/}
          {/*  control={control}*/}
          {/*  keyboardType="default"*/}
          {/*  name="address"*/}
          {/*  label="Company Location"*/}
          {/*  placeholder="Enter your company location here"*/}
          {/*/>*/}

          <ControlledInput
            testID="Website-link"
            control={control}
            keyboardType="url"
            name="website"
            label="Website Link"
            // transformToUppercase={true}
            autoCapitalize="none"
            placeholder="Enter your website link here"
          />

          <ControlledInput
            testID="fax"
            control={control}
            keyboardType="default"
            name="fax"
            label="Fax"
            placeholder="Enter your fax number here"
          />

          <ControlledInput
            testID="about-company"
            control={control}
            keyboardType="default"
            name="about"
            label="About Company"
            numberOfLines={4}
            multiline={true}
            className="h-24 px-5"
            placeholder="Enter a description about your company"
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default EditCompanyProfile;
