import React, { useState } from 'react';
import { Text, View } from 'react-native';
import { Pressable } from '@/ui';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';

type MultiSliderProps = {
  label: string;
  selectedMinValue: number;
  selectedMaxValue: number;
  minimumValue: number;
  maximumValue: number;
  step?: number;
  onChange: (min: number, max: number) => void;
};

const CustomSliderMarkerLeft = ({ currentValue }: { currentValue: number }) => {
  return (
    <View
      style={{
        padding: 7,
        backgroundColor: 'white',
        borderRadius: 50,
        shadowColor: '#000',
        shadowOffset: { width: 4, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 7,
        elevation: 7,
        borderWidth: 1,
        borderColor: '#ddd',
      }}
    >
      <Text
        style={{ color: 'black', fontSize: 16, fontWeight: '500' }}
        className="font-airbnb_md"
      >
        {/*{currentValue}*/}
      </Text>
    </View>
  );
};

const CustomSliderMarkerRight = ({
  currentValue,
}: {
  currentValue: number;
}) => {
  return (
    <View
      style={{
        padding: 7,
        backgroundColor: 'white',
        borderRadius: 50,
        shadowColor: '#000',
        shadowOffset: { width: 4, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 7,
        elevation: 7,
        borderWidth: 1,
        borderColor: '#ddd',
      }}
    >
      <Text
        style={{ color: 'black', fontSize: 16, fontWeight: '500' }}
        className="font-airbnb_md"
      >
        {/*{currentValue}*/}
      </Text>
    </View>
  );
};

const MultiSliderComponent: React.FC<MultiSliderProps> = ({
  label,
  selectedMinValue,
  selectedMaxValue,
  minimumValue,
  maximumValue,
  step,
  onChange,
}) => {
  const [isShown, setIsShown] = useState(true);
  const handleShown = () => {
    setIsShown((prevState) => !prevState);
  };

  return (
    <View className="py-4 px-5 border border-primary-300 rounded-2xl">
      {/* label part */}
      <View className="flex-row items-center justify-between">
        <Text
          className="font-airbnb_md font-medium text-lg text-text-600"
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{ maxWidth: '80%' }}
        >
          {label}
        </Text>
        <Pressable onPress={handleShown}>
          <FontAwesome6
            name={isShown ? 'chevron-down' : 'chevron-up'}
            size={20}
            color="#5F2800"
          />
        </Pressable>
      </View>

      {/* slider */}
      {isShown && (
        <>
          <View className="flex-row items-center justify-between">
            <Text className="font-airbnb_bk text-sm text-text-600">
              {selectedMinValue}
            </Text>

            <Text className="font-airbnb_bk text-sm text-text-600">
              {selectedMaxValue}
            </Text>
          </View>
          <View className="flex-1 items-center justify-center ">
            <MultiSlider
              isMarkersSeparated={true}
              min={minimumValue}
              max={maximumValue}
              step={step ?? 1}
              values={[20, 80]}
              trackStyle={{ height: 4 }}
              selectedStyle={{ height: 6, backgroundColor: '#C58E00' }}
              customMarkerLeft={(e) => (
                <CustomSliderMarkerLeft currentValue={e.currentValue} />
              )}
              customMarkerRight={(e) => (
                <CustomSliderMarkerRight currentValue={e.currentValue} />
              )}
            />
          </View>
          <View className="flex-row items-center justify-between">
            <Text className="font-airbnb_bk text-sm text-text-600">
              {minimumValue}
            </Text>

            <Text className="font-airbnb_bk text-sm text-text-600">
              {maximumValue}
            </Text>
          </View>
        </>
      )}
    </View>
  );
};

export default MultiSliderComponent;
