import { Pressable, Text, View } from 'react-native';
import UserPic from '@/components/user-pic';
import { Image } from 'expo-image';
import React from 'react';

const AgentHorizontalCard = ({
  agent,
  handlePress,
}: {
  agent: {
    id: string;
    name: string;
    companyDetails: {
      id: string;
      companyName: string;
    } | null;
    propertiesSold: number | null;
    properties?: any[];
    experience: string | null;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    rating: string | null;
    receivedConnectionRequests?: {
      id: string;
    }[];
    sentConnectionRequests?: {
      id: string;
    }[];
  };
  handlePress?: () => void;
}) => {
  return (
    <Pressable
      onPress={handlePress}
      className="mb-2.5 mt-4 rounded-[10px] border border-[#E9E2DD] bg-primary-50 px-2.5 py-3"
    >
      <View className="border-b border-[#E9E2DD]">
        {/* top part */}
        <View className="mb-2 flex-row items-center justify-between">
          {/* image and name part */}
          <View className="flex-row items-center">
            <UserPic
              picUrl={agent.cloudinaryProfileImageUrl ?? agent.filePublicUrl}
              size={42}
              color="#784100"
              className="h-12 w-12 rounded-full"
            />
            {/*<NImage*/}
            {/*  source={*/}
            {/*    item.sender.filePublicUrl*/}
            {/*      ? { uri: item.sender.filePublicUrl }*/}
            {/*      : require('../../assets/images/profile7.png')*/}
            {/*  }*/}
            {/*  className="h-12 w-12"*/}
            {/*/>*/}
            <View className="ml-1.5">
              <Text className="font-airbnb_xbd text-lg font-extrabold text-primary-750">
                {agent.name}
              </Text>
              <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                {agent.companyDetails?.companyName ?? ' '}
              </Text>
            </View>
          </View>

          {/* rating, or experience and active part */}
          <View>
            <View className="mb-2 flex-row items-center self-end">
              <Image
                source={require('../../assets/icons/star.png')}
                className="h-4 w-4"
              />
              <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-primary-750">
                {agent.rating || 0}
              </Text>
            </View>

            {agent?.properties && (
              <View className="flex-row items-center self-start rounded-md bg-primary-100 px-2.5 py-0.5">
                <Text className="font-airbnb_bk text-[10px] font-normal text-primary-750">
                  Active Properties:
                </Text>
                <Text className="font-airbnb_bd text-[10px] font-bold text-primary-750">
                  {' '}
                  {agent.properties.length}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* experience and work record */}
      <View className="flex-row items-center justify-between py-2.5">
        <View className="flex-row items-center">
          <Image
            source={require('../../assets/icons/sold.png')}
            className="mr-2 h-5 w-5"
            tintColor={'#784100'}
          />
          <Text className="font-airbnb_bk text-[11px] font-normal text-text-main700">
            {agent.propertiesSold || 0} Properties Sold
          </Text>
        </View>

        <View className="flex-row items-center">
          <Image
            source={require('../../assets/icons/experience.png')}
            className="mr-2 h-5 w-5"
            tintColor={'#784100'}
          />
          <Text className="font-airbnb_bk text-[11px] font-normal text-text-main700">
            {agent.experience || 0}yrs Experience
          </Text>
        </View>
      </View>

      {/* Buttons */}
      <View className="flex-row items-center justify-between">
        {/*{onChatPress && (*/}
        {/*  <Button*/}
        {/*    label="Chat"*/}
        {/*    backgroundColor={'bg-secondary-main700'}*/}
        {/*    textColor={'text-white'}*/}
        {/*    onPress={onChatPress}*/}
        {/*  />*/}
        {/*)}*/}
        {/*{onViewProfilePress && (*/}
        {/*  <Button*/}
        {/*    label="View Profile"*/}
        {/*    backgroundColor={'bg-secondary-100'}*/}
        {/*    textColor={'text-secondary-main700'}*/}
        {/*    onPress={onViewProfilePress}*/}
        {/*  />*/}
        {/*)}*/}
      </View>
    </Pressable>
  );
};

export default AgentHorizontalCard;
