import React from 'react';

import { cleanup, fireEvent, render, screen, waitFor } from '@/core/test-utils';

import type { SignUpFormProps } from './sign-up-form';
import { SignUpForm } from './sign-up-form';

afterEach(cleanup);

const onSubmitMock: jest.Mock<SignUpFormProps['onSubmit']> = jest.fn();

describe('SignUpForm Form', () => {
  it('renders correctly', async () => {
    render(<SignUpForm />);
    expect(await screen.findByText(/Sign In/i)).toBeOnTheScreen();
  });

  it('should display required error when values are empty', async () => {
    render(<SignUpForm onSubmit={onSubmitMock} />);
    const button = screen.getByTestId('continue-button');

    fireEvent.press(button);
    expect(await screen.findByText(/Email is required/i)).toBeOnTheScreen();
  });

  it('should display error when email is invalid', async () => {
    render(<SignUpForm onSubmit={onSubmitMock} />);
    const button = screen.getByTestId('continue-button');
    const emailInput = screen.getByTestId('email-input');

    fireEvent.changeText(emailInput, 'invalid-email');
    fireEvent.press(button);
    expect(await screen.findByText(/Invalid email format/i)).toBeOnTheScreen();
  });

  it('Should call SignUpForm with correct values when values are valid', async () => {
    render(<SignUpForm onSubmit={onSubmitMock} />);

    const button = screen.getByTestId('continue-button');
    const nameInput = screen.getByTestId('name');
    const emailInput = screen.getByTestId('email-input');
    const phoneInput = screen.getByTestId('phone-number');
    const aadhaarInput = screen.getByTestId('aadhaar-number');
    const panInput = screen.getByTestId('pancard-number');
    const reraInput = screen.getByTestId('rera-number');
    const gstInput = screen.getByTestId('gst-number');

    fireEvent.changeText(nameInput, 'John Doe');
    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(phoneInput, '1234567890');
    fireEvent.changeText(aadhaarInput, '************');
    fireEvent.changeText(panInput, '**********');
    fireEvent.changeText(reraInput, 'RERA12345');
    fireEvent.changeText(gstInput, '22**********1Z5');

    fireEvent.press(button);

    await waitFor(() => {
      expect(onSubmitMock).toHaveBeenCalledTimes(1);
    });

    expect(onSubmitMock).toHaveBeenCalledWith(
      {
        name: 'John Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        aadhaarNumber: '************',
        panNumber: '**********',
        reraNumber: 'RERA12345',
        gstNumber: '22**********1Z5',
      },
      undefined,
    );
  });
});
