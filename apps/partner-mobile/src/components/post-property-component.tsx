import { Image as NImage } from 'expo-image';
import * as React from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController } from 'react-hook-form';
import type { TextInputProps } from 'react-native';
import { I18nManager, Text, View } from 'react-native';
import { TextInput as NTextInput } from 'react-native';
import { cn } from '@/ui';

export interface NInputProps extends TextInputProps {
  label?: string;
  disabled?: boolean;
  error?: string;
  icon?: number;
  multiline?: boolean;
  width?: string;
  placeholder?: string; // Add placeholder prop
  imagesource?: string;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
    >
  | undefined;

export type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };

// Define ControlledInputProps
export type ControlledInputProps<T extends FieldValues> = NInputProps & {
  name: Path<T>;
  control: Control<T>;
  rules?: RuleType<T>;
  transformToUppercase?: boolean;
};

export const PostPropertyComponent = React.forwardRef<NTextInput, NInputProps>(
  (props, ref) => {
    const {
      label,
      icon,
      autoCapitalize,
      multiline,
      width,
      disabled,
      placeholder,
      imagesource,
      error,
      ...inputProps
    } = props;

    const [isFocussed, setIsFocussed] = React.useState(false);

    const onBlur = React.useCallback(() => setIsFocussed(false), []);
    const onFocus = React.useCallback(() => setIsFocussed(true), []);

    return (
      <View className={`mb-2.5 flex-1 ${width || 'w-full'}`}>
        {label && (
          <Text
            className={cn(
              'mb-2 font-airbnb_md text-base font-medium text-text-600',
              error && 'text-danger-600',
            )}
          >
            {label}
          </Text>
        )}
        <View
          className={cn(
            'mt-0 flex-1 flex-row items-center rounded-xl border-[0.5px] border-[#ECE9E8] bg-primary-50',
            // isFocussed && 'border-secondary-main',
            error && 'border-danger-600',
          )}
        >
          {icon === 1 && (
            <NImage
              source={imagesource}
              className="ml-5 h-6 w-5"
              contentFit="contain"
              tintColor={isFocussed ? '#45A58E' : '#926C57'}
            />
          )}
          {/** Custom placeholder logic */}
          {!inputProps.value && !isFocussed && (
            <Text
              className={`${icon === 1 ? 'ml-2' : 'ml-5'} font-airbnb_bk text-sm font-normal text-text-600`}
            >
              {placeholder}
            </Text>
          )}
          <NTextInput
            ref={ref}
            className={`flex-1 px-5 py-3.5 font-airbnb_bk text-base font-medium leading-5`}
            onBlur={onBlur}
            onFocus={onFocus}
            autoCapitalize={autoCapitalize}
            multiline={multiline}
            editable={!disabled}
            {...inputProps}
            style={{
              writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
          />
        </View>
        {error && <Text className="text-sm text-danger-400">{error}</Text>}
      </View>
    );
  },
);

// ControlledInput to wrap PostPropertyComponent
export function ControlledInput<T extends FieldValues>(
  props: ControlledInputProps<T>,
) {
  const { name, control, rules, transformToUppercase, ...inputProps } = props;

  const { field, fieldState } = useController({ control, name, rules });

  return (
    <PostPropertyComponent
      ref={field.ref}
      autoCapitalize={inputProps.autoCapitalize}
      onChangeText={(text) =>
        transformToUppercase
          ? field.onChange(text.toUpperCase())
          : field.onChange(text)
      }
      value={(field.value as string) || ''}
      {...inputProps}
      error={fieldState.error?.message}
      disabled={inputProps.disabled}
    />
  );
}
