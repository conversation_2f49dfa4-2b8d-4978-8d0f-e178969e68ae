import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';
import Svg, { Circle } from 'react-native-svg';

interface CircularGraphProps {
  total: number;
  magnitude?: number;
  progressCircleColor: string;
  bgCircleColor: string;
  CenterIcon: React.ReactNode;
  headingText?: React.ReactNode;
  descriptionText?: React.ReactNode;
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const CircularGraph: React.FC<CircularGraphProps> = ({
  total,
  magnitude = 0,
  progressCircleColor,
  bgCircleColor,
  CenterIcon,
  headingText,
  descriptionText,
}) => {
  const radius = 32;
  const strokeWidth = 4;
  const normalizedRadius = radius - strokeWidth / 2;
  const circumference = normalizedRadius * 2 * Math.PI;

  const strokeDashoffset = useRef(new Animated.Value(circumference)).current;

  useEffect(() => {
    const progressPercentage = (magnitude / total) * 100;
    const targetOffset =
      circumference - (progressPercentage / 100) * circumference;

    Animated.timing(strokeDashoffset, {
      toValue: targetOffset,
      duration: 700,
      useNativeDriver: true,
    }).start();
  }, [magnitude, total, circumference, strokeDashoffset]);

  return (
    <View className="mt-3 flex w-full">
      <View className="relative items-center">
        <Svg
          height={radius * 2}
          width={radius * 2}
          style={{ transform: [{ rotate: '-90deg' }] }}
        >
          <Circle
            stroke={bgCircleColor}
            fill="transparent"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
          <AnimatedCircle
            stroke={progressCircleColor}
            fill="transparent"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            strokeDasharray={[circumference, circumference]}
            strokeDashoffset={strokeDashoffset}
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
        </Svg>

        <View
          className="absolute"
          style={{
            width: radius * 2,
            height: radius * 2,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {CenterIcon}
        </View>

        <View
          style={{ flexDirection: 'row', alignItems: 'center', marginTop: 6 }}
        >
          {headingText}
        </View>

        <View style={{ marginTop: 3 }}>{descriptionText}</View>
      </View>
    </View>
  );
};

export default CircularGraph;
