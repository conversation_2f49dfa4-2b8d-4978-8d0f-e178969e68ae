import { Image as NImage } from 'expo-image';
import React from 'react';
import { Text, View } from 'react-native';

// Border component that decides whether to show a line or text
// const Border = ({ complete, current }: any) => {
//   return complete ? (
//     <View className="h-0.5 w-20 bg-primary-700" />
//   ) : (
//     <Text
//       className={`${current ? 'text-primary-750' : 'text-primary-700'} mr-2`}
//     >
//       ---------
//     </Text>
//   );
// };

const Border = ({
  complete,
  current,
  isNext,
}: {
  complete: boolean;
  current: boolean;
  isNext?: number;
}) => (
  <>
    {complete ? (
      <View className="w-16 border-solid border-primary-700 border" />
    ) : (
      <View className="w-16 mr-2 flex-row items-center justify-evenly">
        {[...Array(5)].map((_, index) => (
          <View 
            key={index}
            className={`h-2 w-2 rounded-full ${current ? 'bg-primary-750' : 'bg-primary-750 opacity-50'}`} 
          />
        ))}
      </View>
    )}
  </>
);

const Stepper = ({ currentStep }: any) => {
  return (
    <View className="mx-3 my-5 flex-row items-center self-center">
      <View className="flex-row items-center">
        <NImage
          source={
            currentStep === 1
              ? require('../../assets/stepper/step1current.png')
              : currentStep > 1
                ? require('../../assets/stepper/step1complete.png')
                : null
          }
          className="h-11 w-11"
          contentFit="contain"
        />
        <Border
          complete={currentStep > 1}
          current={currentStep === 1}
          isNext={2}
        />
      </View>

      <View className="flex-row items-center">
        <NImage
          source={
            currentStep === 2
              ? require('../../assets/stepper/step2current.png')
              : currentStep > 2
                ? require('../../assets/stepper/step2complete.png')
                : require('../../assets/stepper/step2incomplete.png')
          }
          className="h-11 w-11"
          contentFit="contain"
        />
        <Border
          complete={currentStep > 2}
          current={currentStep === 2}
          isNext={currentStep + 1}
        />
      </View>

      <View className="flex-row items-center">
        <NImage
          source={
            currentStep === 3
              ? require('../../assets/stepper/step3current.png')
              : currentStep > 3
                ? require('../../assets/stepper/step3complete.png')
                : currentStep === 2
                  ? require('../../assets/stepper/step3next.png')
                  : require('../../assets/stepper/step3incomplete.png')
          }
          className="h-11 w-11"
          contentFit="contain"
        />
        <Border
          complete={currentStep > 3}
          current={currentStep === 3}
          isNext={currentStep + 1}
        />
      </View>

      <View className="flex-row items-center">
        <NImage
          source={
            currentStep === 4
              ? require('../../assets/stepper/step4current.png')
              : currentStep === 3
                ? require('../../assets/stepper/step4next.png')
                : require('../../assets/stepper/step4incomplete.png')
          }
          className="h-11 w-11"
          contentFit="contain"
        />
      </View>
    </View>
  );
};

export default Stepper;
