import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';
import Svg, { Circle } from 'react-native-svg';

interface Segment {
  value: number;
  color: string;
}

interface ColourfulCircularGraphProps {
  segments: Segment[];
  total?: number;
  CenterIcon?: React.ReactNode;
  headingText?: React.ReactNode;
  descriptionText?: React.ReactNode;
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const ColourfulCircularGraph: React.FC<ColourfulCircularGraphProps> = ({
  segments,
  total,
  CenterIcon,
  headingText,
  descriptionText,
}) => {
  const radius = 84;
  const strokeWidth = 7;
  const normalizedRadius = radius - strokeWidth / 2;
  const circumference = normalizedRadius * 2 * Math.PI;

  const totalValue =
    total || segments.reduce((sum, segment) => sum + segment.value, 0);

  const animatedOffsets = segments.map(
    () => useRef(new Animated.Value(circumference)).current,
  );

  const rotationValues = segments.map((_, index) => {
    const previousSegmentsLength = segments
      .slice(0, index)
      .reduce((sum, seg) => sum + (seg.value / totalValue) * circumference, 0);
    return (previousSegmentsLength / circumference) * 360;
  });

  useEffect(() => {
    let currentOffset = 0;

    const animations = segments.map((segment, index) => {
      const segmentLength = (segment.value / totalValue) * circumference;
      const targetOffset = circumference - segmentLength;

      const startOffset = currentOffset;
      currentOffset += segmentLength;

      return Animated.timing(animatedOffsets[index], {
        toValue: targetOffset,
        duration: 700,
        useNativeDriver: true,
      });
    });

    Animated.parallel(animations).start();
  }, [segments, totalValue, circumference]);

  return (
    <View className="my-3 flex w-full">
      <View className="relative items-center">
        <Svg
          height={radius * 2}
          width={radius * 2}
          style={{ transform: [{ rotate: '-90deg' }] }}
        >
          {segments.map((segment, index) => {
            const previousSegmentsLength = segments
              .slice(0, index)
              .reduce(
                (sum, seg) => sum + (seg.value / totalValue) * circumference,
                0,
              );

            const rotation = (previousSegmentsLength / circumference) * 360;

            return (
              <AnimatedCircle
                key={index}
                stroke={segment.color}
                fill="transparent"
                strokeWidth={strokeWidth}
                strokeLinecap="butt"
                strokeDasharray={`${circumference} ${circumference}`}
                strokeDashoffset={animatedOffsets[index]}
                r={normalizedRadius}
                cx={radius}
                cy={radius}
                origin={`${radius}, ${radius}`}
                rotation={rotationValues[index]}
              />
            );
          })}
        </Svg>

        <View
          className="absolute"
          style={{
            width: radius * 2,
            height: radius * 2,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {CenterIcon}

          <View className="items-center justify-center">
            {headingText}

            <View className="mt-1 w-4/5">{descriptionText}</View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default ColourfulCircularGraph;
