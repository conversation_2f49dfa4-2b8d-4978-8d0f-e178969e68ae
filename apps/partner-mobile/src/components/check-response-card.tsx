/* eslint-disable max-lines-per-function */
import { Image as NImage } from 'expo-image';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { TextInput } from 'react-native-gesture-handler';

interface CheckResponseCardProps {
  item: {
    id: number;
    name: string;
    datetime: string;
    budget: string;
    location: string;
    msg: string;
  };
}

const CheckResponseCard: React.FC<CheckResponseCardProps> = ({ item }) => {
  const [isFocused, setIsFocused] = useState<boolean>(false);
  return (
    <View className="mb-2.5 rounded-2xl bg-[#FFF9F5] p-3">
      {/* Top Part */}
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center">
          <NImage
            source={require('../../assets/images/profile7.png')}
            className="h-10 w-10"
          />
          <View className="ml-1.5">
            <Text className="font-airbnb_bd text-base font-bold text-text-550">
              {item.name}
            </Text>
            <Text className="mt-0.5 font-airbnb_bk text-[11px] font-normal text-text-550">
              {item.datetime}
            </Text>
          </View>
        </View>

        {isFocused ? (
          <Pressable
            className=" self-start rounded-[4px] bg-secondary-100 p-1.5"
            onPress={() => router.push('/listing-screens/property-enquiry')}
          >
            <Text className="font-airbnb_md text-[11px] font-medium text-secondary-main700">
              Check Response
            </Text>
          </Pressable>
        ) : (
          <Pressable
            className=" self-start rounded-[4px] bg-secondary-main700 p-1.5"
            onPress={() => router.push('/listing-screens/property-enquiry')}
          >
            <Text className="font-airbnb_md text-[11px] font-medium text-[#F4FFFD]">
              New Reponses
            </Text>
          </Pressable>
        )}
      </View>

      {/* Mid part */}
      <View className="flex-row items-center justify-between py-3.5">
        <View className="flex-row items-center">
          <Pressable className="mr-1 self-start rounded-[4px] p-0.5">
            <NImage
              source={require('../../assets/icons/moneybag.png')}
              className="h-4 w-4"
            />
          </Pressable>
          <Text className="font-airbnb_md text-xs font-medium text-primary-750">
            {item.budget}
          </Text>
        </View>

        <View className="flex-row items-center">
          <Pressable className="mr-1 self-start rounded-[4px] p-0.5">
            <NImage
              source={require('../../assets/icons/location2.png')}
              className="h-3.5 w-3"
              contentFit="contain"
            />
          </Pressable>
          <Text className="font-airbnb_md text-xs font-medium text-primary-750">
            {item.location}
          </Text>
        </View>
      </View>

      {/* ControlledInput */}
      {isFocused ? (
        <View className="py-2 flex-row items-center">
          <Text className="flex-1 font-airbnb_bk text-xs font-normal text-text-600">
            {item.msg}
          </Text>
          <Pressable
            className="self-end rounded-full bg-secondary-main700 p-1.5"
            onPress={() => router.push('/chat-screens/chat-screen')}
          >
            <NImage
              source={require('../../assets/icons/chats1.png')}
              className="h-6 w-6"
            />
          </Pressable>
        </View>
      ) : (
        <View
          className={`w-full flex-row items-center justify-between rounded-2xl border ${isFocused ? 'border-secondary-main700' : 'border-secondary-200'} bg-white px-4`}
        >
          <TextInput
            className="py-4 flex-1 items-center justify-center font-airbnb_bk text-xs font-normal text-text-600"
            placeholder={'Reply'}
            placeholderTextColor={'#252525'}
            multiline={true}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
          />

          <NImage
            source={require('../../assets/icons/send.png')}
            className="aspect-square w-[6%]"
            contentFit="contain"
            tintColor={'#F04D24'}
          />
        </View>
      )}
    </View>
  );
};

export default CheckResponseCard;
