import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON><PERSON><PERSON>,
  useState,
} from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';
import { Modal, useModal, Button, ControlledInput, Pressable } from '@/ui';
import { OtpInput } from 'react-native-otp-entry';
import { useForm } from 'react-hook-form';
import { api } from '@/utils/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  EditProfileSchema,
  signUpInputValidation,
} from '@/utils/form-validators';
import { showMessage } from 'react-native-flash-message';

const { height, width } = Dimensions.get('screen');

const formSchema = signUpInputValidation
  .pick({
    phoneNumber: true,
    // adharcardNumber: true,
    // pancardNumber: true,
    email: true,
  })
  .extend({
    otp: z
      .string({ message: 'Invalid otp' })
      .min(4, { message: 'Minimum 4 digits required' })
      .max(4, { message: 'Maximum 4 digits allowed' }),
  });

type PersonalDetailEditFormSheetProps = {
  phoneNumber?: string;
  email?: string;
};

export const EditBottomsheet = ({
  email,
  phoneNumber,
}: {
  email: string;
  phoneNumber: string;
}) => {
  const [msg, setMsg] = useState<string | null>(null);
  const { ref, present, dismiss } = useModal();

  const { mutate: requestOtp, isPending } =
    api.user.requestOtpForProfileUpdate.useMutation();
  const { mutate: verifyOtpAndUpdateProfile } =
    api.user.verifyOtpForProfileUpdate.useMutation();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phoneNumber: '',
      //   pancardNumber: "",
      //   adharcardNumber: "",
    },
  });

  const handleOtpRequest = () => {
    requestOtp(undefined, {
      onSuccess: (opts) => {
        setMsg(opts.message);
        showMessage({
          message: opts.message,
          type: 'success',
        });
      },
      onError: (opts) => {
        setMsg(opts.message);

        showMessage({
          message: opts.message,
          type: 'danger',
        });
      },
    });
  };

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    verifyOtpAndUpdateProfile(values, {
      onSuccess: (opts) => {
        showMessage({
          message: opts.message,
          type: 'success',
        });
      },
      onError: (opts) => {
        showMessage({
          message: opts.message,
          type: 'danger',
        });
      },
    });
  };

  useEffect(() => {
    if (phoneNumber && email) {
      form.setValue('email', email);
      form.setValue('phoneNumber', phoneNumber);
    }
  }, [form, email, phoneNumber]);

  return (
    <View>
      <Pressable
        className="self-end"
        onPress={() => {
          console.log('presenting modal');
          present();
        }}
      >
        <Text className="font-airbnb_md text-sm font-medium text-[#451F0A]">
          Edit
        </Text>
      </Pressable>
      <Modal snapPoints={['70%']} ref={ref} title="Edit Contact Info">
        <View style={styles.modalContent}>
          <OtpInput
            numberOfDigits={4}
            focusColor="#C58E00"
            focusStickBlinkingDuration={500}
            onTextChange={(text) => {
              form.setValue('otp', text);
              // if (text.length === 4) setError('');
            }}
            onFilled={(text) => form.setValue('otp', text)}
            type="numeric"
            textInputProps={{
              accessibilityLabel: 'One-Time Password',
            }}
            theme={{
              containerStyle: styles.container,
              pinCodeContainerStyle: styles.pinCodeContainer,
              pinCodeTextStyle: styles.pinCodeText,
              focusStickStyle: styles.focusStick,
              focusedPinCodeContainerStyle: styles.activePinCodeContainer,
            }}
          />
          {/*{error ? (*/}
          {/*  <View style={styles.errorContainer}>*/}
          {/*    <Text style={styles.errorText}>{error}</Text>*/}
          {/*  </View>*/}
          {/*) : null}*/}
          <View>
            <ControlledInput
              testID="modal-email-input"
              control={form.control}
              name="email"
              label="Email Id"
              autoCapitalize="none"
            />
            <ControlledInput
              testID="modal-phone-number"
              control={form.control}
              keyboardType="numeric"
              name="phoneNumber"
              label="Phone Number"
            />
          </View>
          <Button label="Save Changes" onPress={form.handleSubmit(onSubmit)} />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { paddingHorizontal: width * 0.125 },
  pinCodeContainer: {
    borderColor: '#ECE9E8',
    borderRadius: 12,
    borderWidth: 2,
  },
  pinCodeText: {
    fontSize: 20,
    fontWeight: '400',
    color: '#252525',
    fontFamily: 'AirbnbW_Bk',
  },
  focusStick: {},
  activePinCodeContainer: {
    backgroundColor: '#FFFFFF',
  },
  modalContent: { flex: 1, paddingHorizontal: 20, gap: 16 },
  errorContainer: { marginTop: 10, alignItems: 'center' },
  errorText: { color: 'red' },
});

export default EditBottomsheet;
