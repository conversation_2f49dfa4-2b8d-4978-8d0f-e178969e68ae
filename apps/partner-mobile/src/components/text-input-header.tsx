import { Image as NImage } from 'expo-image';
import React from 'react';
import { Pressable, View } from 'react-native';
import { router } from 'expo-router';

import TextInputComponent from '@/components/text-input-component';

type PropsType = {
  placeholderText: string;
  value?: string;
  onChangeText?: (text: string) => void;
};

import { memo } from 'react';
const  TextInputHeader=({
  placeholderText,
  onChangeText,
  value,
}: PropsType) =>{
  return (
    <View className="bg-[#fff]">
      <View className="flex-row items-center justify-center">
        <View className="w-full px-5">
          <TextInputComponent
            background={'bg-[#fff]'}
            placeholdercolor={'#3A3A3A'}
            searchiconimagecolor={'#3A3A3A'}
            textinputcolor={'text-[#3A3A3A]'}
            bordercolor={'border-secondary-main700'}
            placeholdertext={placeholderText}
            onChangeText={
              onChangeText
                ? onChangeText
                : (text) => router.setParams({ query: text })
            }
          />
        </View>

        {/*<Pressable>*/}
        {/*  <NImage*/}
        {/*    source={require('../../assets/icons/dot2.png')}*/}
        {/*    className="ml-2 h-10 w-10"*/}
        {/*  />*/}
        {/*</Pressable>*/}
      </View>
    </View>
  );
}
export default(memo(TextInputHeader));