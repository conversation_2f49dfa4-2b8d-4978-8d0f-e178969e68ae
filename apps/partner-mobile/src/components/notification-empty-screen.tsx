import { Image as NImage } from 'expo-image';
import React from 'react';
import { Text, View } from 'react-native';

export default function NotificationEmpty() {
  return (
    <View className="h-full items-center mt-12">
      <NImage
        source={require('../../assets/images/pana.png')}
        className="aspect-square w-4/5"
        contentFit="contain"
      />
      <Text className="mb-3 mt-16 w-4/5 text-center font-airbnb_bd text-lg font-bold text-text-600">
        No Notification Received Yet
      </Text>
      <Text className="w-4/5 text-center font-airbnb_bk text-base font-normal text-text-500">
        You Haven't received any notifications yet, Keep Checking Deer!!
      </Text>
    </View>
  );
}
