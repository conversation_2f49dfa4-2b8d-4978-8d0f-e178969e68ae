import React, { useState } from 'react';
import { Text, View } from 'react-native';
import { Pressable } from '@/ui';

type FilterProps = {
  FilterOptions: { label: string; value: string }[];
  label: string;
  singleSelect?: boolean;
  value?: string[];
  onValueChange?: (v: string[]) => void;
};

const FilterComponent: React.FC<FilterProps> = ({
  FilterOptions,
  label,
  value,
  singleSelect,
  onValueChange,
}) => {
  const [isShown, setIsShown] = useState(true);
  const handleShown = () => {
    setIsShown((prevState) => !prevState);
  };

  const [selectedFilter, setSelectedFilter] = useState<string[]>(value ?? []);

  const toggleFilterType = (type: string) => {
    setSelectedFilter((prev) => {
      const isExist = prev.includes(type);
      if (isExist) {
        const newValues = prev.filter((item) => item !== type);
        onValueChange && onValueChange(newValues);
        return newValues;
      } else {
        const newValues = singleSelect ? [type] : [...prev, type];
        onValueChange && onValueChange(newValues);
        return newValues;
      }
    });
  };

  const getValueLabel = (value: string) => {
    return FilterOptions.find((item) => item.value === value)?.label;
  };

  return (
    <View className="py-4 px-5 border border-primary-300 rounded-2xl">
      <View className="mb-5 flex-row items-center justify-between">
        <Text
          className="font-airbnb_md font-medium text-lg text-text-600"
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{ maxWidth: '80%' }}
        >
          {label}
        </Text>
        <Pressable onPress={handleShown}>
          {/*<FontAwesome6*/}
          {/*  name={isShown ? 'chevron-down' : 'chevron-up'}*/}
          {/*  size={20}*/}
          {/*  color="#5F2800"*/}
          {/*/>*/}
        </Pressable>
      </View>

      {/* Mapping the list */}
      {isShown && (
        <View>
          {/* Display selected types */}
          <View className="flex-row flex-wrap items-center">
            {selectedFilter.length > 0 ? (
              selectedFilter.map((type) => (
                <Pressable
                  key={type}
                  className="my-2 mr-2 self-start rounded-2xl bg-primary-700 px-5 py-3"
                  onPress={() => toggleFilterType(type)}
                >
                  <Text className="font-airbnb_bk text-sm font-normal text-white">
                    {getValueLabel(type)}
                  </Text>
                </Pressable>
              ))
            ) : (
              <Text className="mb-5 font-airbnb_md text-base text-text-600">
                No Filter option selected.
              </Text>
            )}
          </View>

          {/* Display Unselected/Full types */}
          <View className="flex-row flex-wrap items-center justify-start">
            {FilterOptions.filter(
              (type) => !selectedFilter.includes(type.value),
            ).map((type) => (
              <Pressable
                key={type.value}
                className="my-2 mr-2 self-start rounded-2xl border border-primary-100 bg-white px-5 py-3"
                onPress={() => toggleFilterType(type.value)}
              >
                <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                  {type.label}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

export default FilterComponent;
