import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { signUpInputValidation } from '@/utils/form-validators';
import * as z from 'zod';

import {
  Button,
  ControlledInput,
  ControlledSelect,
  ScrollView,
  Select,
  Text,
  View,
} from '@/ui';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { useRouter } from 'expo-router';

export type FormType = z.infer<typeof signUpInputValidation>;

export type SignUpFormProps = {
  onSubmit?: SubmitHandler<FormType>;
  label?: boolean;
};

// eslint-disable-next-line max-lines-per-function
export const SignUpForm = ({ onSubmit = () => {}, label }: SignUpFormProps) => {
  const router = useRouter();
  const { handleSubmit, control } = useForm<FormType>({
    resolver: zodResolver(signUpInputValidation),
  });

  const { data: cityData } = api.user.getCities.useQuery();
  const signUpMutation = api.user.signup.useMutation();

  const onSubmitInternal = async (data: FormType) => {
    signUpMutation.mutate(data, {
      onSuccess: () => {
        console.log('success');
        showMessage({
          message: 'Signup successful',
          type: 'success',
        });
        router.push({
          pathname: `/auth/otp`,
          params: {
            phoneNumber: data.phoneNumber,
          },
        });
      },
      onError: (error) => {
        showMessage({
          message:
            error.message ?? 'Something went wrong, Signup not successful',
          type: 'danger',
        });
      },
    });
  };

  const onFormError = (errors: any) => {
    console.log('errors', errors);
    showMessage({
      message: 'Please check form fields',
      type: 'danger',
    });
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      keyboardVerticalOffset={10}
    >
      {label === true ? (
        <View className="p-4">
          <Text
            testID="form-title"
            className="mb-2 font-airbnb_xbd text-2xl font-extrabold text-secondary-650"
          >
            Sign Up
          </Text>
          <Text className="mb-5 font-airbnb_bk text-base font-normal text-text-550">
            Create account to continue with Deer Connect
          </Text>
        </View>
      ) : null}
      <ScrollView
        showsVerticalScrollIndicator={false}
        // contentContainerStyle={{ flex: 1 }}
        className="flex-1"
      >
        <View className={`px-4 ${label === true ? null : 'pb-28 pt-6'}`}>
          <ControlledInput
            // icon={1}
            // imagesource={require('../../assets/icons/profile.png')}
            testID="name"
            control={control}
            name="name"
            label="Name"
            // placeholder="enter your name here"
          />
          <ControlledInput
            // icon={1}
            // imagesource={require('../../assets/icons/profile.png')}
            testID="email-input"
            control={control}
            name="email"
            label="Email Id"
            autoCapitalize="none"
            // placeholder="enter your email here"
          />
          <ControlledInput
            testID="phone-number"
            control={control}
            keyboardType="numeric"
            name="phoneNumber"
            label="Phone Number"
            // placeholder="enter your phone number here"
          />
          <ControlledInput
            testID="aadhaar-number"
            control={control}
            keyboardType="numeric"
            name="adharcardNumber"
            label="Aadhaar Card Number*"
            // placeholder="enter your Aadhaar card number here"
          />
          <ControlledInput
            testID="pancard-number"
            control={control}
            keyboardType="default"
            name="pancardNumber"
            label="PAN Card Number*"
            transformToUppercase={true}
            // placeholder="enter your PAN card number here"
          />
          <ControlledInput
            testID="rera-number"
            control={control}
            keyboardType="default"
            name="reraNumber"
            label="RERA Number (optional)"
            transformToUppercase={true}
            // placeholder="enter your RERA number here"
          />
          <ControlledInput
            testID="gst-number"
            control={control}
            keyboardType="default"
            name="gstNumber"
            label="GST Number"
            // placeholder="enter your GST number here"
          />
          <ControlledSelect
            name="cityId"
            label="City"
            control={control}
            placeholder="Select your city"
            options={cityData?.map((city) => ({
              label: city.name,
              value: city.id,
            }))}
          />
          <ControlledInput
            testID="referred-by"
            control={control}
            keyboardType="default"
            name="referredBy"
            label="Referred By"
            placeholder=""
          />
        </View>
      </ScrollView>
      <View className="p-4">
        {label === true ? (
          <Button
            testID="continue-button"
            label="Continue"
            loading={signUpMutation.isPending}
            onPress={handleSubmit(onSubmitInternal, onFormError)}
          />
        ) : null}
      </View>
    </KeyboardAvoidingView>
  );
};
