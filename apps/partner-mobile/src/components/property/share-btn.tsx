import { Pressable, Share } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';
import { getBaseUrl } from '@/utils/base-url';

const PropertyShareBtn = ({
  propertyId,
  aboutProperty,
  propertyTitle,
  bg,
}: {
  propertyId: string;
  propertyTitle: string;
  aboutProperty: string;
  bg?: boolean;
}) => {
  const handleShare = () => {
    console.log('title recieved ', propertyTitle);

    // https://tabl3.com/property-listing?propertyFor=SALE&viewPropertyId=cm572y4gg001uurrgu2ky7xxk
    Share.share(
      {
        //   url: `${getBaseUrl()}/property-listing?propertyFor=SALE&viewPropertyId=${propertyId}`,
        message: `${getBaseUrl()}/property-listing?propertyFor=SALE&viewPropertyId=${propertyId}`,
        title: propertyTitle,
      },
      {
        dialogTitle: 'Share Property',
      },
    ).catch((e) => {
      console.log(`expo error is`, e);
    });
  };

  return (
    <Pressable
      onPress={handleShare}
      className={` ${bg ? 'p-3 bg-[#F1F1F1] rounded-full' : ''}`}
    >
      <Image
        source={require('../../../assets/icons/share.png')}
        className="h-6 w-6"
        contentFit="contain"
      />
    </Pressable>
  );
};

export default PropertyShareBtn;
