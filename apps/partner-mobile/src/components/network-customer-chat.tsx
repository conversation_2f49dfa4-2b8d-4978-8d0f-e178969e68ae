import { Image as NImage } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/network-button-rating-experience-component';
import UserPic from '@/components/user-pic';
import { CustomerConnectionRequest } from '@/types';

export const NetworkCustomerChat = ({
  item,
  Chat,
}: {
  item: CustomerConnectionRequest;
  Chat: () => void;
}) => {
  return (
    <Pressable className="mb-2.5 mt-4 rounded-[10px] border border-[#E9E2DD] bg-primary-50 px-2.5 py-3">
      <View>
        {/* top part */}
        <View className="mb-2 flex-row items-center justify-between">
          {/* image and name part */}
          <View className="flex-row items-center gap-2">
            <UserPic
              picUrl={
                item.customer.cloudinaryImagePublicUrl ??
                item.customer.profileImagePublicUrl
              }
              size={42}
              color="#784100"
              className="h-12 w-12 rounded-full"
            />
            {/*<NImage*/}
            {/*  source={*/}
            {/*    item.sender.filePublicUrl*/}
            {/*      ? { uri: item.sender.filePublicUrl }*/}
            {/*      : require('../../assets/images/profile7.png')*/}
            {/*  }*/}
            {/*  className="h-12 w-12"*/}
            {/*/>*/}
            <View className="ml-1.5">
              <Text className="font-airbnb_xbd text-lg font-extrabold text-primary-750">
                {item.customer.name}
              </Text>
            </View>
          </View>

          <View>
            <View className="mb-2 flex-row items-center self-end gap-1">
              <NImage
                source={require('../../assets/icons/handshake.png')}
                className="h-4 w-4"
              />
              <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-primary-750">
                {formatDistanceToNow(new Date(item.updatedAt), {
                  addSuffix: true,
                })}
              </Text>
            </View>

            <View className="mb-2 flex-row items-center self-end">
              <NImage
                source={require('../../assets/icons/location2.png')}
                className="h-4 w-4"
              />
              <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-primary-750">
                {item.customer.city?.name}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Buttons */}

      <Button
        label="Chat"
        backgroundColor={'bg-secondary-main700'}
        textColor={'text-white'}
        onPress={Chat}
      />
    </Pressable>
  );
};
