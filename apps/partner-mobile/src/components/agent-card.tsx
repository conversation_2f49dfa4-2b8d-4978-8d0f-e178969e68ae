import { Dimensions, StyleSheet, Text, View } from 'react-native';
import { Button } from '@/ui';
import React from 'react';
import { api } from '@/utils/api';
import { Image } from 'expo-image';
import { showMessage } from 'react-native-flash-message';

const { height, width } = Dimensions.get('screen');

const AgentCard = ({
  agent,
  AddAgent,
  RemoveAgent,
}: {
  agent: {
    id: string;
    name: string;
    company?: {
      id: string;
      companyName: string;
    } | null;
    propertiesSold: number | null;
    experience: string | null;
    filePublicUrl: string | null;
    cloudinaryProfileImageUrl: string | null;
    rating: string | null;
    receivedConnectionRequests?: {
      id: string;
    }[];
    sentConnectionRequests?: {
      id: string;
    }[];
  };
  handlePress?: () => void;
  AddAgent?: boolean;
  RemoveAgent?: boolean;
}) => {
  const utils = api.useUtils();
  const sendConnectionReqMutation =
    api.connection.sendConnectionRequest.useMutation();
  const addExistingAgentMutation = api.company.addExistingAgent.useMutation();
  const removeAgent = api.company.removeAgentFromCompany.useMutation();
  const handleClick = (
    agentId: string,
    purpose: 'add' | 'connect' | 'remove',
  ) => {
    switch (purpose) {
      case 'connect':
        sendConnectionReqMutation.mutate(
          {
            receiverId: agentId,
          },
          {
            onSuccess: (opts) => {
              utils.invalidate();
              if (opts.warning) {
                showMessage({
                  message: opts.message,
                  type: 'danger',
                });
                return;
              }
              showMessage({
                message: opts.message,
                type: 'success',
              });
            },
            onError: (opts) => {
              showMessage({
                message: opts.message,
                type: 'danger',
              });
            },
          },
        );
        break;

      case 'add':
        addExistingAgentMutation.mutate(
          {
            id: agentId,
          },
          {
            onSuccess: (opts) => {
              utils.invalidate();
              if (opts.error) {
                showMessage({
                  message: opts.error,
                  type: 'danger',
                });
                return;
              }
              showMessage({
                message: opts?.messageTitle ?? '',
                type: 'success',
              });
            },
            onError: (opts) => {
              showMessage({
                message: opts.message,
                type: 'danger',
              });
            },
          },
        );
        break;

      case 'remove':
        removeAgent.mutateAsync(
          { agentId: agent.id },
          {
            onSuccess: (opts) => {
              utils.invalidate();
              if (opts.error)
                showMessage({
                  message: opts.error,
                  type: 'danger',
                });
              showMessage({
                message: opts?.messageTitle ?? '',
                type: 'success',
              });
            },
            onError: (opts) => {
              showMessage({
                message: opts.message,
                type: 'danger',
              });
            },
          },
        );
        break;
    }
  };

  return (
    <View
      key={agent.id}
      style={{
        width: width * 0.6837,
        height: height * 0.383,
        position: 'relative',
        ...styles.shadowBox,
      }}
      className="flex-1 bg-secondary-650 rounded-2xl mx-6"
    >
      <Image
        source={
          agent.cloudinaryProfileImageUrl
            ? { uri: agent.cloudinaryProfileImageUrl }
            : agent.filePublicUrl
              ? { uri: agent.filePublicUrl }
              : require('../../assets/icons/default-user.png')
        }
        contentFit="cover"
        style={{ height: height * 0.2568 }}
        className="w-full rounded-2xl"
      />
      {agent.rating && (
        <View
          className="px-2.5 py-2 absolute bg-secondary-100 flex-row items-center self-end"
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            zIndex: 10,
            borderBottomStartRadius: 12,
            borderTopRightRadius: 12,
          }}
        >
          <Image
            source={require('../../assets/icons/star2.png')}
            className="mr-1 h-5 w-5"
          />
          <Text className="font-medium font-airbnb_md text-lg text-secondary-650">
            {agent.rating}
          </Text>
        </View>
      )}

      <View
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
        }}
        className="m-2.5 p-4 rounded-lg bg-white flex-col flex-end"
      >
        <Text className="mb-0.5 text-primary-800 font-airbnb_bd font-bold text-xl">
          {agent.name}
        </Text>
        <Text className="text-text-550 font-medium text-sm font-airbnb_md">
          {agent.company?.companyName ?? ' '}
        </Text>

        <View className="mt-2 flex-row items-center justify-between">
          <View className="flex-row items-center">
            <Image
              source={require('../../assets/icons/hut1.png')}
              className="mr-1 h-5 w-5"
            />
            <Text className="text-text-main700 font-normal text-xs">
              {agent.propertiesSold ?? 0} Properties Sold
            </Text>
          </View>

          <View className="flex-row items-center">
            <Image
              source={require('../../assets/icons/experience2.png')}
              className="mr-1 h-5 w-5"
            />
            <Text className="text-text-main700 font-normal text-xs">
              {agent.experience ?? 0}yrs Experience
            </Text>
          </View>
        </View>

        {AddAgent ? (
          <View>
            <Button
              label="Add Agent"
              onPress={() => handleClick(agent.id, 'add')}
            />
          </View>
        ) : RemoveAgent ? (
          <View>
            <Button
              label="Remove Agent"
              onPress={() => handleClick(agent.id, 'remove')}
            />
          </View>
        ) : (
          <View>
            {agent.receivedConnectionRequests?.length === 0 &&
            agent.sentConnectionRequests?.length === 0 ? (
              <Button
                className="bg-secondary-750"
                label="Contact Agent"
                loading={sendConnectionReqMutation.isPending}
                onPress={() => handleClick(agent.id, 'connect')}
              />
            ) : (
              <Button className="bg-secondary-750" label="Connected" />
            )}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  shadowBox: {
    shadowColor: '#250000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
});

export default AgentCard;
