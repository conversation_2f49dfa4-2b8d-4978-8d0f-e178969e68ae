{"cli": {"version": ">= 0.57.0"}, "build": {"production": {"channel": "production", "distribution": "store", "node": "22.14.0", "yarn": "1.22.22", "ios": {"image": "latest"}, "android": {"buildType": "app-bundle", "image": "latest"}, "env": {"EXPO_NO_DOTENV": "1", "APP_ENV": "production", "FLIPPER_DISABLE": "1"}, "prebuildCommand": "prebuild --skip-dependency-update react", "cache": {"key": "eas-1"}}, "staging": {"channel": "staging", "distribution": "internal", "node": "22.14.0", "yarn": "1.22.22", "ios": {"image": "latest"}, "android": {"buildType": "apk", "image": "latest"}, "env": {"APP_ENV": "staging", "EXPO_NO_DOTENV": "1", "FLIPPER_DISABLE": "1"}, "prebuildCommand": "prebuild --skip-dependency-update react", "cache": {"key": "eas-1"}}, "development": {"developmentClient": true, "distribution": "internal", "node": "22.14.0", "yarn": "1.22.22", "ios": {"image": "latest"}, "android": {"image": "latest"}, "env": {"APP_ENV": "development", "EXPO_NO_DOTENV": "1"}, "prebuildCommand": "prebuild --skip-dependency-update react", "cache": {"key": "eas-1"}}, "simulator": {"ios": {"simulator": true, "image": "latest"}, "android": {"image": "latest"}, "env": {"APP_ENV": "development", "EXPO_NO_DOTENV": "1"}, "node": "22.14.0", "yarn": "1.22.22", "prebuildCommand": "prebuild --skip-dependency-update react", "cache": {"key": "eas-1"}}}, "submit": {"production": {}}}