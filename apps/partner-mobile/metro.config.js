// Learn more: https://docs.expo.dev/guides/monorepos/
const { getDefaultConfig } = require("expo/metro-config");
const { FileStore } = require("metro-cache");
const { withNativeWind } = require("nativewind/metro");
const {
  wrapWithReanimatedMetroConfig,
} = require("react-native-reanimated/metro-config");
const path = require("node:path");

// Obtain the default configuration
const defaultConfig = getDefaultConfig(__dirname);

// Enhance the default configuration with NativeWind
const nativeWindConfig = withNativeWind(defaultConfig, {
  input: "./global.css",
  configPath: "./tailwind.config.js",
});

// Further wrap the configuration with Reanimated's Metro config
const reanimatedConfig = wrapWithReanimatedMetroConfig(nativeWindConfig);

// Define SVG transformer settings
const svgTransformer = {
  transformer: {
    babelTransformerPath: require.resolve("react-native-svg-transformer"),
  },
  resolver: {
    assetExts: reanimatedConfig.resolver.assetExts.filter(
      (ext) => ext !== "svg",
    ),
    sourceExts: [...reanimatedConfig.resolver.sourceExts, "svg"],
  },
};

// Merge the SVG transformer settings into the existing configuration
const config = {
  ...reanimatedConfig,
  transformer: {
    ...reanimatedConfig.transformer,
    ...svgTransformer.transformer,
  },
  resolver: {
    ...reanimatedConfig.resolver,
    ...svgTransformer.resolver,
  },
};

// Set up Metro cache
config.cacheStores = [
  new FileStore({ root: path.join(__dirname, ".cache/metro") }),
];

// Enable package exports resolution for workspace packages
config.resolver.unstable_enablePackageExports = true;

module.exports = config;

/**
 * Move the Metro cache to the `.cache/metro` folder.
 * If you have any environment variables, you can configure Turborepo to invalidate it when needed.
 *
 * @see https://turbo.build/repo/docs/reference/configuration#env
 * @param {import('expo/metro-config').MetroConfig} config
 * @returns {import('expo/metro-config').MetroConfig}
 */
function withTurborepoManagedCache(config) {
  config.cacheStores = [
    new FileStore({ root: path.join(__dirname, ".cache/metro") }),
  ];
  return config;
}
