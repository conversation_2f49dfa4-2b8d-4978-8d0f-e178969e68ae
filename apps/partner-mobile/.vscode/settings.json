{
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "search.exclude": {
    "yarn.lock": true
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "typescript.tsdk": "node_modules/typescript/lib",
  "eslint.format.enable": true,
  "[javascript][typescript][typescriptreact]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "dbaeumer.vscode-eslint",
    "editor.codeActionsOnSave": [
      "source.addMissingImports",
      "source.fixAll.eslint"
    ]
  },
  "[json][jsonc]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[astro]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "astro-build.astro-vscode"
  },
  "cSpell.words": [
    "autolocate",
    "bordercolor",
    "Buttonbg",
    "Dalal",
    "darkgreen",
    "doublecheck",
    "Entypo",
    "Favourites",
    "Flashlist",
    "gorhom",
    "greenaccent",
    "inkdarkest",
    "Lato",
    "lightbackground",
    "lightgreen",
    "locationstep",
    "maincolor",
    "maincoloringuide",
    "mydeer",
    "Mylistings",
    "nativewind",
    "noofmsg",
    "onboardingcomponent",
    "onboardinglabelgreen",
    "onboardingloggingreen",
    "otpborderaccent",
    "placeholdercolor",
    "placeholdertext",
    "Pressable",
    "primarycolor",
    "primarygreen",
    "primaryy",
    "quadinguide",
    "searchiconimagecolor",
    "secondarycolor",
    "secondaryinguide",
    "signup",
    "Sriya",
    "Tannu",
    "textcolor",
    "textcolordark",
    "textinputcolor",
    "textlightdark",
    "textmediumdark",
    "textvlightdark",
    "textvvlightdark"
  ],
  "i18n-ally.localesPaths": ["src/translations/"],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.disabled": false, // make sure to disable i18n-ally in your global setting and only enable it for such projects
  "tailwindCSS.experimental.classRegex": [
    ["tv\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ],
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
