#!/bin/sh
. "$(dirname "$0")/_/husky.sh"
. "$(dirname "$0")/common.sh"


echo "===\n>> Checking branch name..."

# Check if branch protection is enabled
if [[ -z $SKIP_BRANCH_PROTECTION ]]; then
    BRANCH=$(git rev-parse --abbrev-ref HEAD)
    PROTECTED_BRANCHES="^(main|master)"

    if [[ $BRANCH =~ $PROTECTED_BRANCHES ]]; then
        echo ">> Direct commits to the $BRANCH branch are not allowed. Please choose a new branch name."
        exit 1
    fi
else
    echo ">> Skipping branch protection."
fi

echo ">> Finish checking branch name"
echo ">> Linting your files and fixing them if needed..."

pnpm type-check
pnpm lint-staged