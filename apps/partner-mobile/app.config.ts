/* eslint-disable max-lines-per-function */
import type { ConfigContext, ExpoConfig } from '@expo/config';

import { ClientEnv, Env } from './env';
import { AppIconBadgeConfig } from 'app-icon-badge/types';

const appIconBadgeConfig: AppIconBadgeConfig = {
  enabled: Env.APP_ENV !== 'production',
  badges: [
    {
      text: Env.APP_ENV,
      type: 'banner',
      color: 'white',
    },
    {
      text: Env.VERSION.toString(),
      type: 'ribbon',
      color: 'white',
    },
  ],
};

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'Deer Connect',

  description: `Deer Connect`,
  owner: 'nextfly_technologies',
  scheme: 'com.nextflytech.deerconnect',
  slug: 'mydeer-partner',
  version: '2',
  orientation: 'portrait',
  //   newArchEnabled: true,
  icon: './assets/icon.png',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  splash: {
    image: './assets/splash.png',
    resizeMode: 'contain',
    backgroundColor: '#2E3C4B',
  },
  updates: {
    url: 'https://u.expo.dev/0d682abe-2acf-43bb-bc44-555db00b25ef',
    fallbackToCacheTimeout: 0,
  },
  runtimeVersion: '0.0.3',
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.nextflytech.deerconnect',
    version: '0.0.3',
    runtimeVersion: '0.0.3',
    infoPlist: {
      NSCameraUsageDescription:
        'The app accesses your camera to let you take photos.',
      NSPhotoLibraryUsageDescription:
        'The app accesses your photos to let you share them.',
      NSPhotoLibraryAddUsageDescription:
        'The app accesses your photo library to save photos.',
      NSLocationWhenInUseUsageDescription: 'Allow $(PRODUCT_NAME) to use your location to provide you with a better experience.',
      ITSAppUsesNonExemptEncryption: false,
      UIDeviceFamily: [1],
      UIBackgroundModes: ["remote-notification"],
    },
    entitlements: {
      "aps-environment": "production", // ✅ Required for push notification, change to "production" for Testflight and App Store builds
      "com.apple.security.application-groups": [
        "group.com.nextflytech.deerconnect.onesignal" // // ✅ Required for confirmed delivery
      ]
    }
    // config: {
    //   googleMapsApiKey: "AIzaSyA8xcWnQtKcNfWlbST_AE0GOHAYs1mNC9w",
    // },
  },
  experiments: {
    typedRoutes: true,
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#2E3C4B',
    },
    package: 'com.nextflytech.deerconnect', //Env.PACKAGE,
    runtimeVersion: '0.0.3',
    versionCode: 1,
    permissions: [
      'CAMERA',
      'READ_EXTERNAL_STORAGE',
      'WRITE_EXTERNAL_STORAGE',
      'READ_MEDIA_IMAGES',
    ],
    config: {
      googleMaps: {
        apiKey: 'AIzaSyDU7HhmW9U0VikdE77EiHpVKK3ZhoV1EBU',
      },
    },
  },
  web: {
    favicon: './assets/favicon.png',
    bundler: 'metro',
  },
  plugins: [
    'expo-secure-store',
    [
      'expo-video',
      {
        supportsBackgroundPlayback: false,
        supportsPictureInPicture: false,
      },
    ],
    [
      'expo-font',
      {
        fonts: [
          './assets/fonts/Inter.ttf',
          './assets/fonts/AirbnbCereal_W_XBd.otf',
          './assets/fonts/AirbnbCereal_W_Md.otf',
          './assets/fonts/AirbnbCereal_W_Lt.otf',
          './assets/fonts/AirbnbCereal_W_Blk.otf',
          './assets/fonts/AirbnbCereal_W_Bk.otf',
          './assets/fonts/AirbnbCereal_W_Bd.otf',
        ],
      },
    ],
    'expo-localization',
    'expo-router',
    [
      'app-icon-badge',
      {
        enabled: Env.APP_ENV !== 'production',
        badges: [
          {
            text: Env.APP_ENV,
            type: 'banner',
            color: 'white',
          },
          {
            text: Env.VERSION.toString(),
            type: 'ribbon',
            color: 'white',
          },
        ],
      },
    ],
    [
      'expo-image-picker',
      {
        photosPermission: 'The app accesses your photos to let you share them.',
        cameraPermission:
          'The app accesses your camera to let you take photos.',
        microphonePermission:
          'The app accesses your microphone to let you record audio.',
      },
    ],
    [
      'expo-document-picker',
      {
        iCloudContainerEnvironment: 'Production',
      },
    ],
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
      },
    ],
    [
      'expo-video',
      {
        supportsBackgroundPlayback: false,
        supportsPictureInPicture: false,
      },
    ],
    [
      'react-native-edge-to-edge',
      {
        android: {
          parentTheme: 'Light',
          enforceNavigationBarContrast: false,
        },
      },
    ],
    [
      'expo-splash-screen',
      {
        backgroundColor: '#FFFFFF',
        image: './assets/splash-icon.png',
        // dark: {
        //   image: './assets/splash-icon.png',
        //   backgroundColor: '#000000',
        // },
        imageWidth: 150,
      },
    ],
    ['app-icon-badge', appIconBadgeConfig],
    [
      'onesignal-expo-plugin',
      {
        mode: 'development',
      },
    ],
    [
      'expo-build-properties',
      {
        android: {
          targetSdkVersion: 35,
          compileSdkVersion: 35,
        },
      },
    ],
    [
      "expo-web-browser"
    ],
  ],
  extra: {
    ...ClientEnv,
    eas: {
      projectId: '0d682abe-2acf-43bb-bc44-555db00b25ef', //Env.EAS_PROJECT_ID,
    },
    oneSignalAppId: '',
  },
});
