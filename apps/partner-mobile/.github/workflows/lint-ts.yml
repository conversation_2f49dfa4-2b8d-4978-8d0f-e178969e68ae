# 🔗 Links:
# Source file: https://github.com/obytes/react-native-template-obytes/blob/master/.github/workflows/lint-ts.yml

# ✍️ Description:
# This action is used to run eslint checks
# Runs on pull requests and pushes to  the main/master branches
# Based on the event type:
#   - If it's a pull request, it will run eslint, then add the check to the PR as well as annotate the code with the errors and warnings.
#   - If it's a push to main/master, it will run the type checking and fail if there are any errors.

# 🚨 GITHUB SECRETS REQUIRED: NONE

name: Lint TS (eslint, prettier)

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  lint:
    name: <PERSON>t TS (eslint, prettier)
    runs-on: ubuntu-latest

    steps:
      - name: 📦 Checkout project repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 📦 Setup Node + PNPM + install deps
        uses: ./.github/actions/setup-node-pnpm-install

      - name: 🏃‍♂️ Run ESLint PR
        if: github.event_name == 'pull_request'
        uses: reviewdog/action-eslint@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          reporter: github-pr-review
          eslint_flags: '. --ext .js,.jsx,.ts,.tsx'

      - name: 🏃‍♂️ Run ESLint PR
        if: github.event_name != 'pull_request'
        run: pnpm run lint
