const colors = require('./src/ui/colors');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      fontFamily: {
        inter: ['Inter'],
        airbnb_xbd: ['AirbnbW_XBd'],
        airbnb_md: ['AirbnbW_Md'],
        airbnb_lt: ['AirbnbW_Lt'],
        airbnb_blk: ['AirbnbW_Blk'],
        airbnb_bk: ['AirbnbW_Bk'],
        airbnb_bd: ['AirbnbW_Bd'],
      },
      colors: {
        // primary: {
        //   main: '#926C57', // Main shade
        //   500: '#120000', // Darkest ↑
        //   400: '#451F0A',
        //   300: '#5F3924',
        //   200: '#F15F3A',
        //   100: '#F4F0EE',
        //   50: '#FFFBF9', // Lightest ↓
        // },
        primary: {
          main600: '#F8C131', // Main shade
          900: '#120000', // Darkest ↑
          850: '#2C0000',
          800: '#5F2800',
          750: '#784100',
          700: '#C58E00',
          650: '#DEA717',
          600: '#F8C131',
          550: '#F9C746',
          500: '#F9CD5A',
          450: '#FAD46F',
          400: '#FBDA83',
          300: '#FDECC1',
          200: '#FEF3D6',
          100: '#FEF9EA',
          50: '#FFFBF9',
          0: '#F4F0EE',
          // Lightest ↓
        },
        // secondary: {
        //   main: '#45A58E', // Main shade
        //   600: '#C58E00', // Darkest ↑
        //   500: '#12725B',
        //   400: '#2B8B75',
        //   300: '#A2D2C7',
        //   200: '#C7E4DD',
        //   100: '#DAEDE8',
        //   50: '#F4FFFD', // Lightest ↓
        // },
        secondary: {
          main700: '#F04D24', // Main shade
          900: '#0A0000', // Darkest ↑
          850: '#3D0000',
          800: '#A30000',
          750: '#D6330A',
          650: '#F15F3A',
          600: '#F37150',
          550: '#F48266',
          500: '#F6947C',
          450: '#F9B8A7',
          300: '#FACABD',
          200: '#FCDBD3',
          100: '#FDEDE9', // Lightest ↓
        },
        // text: {
        //   main: '#1E1E1E', // Main shade
        //   600: '#252525', // Darkest ↑
        //   500: '#3A3A3A',
        //   400: '#ƒˇ525252',
        //   300: '#6B6B6B',
        //   200: '#E6E6E6',
        //   100: '#F1F1F1',
        //   50: '#FAFAFA', // Lightest ↓
        // },
        text: {
          main700: '#1E1E1E', // Main shade
          600: '#252525', // Darkest ↑
          550: '#3A3A3A',
          500: '#525252',
          400: '#6B6B6B',
          300: '#848484',
          200: '#B5B5B5',
          100: '#CECECE',
          50: '#E6E6E6', // Lightest ↓
        },
        ...colors,
      },
    },
  },
  plugins: [],
};
