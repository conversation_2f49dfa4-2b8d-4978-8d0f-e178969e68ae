import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "../trpc";

const reportedUsersFromChatRouter = createTRPCRouter({
  getReportedUsersFromChat: protectedProcedure.query(async ({ ctx }) => {
    try {
      const reportedUsers = await ctx.db.reportedUserFromChat.findMany({
        select: {
          id: true,
          reportingUserId: true,
          reportingUser: {
            select: {
              name: true,
            },
          },
          reportedUserId: true,
          reportedUser: {
            select: {
              name: true,
            },
          },
          reasonForReporting: true,
          reportingCustomerId: true,
          reportingCustomer: {
            select: {
              name: true,
            },
          },
        },
      });
      return reportedUsers;
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "UNAUTHORIZ<PERSON>",
        message: "Error fetching reported users from chat",
      });
    }
  }),
});

export default reportedUsersFromChatRouter;
