import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import addUpdateTestimonialSchema from "../validations/add-update-customer-validation";
import { TRPCError } from "@trpc/server";

export const customerTestimonialsRouter = createTRPCRouter({
  getTestimonials: protectedProcedure.query(async ({ ctx }) => {
    const testimonials = await ctx.db.customerTestimonials.findMany({
      orderBy: {
        createdAt: "desc",
      },
      include: {
        city: { select: { name: true } },
      },
    });
    return testimonials;
  }),

  getCustomerTestimonialById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return await ctx.db.customerTestimonials.findFirst({
        where: {
          id: input.id,
        },
      });
    }),

  addCustomerTestimonials: protectedProcedure
    .input(addUpdateTestimonialSchema)
    .mutation(async ({ ctx, input }) => {
      const { name, description, rating, fileKey, filePublicUrl, cityId } =
        input;
      try {
        await ctx.db.customerTestimonials.create({
          data: {
            name: name,
            description: description,
            fileKey: fileKey,
            filePublicUrl: filePublicUrl,
            rating: rating,
            cityId: cityId,
            createdAt: new Date(),
          },
        });
        return {
          message: "Customer Testimonials added successfully.",
        };
      } catch (err) {
        console.log("error is", err);
        throw new TRPCError({
          message: "Error in creating customer testimonials.",
          code: "BAD_REQUEST",
        });
      }
    }),

  updateCustomerTestimonials: protectedProcedure
    .input(addUpdateTestimonialSchema.extend({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { id, name, rating, description, fileKey, filePublicUrl, cityId } =
        input;
      try {
        await ctx.db.customerTestimonials.update({
          where: {
            id: id,
          },
          data: {
            name: name,
            description: description,
            fileKey: fileKey,
            filePublicUrl: filePublicUrl,
            rating: rating,
            cityId: cityId,
            updatedAt: new Date(),
          },
        });
        return {
          message: "Customer testimonials updated successfully.",
        };
      } catch (err) {
        console.log("error is", err);
        throw new TRPCError({
          message: "Error in upadting customer testimonials.",
          code: "BAD_REQUEST",
        });
      }
    }),

  deleteCustomerTestimonial: protectedProcedure
    .input(z.object({ testimonialId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      try {
        await ctx.db.customerTestimonials.delete({
          where: {
            id: input.testimonialId,
          },
        });
        return {
          message: "Customer Testimonial deleted successfully.",
        };
      } catch (err) {
        console.log("error is", err);
        throw new TRPCError({
          message: "error in deleting testimonials",
          code: "BAD_REQUEST",
        });
      }
    }),
});
