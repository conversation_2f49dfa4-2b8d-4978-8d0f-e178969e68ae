import { CustomerAgentConnectionChatStateEnum } from "@repo/database";
import { createTRPCRouter, protectedProcedure } from "../trpc";

const customerRouter = createTRPCRouter({
  getCustomers: protectedProcedure.query(({ ctx }) => {
    return ctx.db.customer.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        phoneNumber: true,
        createdAt: true,
        updatedAt: true,
        profileImagePublicUrl: true,
        locationAddress: true,
        connections: {
          where: {
            state: CustomerAgentConnectionChatStateEnum.ACCEPTED,
            deletedAt: null,
          },
          select: {
            id: true,
            agent: {
              select: {
                id: true,
                name: true,
                email: true,
                phoneNumber: true,
                filePublicUrl: true,
                rating: true,
                experience: true,
              },
            },
          },
          take: 1,
        },
      },
    });
  }),
});

export default customerRouter;
