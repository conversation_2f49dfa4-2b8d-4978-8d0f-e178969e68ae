"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/ui/select";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import AddUpdatePartnersSchema from "~/server/api/validations/add-update-partners.validation";
import { api } from "~/trpc/react";
import { skipToken } from "@tanstack/react-query";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { useEffect } from "react";
import { useSheet } from "~/app/hooks/use-sheet";
import { PartnerParamName } from "~/app/helpers/constants";

const PartnerAddUpdateForm = () => {
  const { paramValue: partnerId, closeSheet } = useSheet(PartnerParamName);

  const { data: cities, isSuccess: citiesLoaded } =
    api.partner.getCities.useQuery();

  const {
    data: partnerDetail,
    isLoading,
    error,
  } = api.partner.getPartnerById.useQuery(
    partnerId ? { id: partnerId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  );

  const { mutate: updatePartner, isPending: isPendingUpdateMutation } =
    api.partner.updatePartner.useMutation();
  const { mutate: createPartner, isPending: isPendingCreateMutation } =
    api.partner.createPartner.useMutation();
  const trpcUtils = api.useUtils();

  const form = useForm<z.infer<typeof AddUpdatePartnersSchema>>({
    resolver: zodResolver(AddUpdatePartnersSchema),
    defaultValues: partnerDetail
      ? {
          name: partnerDetail.name,
          email: partnerDetail.email,
          phoneNumber: partnerDetail.phoneNumber,
          pancardNumber: partnerDetail.pancardNumber,
          adharcardNumber: partnerDetail.adharcardNumber ?? undefined,
          cityId: partnerDetail.cityId ?? undefined,
          reraNumber: partnerDetail.reraNumber ?? undefined,
          gstNumber: partnerDetail.gstNumber ?? undefined,
          referredBy: partnerDetail.referredByUserId ?? undefined,
        }
      : {
          name: "",
          email: "",
          phoneNumber: "",
          pancardNumber: "",
          adharcardNumber: undefined,
          cityId: undefined,
          reraNumber: undefined,
          gstNumber: undefined,
          referredBy: undefined,
        },
  });

  const adharcardField = form.watch("adharcardNumber");

  useEffect(() => {
    if (adharcardField === "") {
      form.setValue("adharcardNumber", undefined);
    }
  }, [adharcardField, form]);

  const onSubmit = (values: z.infer<typeof AddUpdatePartnersSchema>) => {
    if (partnerId) {
      updatePartner(
        { ...values, id: partnerId },
        {
          onSuccess: (opts) => {
            toast.success(opts.messageTitle);
            void trpcUtils.partner.getPartnerById.invalidate({ id: partnerId });
            void trpcUtils.partner.getPartners.invalidate();
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      createPartner(values, {
        onSuccess: (opts) => {
          toast.success(opts.messageTitle);
          void trpcUtils.partner.getPartners.invalidate();
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && partnerId) {
    return <div className="p-4 text-center">Loading partner details...</div>;
  }

  if (error && partnerId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading partner: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form
        className="flex flex-col gap-3 md:gap-4 xl:gap-6"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium lg:text-lg">Name</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter partner name"
                  id="name"
                  className="text-text-600"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Phone Number and Email */}
        <div className="gap-4 md:flex md:items-center lg:flex-col xl:flex-row">
          <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium lg:text-lg">
                    Phone Number
                  </FormLabel>
                  <FormControl className="w-full">
                    <Input
                      type="text"
                      placeholder="Enter phone number"
                      id="phoneNumber"
                      className="text-text-600"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium lg:text-lg">
                    Email
                  </FormLabel>
                  <FormControl className="w-full">
                    <Input
                      type="text"
                      id="email"
                      className="text-text-600"
                      placeholder="Enter email address"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Pancard Number and Adhar Card */}
        <div className="gap-4 md:flex md:items-center lg:flex-col xl:flex-row">
          {(!partnerId || partnerDetail?.pancardNumber === "") && (
            <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
              <FormField
                control={form.control}
                name="pancardNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium lg:text-lg">
                      Pancard Number*
                    </FormLabel>
                    <FormControl className="w-full">
                      <Input
                        type="text"
                        id="pancardNumber"
                        className="text-text-600"
                        placeholder="Enter PAN number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}
          {(!partnerId || !partnerDetail?.adharcardNumber) && (
            <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
              <FormField
                control={form.control}
                name="adharcardNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium lg:text-lg">
                      Adhar Card Number (optional)
                    </FormLabel>
                    <FormControl className="lg:w-full">
                      <Input
                        type="text"
                        className="text-text-600"
                        placeholder="Enter Aadhar number (optional)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}
        </div>

        <div className="gap-4 md:flex md:items-center lg:flex-col xl:flex-row">
          <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
            <FormField
              control={form.control}
              name="cityId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium lg:text-lg">City</FormLabel>
                  <FormControl className="w-full">
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your city" />
                      </SelectTrigger>
                      <SelectContent>
                        {citiesLoaded && cities.length > 0 ? (
                          cities.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              {item.name}
                            </SelectItem>
                          ))
                        ) : (
                          <p className="p-2">No cities available</p>
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
            {/* RERA Number */}
            <div className="flex flex-col gap-2">
              <FormField
                control={form.control}
                name="reraNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium lg:text-lg">
                      RERA Number{" "}
                      <span className="font-airbnb_w_lt text-sm text-text-400">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl className="w-full">
                      <Input
                        type="text"
                        className="text-text-600"
                        placeholder="89DEG560TR"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>
        {/* GST Number */}
        <div className="gap-4 md:flex md:items-center lg:flex-col xl:flex-row">
          <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
            <div className="flex flex-col gap-2">
              <FormField
                control={form.control}
                name="gstNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium lg:text-lg">
                      GST Number{" "}
                      <span className="font-airbnb_w_lt text-sm text-text-400">
                        (optional)
                      </span>
                    </FormLabel>
                    <FormControl className="w-full">
                      <Input
                        type="text"
                        className="text-text-600"
                        placeholder="Enter GST number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
            {/* referred by */}
            {!partnerDetail?.referredByUserId && (
              <div className="flex flex-col gap-2">
                <FormField
                  control={form.control}
                  name="referredBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium lg:text-lg">
                        Referral Code{" "}
                        <span className="font-airbnb_w_lt text-sm text-text-400">
                          (optional)
                        </span>
                      </FormLabel>
                      <FormControl className="w-full">
                        <Input
                          type="text"
                          className="text-text-600"
                          placeholder="Enter referral code"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
          </div>
        </div>

        {partnerId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          partnerId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!partnerId && isPendingCreateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !partnerId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default PartnerAddUpdateForm;
