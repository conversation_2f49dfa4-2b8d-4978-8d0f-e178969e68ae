"use client";
import Link from "next/link";
import HeadingBadge from "../components/shared/heading-badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@repo/ui/components/ui/accordion";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { z } from "zod";
import { deleteAccountFormSchema } from "@repo/validators";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/ui/input";
import { Button } from "@repo/ui/components/ui/button";
import { Textarea } from "@repo/ui/components/ui/textarea";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

const DeleteAccount = () => {
  const { data: faqs } = api.faq.getAllFaq.useQuery({
    project: "B2C_MY_DEER",
    page: "DELETE_ACCOUNT_PAGE",
  });
  const { executeRecaptcha } = useGoogleReCaptcha();
  const { mutate: verifyCaptcha } = api.user.verifyCaptcha.useMutation();
  const deleteAccount =
    api.deleteCustomerAccount.createDeleteCustomerAccount.useMutation();
  const form = useForm<z.infer<typeof deleteAccountFormSchema>>({
    resolver: zodResolver(deleteAccountFormSchema),
    defaultValues: {
      email: "",
      phoneNumber: "",
      reason: "",
    },
  });
  const onSubmit = async (values: z.infer<typeof deleteAccountFormSchema>) => {
    console.log("form values are", values);
    if (!executeRecaptcha) {
      console.log("not available to execute recaptcha");
      return;
    }

    const gRecaptchaToken = await executeRecaptcha();
    verifyCaptcha(
      { token: gRecaptchaToken },
      {
        onSuccess: (opts) => {
          if (!opts.success) {
            toast.warning("failed to verify human");
            return;
          }
          deleteAccount
            .mutateAsync({ ...values })
            .then((resp) => {
              if (resp.message) {
                console.log("success is", resp.message);
                toast.success(resp.message);
                form.reset();
              }
              if (resp.error) {
                console.log("error is", resp.error);
                toast.error(resp.error);
              }
            })
            .catch((err) => {
              console.log("error is", err);
            });
        },
      },
    );
  };
  return (
    <>
      <div className="container max-w-full bg-[#FFF6F4]">
        <div className="flex flex-col items-center gap-2.5 py-9 md:gap-3 md:py-[50px] lg:gap-4 lg:py-[55px] xl:py-[65px] 2xl:gap-5 2xl:py-[70px]">
          <Link
            href="/"
            className="font-airbnb_w_md text-sm font-medium text-primary-2-750 lg:text-base 2xl:text-lg"
          >
            /Back to home page
          </Link>
          <div className="flex flex-col items-center gap-1 md:gap-1.5 lg:gap-2 2xl:gap-3">
            <div className="text-center font-airbnb_w_xbd text-3xl font-extrabold text-secondary-2-700 md:text-4xl lg:text-[44px] lg:leading-[56px] xl:text-[56px] xl:leading-[69px] 2xl:text-6xl 2xl:leading-[80px]">
              Delete Account Request{" "}
            </div>
            <p className="text-center font-airbnb_w_bk text-base font-normal text-[#1A1A1A] md:text-lg lg:text-xl 2xl:text-2xl">
              To reactive/deactivate or to delete your account at deer connect,
              please get in touch with us on our Toll-Free number{" "}
              <a
                href="tel:+************"
                className="text-secondary-2-700 underline"
              >
                +************
              </a>{" "}
              or email us on{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-secondary-2-700 underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>

      <div className="container max-w-full">
        <div className="mx-auto py-[26px] md:py-10 lg:py-[50px] 2xl:py-[70px]">
          <div className="">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="mx-auto 2xl:w-4/5"
              >
                <div className="flex flex-col gap-[18px] lg:gap-8 2xl:gap-10">
                  <div className="flex flex-col gap-[18px] md:grow md:flex-row lg:gap-8 2xl:gap-10">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem className="md:w-1/2">
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter your email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem className="md:w-1/2">
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input placeholder="eg.893 4232 323" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="reason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reason</FormLabel>
                        <FormControl>
                          <Textarea
                            className="bg-white"
                            placeholder="Write the reason for account deletion"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex justify-center">
                  <Button
                    type="submit"
                    className="mt-5 w-1/2 md:mt-6 lg:mt-10 xl:mt-[42px] 2xl:mt-[65px]"
                  >
                    Submit
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
        {faqs && faqs.length > 0 && (
          <div className="space-y-5 py-9 md:space-y-6 md:py-10 lg:py-[50px] xl:space-y-[30px] xl:py-[65px] 2xl:space-y-[60px] 2xl:py-[70px]">
            <div className="flex flex-col items-center gap-1 md:gap-2 2xl:gap-3">
              <HeadingBadge
                content="FAQ"
                className="rounded px-2.5 py-1 md:rounded-md xl:rounded-lg xl:px-4 xl:py-1.5"
              />
              <h1 className="text-center font-airbnb_w_bd text-xl font-bold xl:text-2xl 2xl:text-5xl">
                You Have Question. We’ve Your Answer
              </h1>
            </div>

            <Accordion
              type="single"
              className="space-y-4 md:space-y-[18px] xl:space-y-6 2xl:space-y-8"
              collapsible
            >
              {faqs.map((item, idx) => (
                <AccordionItem
                  key={idx}
                  value={`item-${idx}`}
                  className="flex flex-col gap-2.5 rounded-xl border border-text-500 border-opacity-30 p-4 data-[state=open]:border-[#5f3924] data-[state=open]:border-opacity-30 data-[state=open]:bg-[#FFFCF4] md:gap-[14px] md:p-5 xl:gap-5 xl:p-8"
                >
                  <AccordionTrigger className="py-0 text-start font-airbnb_w_md text-base font-medium text-text-550 data-[state=open]:text-primary-2-750 xl:text-lg 2xl:text-xl">
                    {item.question}
                  </AccordionTrigger>
                  <AccordionContent className="pb-0 font-airbnb_w_bk text-sm font-normal text-text-550 xl:text-base 2xl:text-lg">
                    {item.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        )}
      </div>
    </>
  );
};

export default DeleteAccount;
